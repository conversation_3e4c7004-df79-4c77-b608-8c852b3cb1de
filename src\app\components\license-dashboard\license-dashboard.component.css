/* license-dashboard.component.css */
.card {
    border-radius: 8px;
    box-shadow: 0 4px 6px var(--shadow-color); /* Değişken kullanıldı */
    margin-bottom: 20px;
    background-color: var(--card-bg-color); /* Arka plan rengi eklendi */
    color: var(--text-color); /* Metin rengi eklendi */
    border: 1px solid var(--border-color); /* Kenarlık eklendi */
  }
  
  .card-header {
    border-bottom: 1px solid var(--border-color); /* Değişken kullanıldı */
    background-color: var(--card-bg-color); /* Değişken kullanıldı */
    color: var(--text-color); /* Metin rengi eklendi */
    font-weight: 500;
    padding: 0.75rem 1.25rem; /* Padding eklendi (Bootstrap benzeri) */
  }
  
  .btn-block {
    width: 100%;
    margin-bottom: 10px;
  }
  
  /* .table th stilleri global stiller tarafından kapsandığı için kaldırıldı */
  
  .bg-primary, .bg-warning, .bg-success, .bg-info {
    position: relative;
    overflow: hidden;
  }
  
  .opacity-50 {
    opacity: 0.5;
  }
  
  /* license-packages-list.component.css */
  /* .mat-mdc-row:hover stili global stiller tarafından kapsandığı için kaldırıldı */
  
  /* .card stili yukarıda zaten güncellendi, bu tekrarı kaldırabiliriz. */
  
  .table-responsive {
    overflow-x: auto;
  }
  
  /* license-package-add-edit.component.css */
  mat-form-field {
    width: 100%;
    margin-bottom: 15px;
  }
  
  .spinner {
    margin: 0 auto;
    width: 70px;
    text-align: center;
  }
  
  .spinner > div {
    width: 12px;
    height: 12px;
    background-color: var(--text-color); /* Spinner rengi için değişken */
    border-radius: 100%;
    display: inline-block;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  }
  
  .spinner .bounce1 {
    animation-delay: -0.32s;
  }
  
  .spinner .bounce2 {
    animation-delay: -0.16s;
  }
  
  @keyframes sk-bouncedelay {
    0%, 80%, 100% { 
      transform: scale(0);
    } 40% { 
      transform: scale(1.0);
    }
  }
  
  /* user-licenses-list.component.css */
  /* .text-danger, .text-warning, .text-success renk tanımları kaldırıldı. */
  /* Global değişken tanımlarına ve potansiyel global utility sınıflarına güveniliyor. */
  
  .actions-column {
    width: 120px;
  }
  
  /* license-purchase.component.css */
  .user-info {
    background-color: var(--card-bg-color); /* Değişken kullanıldı */
    color: var(--text-color); /* Metin rengi eklendi */
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color); /* Kenarlık eklendi */
  }
  
  .user-info h4 {
    margin-top: 0;
    margin-bottom: 5px;
  }
  
  /* license-transactions.component.css */
  .filter-section {
    padding: 15px;
    background-color: var(--card-bg-color); /* Değişken kullanıldı */
    color: var(--text-color); /* Metin rengi eklendi */
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color); /* Kenarlık eklendi */
  }
  
  .filter-button {
    margin-top: 32px;
  }