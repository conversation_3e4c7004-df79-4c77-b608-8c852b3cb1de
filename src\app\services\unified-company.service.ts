import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';

export interface UnifiedCompanyAddDto {
  // Kullanıcı bilgileri (Adım 1)
  userFirstName: string;
  userLastName: string;
  userEmail: string;
  userPassword: string;

  // Şirket bilgileri (Adım 2)
  companyName: string;
  companyPhone: string;

  // Adres bilgileri (Adım 3)
  cityID: number;
  townID: number;
  address: string;

  // Salon sahibi bilgileri (Adım 4)
  ownerFullName: string;
  ownerPhone: string;
}

@Injectable({
  providedIn: 'root'
})
export class UnifiedCompanyService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  addUnifiedCompany(unifiedCompanyDto: UnifiedCompanyAddDto): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'unifiedcompany/add',
      unifiedCompanyDto
    );
  }
}
