import { Component, OnInit, Inject, PLATFORM_ID, Renderer2, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { MemberDetailWithHistory } from '../../models/member-detail-with-history.model';
import { MemberService } from '../../services/member.service';
import { ToastrService } from 'ngx-toastr';
import { isPlatformBrowser } from '@angular/common';
import { Subscription } from 'rxjs';
import { FileUploadService } from '../../services/file-upload.service';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-member-detail-dialog',
  templateUrl: './member-detail-dialog.component.html',
  styleUrls: ['./member-detail-dialog.component.css'],
  standalone: false
})
export class MemberDetailDialogComponent implements OnInit, OnDestroy {
  isBrowser: boolean;
  memberDetail: MemberDetailWithHistory | null = null;
  isLoading = true;
  activeTab = 'personal'; // 'personal', 'memberships', 'payments', 'entries', 'freezes'
  memberId: number;
  profileImageUrl: string | null = null;

  // Theme change observer
  private themeChangeObserver: MutationObserver | null = null;

  constructor(
    public dialogRef: MatDialogRef<MemberDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { memberId: number },
    private memberService: MemberService,
    private toastrService: ToastrService,
    private renderer: Renderer2,
    private fileUploadService: FileUploadService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.memberId = data.memberId;
  }

  ngOnInit(): void {
    // memberId zaten constructor'da data'dan alındı
    this.loadMemberDetail();

    // Set up theme change observer
    if (this.isBrowser) {
      this.setupThemeChangeObserver();
    }
  }

  ngOnDestroy(): void {
    // Clean up the observer when component is destroyed
    if (this.themeChangeObserver) {
      this.themeChangeObserver.disconnect();
    }
  }

  // Set up an observer to detect theme changes on the document element
  private setupThemeChangeObserver(): void {
    if (typeof MutationObserver !== 'undefined') {
      this.themeChangeObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
            // Theme has changed, update component if needed
            console.log('Theme changed in document');
          }
        });
      });

      // Start observing the document element for data-theme attribute changes
      this.themeChangeObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
      });
    }
  }

  loadMemberDetail(): void {
    this.isLoading = true;
    this.memberService.getMemberDetailById(this.memberId).subscribe({
      next: (response) => {
        if (response.success) {
          this.memberDetail = response.data;

          // Profil fotoğrafını yükle
          this.loadProfileImage();

          // Verileri tarihsel olarak sırala (yeni tarihten eskiye)
          if (this.memberDetail) {
            // Üyelikleri sırala
            if (this.memberDetail.memberships && this.memberDetail.memberships.length > 0) {
              this.memberDetail.memberships.sort((a, b) => {
                const dateA = a.startDate ? new Date(a.startDate).getTime() : 0;
                const dateB = b.startDate ? new Date(b.startDate).getTime() : 0;
                return dateB - dateA;
              });
            }

            // Ödemeleri sırala
            if (this.memberDetail.payments && this.memberDetail.payments.length > 0) {
              this.memberDetail.payments.sort((a, b) => {
                const dateA = a.paymentDate ? new Date(a.paymentDate).getTime() : 0;
                const dateB = b.paymentDate ? new Date(b.paymentDate).getTime() : 0;
                return dateB - dateA;
              });
            }

            // Giriş/çıkışları sırala
            if (this.memberDetail.entryExitHistory && this.memberDetail.entryExitHistory.length > 0) {
              this.memberDetail.entryExitHistory.sort((a, b) => {
                const dateA = a.entryDate ? new Date(a.entryDate).getTime() : 0;
                const dateB = b.entryDate ? new Date(b.entryDate).getTime() : 0;
                return dateB - dateA;
              });
            }

            // Dondurma geçmişini sırala
            if (this.memberDetail.freezeHistory && this.memberDetail.freezeHistory.length > 0) {
              this.memberDetail.freezeHistory.sort((a, b) => {
                const dateA = a.startDate ? new Date(a.startDate).getTime() : 0;
                const dateB = b.startDate ? new Date(b.startDate).getTime() : 0;
                return dateB - dateA;
              });
            }
          }
        } else {
          this.toastrService.error('Üye detayları yüklenirken bir hata oluştu.', 'Hata');
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching member details:', error);
        this.toastrService.error('Üye detayları yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  loadProfileImage(): void {
    if (this.memberDetail?.userID) {
      this.profileImageUrl = this.fileUploadService.getProfileImageUrl(this.memberDetail.userID);
    } else {
      this.profileImageUrl = null;
    }
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  closeDialog(): void {
    this.dialogRef.close(); // Close the dialog
  }

  // Cinsiyet bilgisini metin olarak döndürür
  getGenderText(gender: number): string {
    return gender === 1 ? 'Erkek' : 'Kadın';
  }

  // Cinsiyet bilgisine göre avatar rengi döndürür
  getGenderColor(gender: number): string {
    return gender === 1 ? '#4361ee' : '#FF69B4'; // Erkek: Mavi, Kadın: Pembe (Member component ile aynı)
  }

  // Üyelik durumunu metin olarak döndürür
  getMembershipStatusText(isActive: boolean): string {
    return isActive ? 'Aktif' : 'Pasif';
  }

  // Üyelik durumunu CSS sınıfı olarak döndürür
  getMembershipStatusClass(isActive: boolean): string {
    return isActive ? 'status-active' : 'status-expired';
  }

  // Ödeme durumunu metin olarak döndürür
  getPaymentStatusText(status: string): string {
    if (status === 'Completed') return 'Ödendi';
    if (status === 'Pending') return 'Ödenmemiş';
    return status; // Eğer başka bir değer varsa olduğu gibi döndür
  }

  // Ödeme durumunu CSS sınıfı olarak döndürür
  getPaymentStatusClass(status: string): string {
    if (status === 'Completed' || status === 'Ödendi') return 'status-active';
    if (status === 'Pending' || status === 'Ödenmemiş') return 'status-expired';
    return '';
  }

  // Dondurma durumunu metin olarak döndürür
  getFreezeStatusText(isFrozen: boolean): string {
    return isFrozen ? 'Dondurulmuş' : 'Normal';
  }

  // Dondurma durumunu CSS sınıfı olarak döndürür
  getFreezeStatusClass(isFrozen: boolean): string {
    return isFrozen ? 'status-frozen' : '';
  }

  // Çıkış yapılmadı kontrolü (saat 5'te otomatik çıkış yapılanlar için)
  isAutoExitAt5AM(exitDate: Date | undefined | null): boolean {
    if (!exitDate) return false;

    const date = new Date(exitDate);
    return date.getHours() === 5 && date.getMinutes() === 0;
  }
}
