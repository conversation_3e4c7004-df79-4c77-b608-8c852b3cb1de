/* Workout Program Detail Specific Styles */

.program-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.info-item {
  background: var(--bg-secondary);
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) var(--transition-timing);
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.info-item.description {
  grid-column: 1 / -1;
}

.info-header {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-content {
  font-size: 1.05rem;
  color: var(--text-primary);
}

/* Badge Styles */
.program-badge,
.modern-badge {
  display: inline-block;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
}

.program-badge:hover,
.modern-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-xs);
}

.program-badge-primary,
.modern-badge-primary {
  background: var(--primary-light);
  color: var(--primary);
}

.program-badge-warning,
.modern-badge-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.program-badge-success,
.modern-badge-success {
  background: var(--success-light);
  color: var(--success);
}

.program-badge-danger,
.modern-badge-danger {
  background: var(--danger-light);
  color: var(--danger);
}

.program-badge-info,
.modern-badge-info {
  background: var(--info-light);
  color: var(--info);
}

.program-badge-secondary,
.modern-badge-secondary {
  background: var(--secondary-light);
  color: var(--secondary);
}

.program-description {
  line-height: 1.6;
  margin: 0;
  color: var(--text-primary);
}


.day-detail-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-primary);
  transition: all var(--transition-speed) var(--transition-timing);
  height: 100%;
}

.day-detail-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.day-detail-header {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.day-detail-body {
  padding: 1.25rem;
}

.day-number {
  font-weight: 600;
  color: var(--primary);
}

.day-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-left: 0.5rem;
}

.exercise-list {
  max-height: 300px;
  overflow-y: auto;
}

.exercise-item {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-speed) var(--transition-timing);
}

.exercise-item:last-child {
  border-bottom: none;
}

.exercise-item:hover {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
}

.exercise-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.exercise-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.25rem;
}

.exercise-sets,
.exercise-reps,
.exercise-rest {
  font-size: 0.875rem;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.exercise-notes {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid var(--primary);
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Badge Styles */
.modern-badge-secondary {
  background-color: var(--secondary-light);
  color: var(--secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Removed stats-grid styles as stats section is removed */
  
  .day-detail-header {
    padding: 0.75rem 1rem;
  }
  
  .day-detail-body {
    padding: 1rem;
  }
  
  .exercise-details {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .exercise-sets,
  .exercise-reps,
  .exercise-rest {
    align-self: flex-start;
  }
}

/* Dark mode specific adjustments */
[data-theme="dark"] .program-description {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

/* Removed dark mode styles for stat-item as stats section is removed */

[data-theme="dark"] .day-detail-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .day-detail-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-item:hover {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .exercise-sets,
[data-theme="dark"] .exercise-reps,
[data-theme="dark"] .exercise-rest {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-notes {
  background-color: var(--bg-tertiary);
  border-left-color: var(--primary);
}
