/* Product Sale Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content Blur */
.content-blur {
  filter: blur(2px);
  pointer-events: none;
}

/* Form Section */
.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.section-title {
  margin-bottom: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  display: flex;
  align-items: center;
  font-size: 1rem;
}

/* Modern Form Group */
.modern-form-group {
  margin-bottom: var(--spacing-md);
  position: relative;
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.modern-form-label.required::after {
  content: ' *';
  color: var(--danger);
}

/* Input Group Styles */
.input-group {
  display: flex;
  width: 100%;
}

.input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
  border-right: none;
}

.modern-form-control {
  flex: 1;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  transition: border-color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
}

.modern-form-control:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Cart Styles */
.h-100 {
  height: 100% !important;
}

.empty-cart-container {
  min-height: 300px;
}

.empty-cart-icon {
  opacity: 0.6;
}

.cart-items {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.cart-items::-webkit-scrollbar {
  width: 6px;
}

.cart-items::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 3px;
}

.cart-items::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 3px;
}

.cart-items::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

.modern-card-item {
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.modern-card-item:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.item-total {
  font-size: 1.1rem;
}

.cart-summary {
  border: 1px solid var(--border-color);
}

.cart-footer {
  margin-top: auto;
}

/* Badge Styles */
.modern-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Zoom In Animation */
.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Dark Mode Support */
[data-theme="dark"] .form-section {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .form-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .modern-card-item {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: var(--border-color);
}

[data-theme="dark"] .modern-card-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .cart-summary {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: var(--border-color);
}

[data-theme="dark"] .input-group-text {
  background-color: var(--primary-light);
  border-color: var(--border-color);
  color: var(--primary);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .cart-items {
    max-height: 250px;
  }

  .empty-cart-container {
    min-height: 200px;
  }
}

@media (max-width: 767.98px) {
  .modern-card-item {
    padding: 0.75rem;
  }

  .cart-items {
    max-height: 200px;
  }
}