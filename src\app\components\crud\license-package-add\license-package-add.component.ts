import { Component, OnInit, Output, EventEmitter, ViewChild, Optional, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../../services/license-package.service';
import { OperationClaimService } from '../../../services/operation-claim.service';
import { LicensePackage } from '../../../models/licensePackage';
import { OperationClaim } from '../../../models/operationClaim';
import { LicensePackagesListComponent } from '../../license-packages-list/license-packages-list.component';

@Component({
  selector: 'app-license-package-add',
  templateUrl: './license-package-add.component.html',
  styleUrls: ['./license-package-add.component.css'],
  standalone: false
})
export class LicensePackageAddComponent implements OnInit {
  licensePackageAddForm: FormGroup;
  isSubmitting = false;
  roles: OperationClaim[] = [];

  @Output() licensePackageAdded = new EventEmitter<void>();
  @ViewChild('licensePackageList') licensePackageList: LicensePackagesListComponent;

  constructor(
    private formBuilder: FormBuilder,
    private licensePackageService: LicensePackageService,
    private operationClaimService: OperationClaimService,
    private toastrService: ToastrService,
    @Optional() private dialogRef: MatDialogRef<LicensePackageAddComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    this.createLicensePackageAddForm();
    this.loadRoles();
  }

  createLicensePackageAddForm() {
    this.licensePackageAddForm = this.formBuilder.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      role: ['', Validators.required],
      year: ['0', Validators.required],
      month: ['1', Validators.required],
      day: ['0', Validators.required],
      price: ['', [Validators.required, Validators.min(0)]]
    });
  }

  loadRoles() {
    this.operationClaimService.getAll().subscribe({
      next: (response) => {
        this.roles = response.data.filter(role => role.isActive);
        // İlk aktif rolü seç
        if (this.roles.length > 0) {
          this.licensePackageAddForm.patchValue({
            role: this.roles[0].name
          });
        }
      },
      error: (error) => {
        this.toastrService.error('Roller yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  add() {
    if (this.licensePackageAddForm.valid) {
      this.isSubmitting = true;
      let licensePackageModel = Object.assign(
        {},
        this.licensePackageAddForm.value
      );

      // Calculate total days from year, month, day
      const totalDays =
        parseInt(licensePackageModel.year) * 365 +
        parseInt(licensePackageModel.month) * 30 +
        parseInt(licensePackageModel.day);

      licensePackageModel.durationDays = totalDays;

      // Remove year, month, day from the model
      delete licensePackageModel.year;
      delete licensePackageModel.month;
      delete licensePackageModel.day;

      // Set creation date and active status
      licensePackageModel.creationDate = new Date();
      licensePackageModel.isActive = true;

      this.licensePackageService.add(licensePackageModel).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.toastrService.success(response.message, 'Başarılı');

          // Dialog olarak kullanılıyorsa dialog'u kapat
          if (this.dialogRef) {
            this.dialogRef.close(true);
          } else {
            // Normal component olarak kullanılıyorsa form'u resetle ve listeyi güncelle
            this.resetForm();
            this.licensePackageAdded.emit();
            // Lisans paketleri listesini güncelle
            if (this.licensePackageList) {
              this.licensePackageList.loadLicensePackages();
            }
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          this.toastrService.error('Lisans paketi eklenirken bir hata oluştu', 'Hata');
        }
      });
    } else {
      this.toastrService.error('Lütfen tüm alanları doğru şekilde doldurun', 'Hata');
    }
  }

  resetForm() {
    this.licensePackageAddForm.reset({
      name: '',
      description: '',
      role: this.roles.length > 0 ? this.roles[0].name : '',
      year: '0',
      month: '1',
      day: '0',
      price: ''
    });
  }
}
