export interface DeletedCompanyUser {
  companyUserID: number;
  name: string;
  email: string;
  phoneNumber: string;
  cityName: string;
  townName: string;
  deletedDate?: Date;
  
  // Company bilgileri
  companyID?: number;
  companyName?: string;
  companyPhone?: string;
  companyAddress?: string;
  
  // User bilgileri
  userID?: number;
  
  // İstatistik bilgileri
  totalMembers: number;
  canRestore: boolean;
}
