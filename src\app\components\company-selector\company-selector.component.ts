import { Component, OnInit } from '@angular/core';
import { CompanyContextService, Company } from '../../services/company-context.service';

@Component({
  selector: 'app-company-selector',
  templateUrl: './company-selector.component.html',
  styleUrls: ['./company-selector.component.css'],
  standalone:false
})
export class CompanySelectorComponent implements OnInit {
  companies: Company[] = [];
  filteredCompanies: Company[] = [];
  selectedCompanyId: number = -1;
  isLoading: boolean = false;
  searchTerm: string = '';

  constructor(private companyContextService: CompanyContextService) { }

  ngOnInit(): void {
    // Şirketleri yükle
    this.companyContextService.companies.subscribe(companies => {
      this.companies = companies;
      this.filteredCompanies = [...this.companies]; // Initialize filtered companies
    });

    // Seçili şirketi al
    this.companyContextService.companyId.subscribe(companyId => {
      this.selectedCompanyId = companyId;
    });
  }

  // Şirketleri filtrele
  filterCompanies(): void {
    if (!this.searchTerm.trim()) {
      this.filteredCompanies = [...this.companies];
    } else {
      const term = this.searchTerm.toLowerCase().trim();
      this.filteredCompanies = this.companies.filter(company => 
        company.name.toLowerCase().includes(term)
      );
    }
  }

  // Şirket seç
  selectCompany(companyId: number): void {
    this.selectedCompanyId = companyId;
  }

  // Şirket değiştirme işlemi
  onCompanyChange(companyId: number): void {
    if (companyId !== this.selectedCompanyId) {
      this.isLoading = true;
      this.companyContextService.setActiveCompany(companyId);
      
      // Not: setActiveCompany metodu başarılı olduğunda sayfayı yenilediği için
      // isLoading'i false yapmaya gerek yok, sayfa zaten yenilenecek
    }
  }

  // Şirket adını getir
  getCompanyName(companyId: number): string {
    return this.companyContextService.getCompanyName(companyId);
  }
}
