import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';

export interface ExerciseCategory {
  exerciseCategoryID: number;
  categoryName: string;
  description?: string;
  isActive?: boolean;
  creationDate?: Date;
}

export interface ExerciseCategoryAdd {
  categoryName: string;
  description?: string;
}

export interface ExerciseCategoryUpdate {
  exerciseCategoryID: number;
  categoryName: string;
  description?: string;
  isActive?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ExerciseCategoryService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Tüm egzersiz kategorilerini getirir
   */
  getAllCategories(): Observable<ListResponseModel<ExerciseCategory>> {
    return this.httpClient.get<ListResponseModel<ExerciseCategory>>(
      `${this.apiUrl}exercisecategories/getall`
    );
  }

  /**
   * Aktif egzersiz kategorilerini getirir
   */
  getActiveCategories(): Observable<ListResponseModel<ExerciseCategory>> {
    return this.httpClient.get<ListResponseModel<ExerciseCategory>>(
      `${this.apiUrl}exercisecategories/getactive`
    );
  }

  /**
   * ID'ye göre egzersiz kategorisi getirir
   */
  getById(categoryId: number): Observable<SingleResponseModel<ExerciseCategory>> {
    return this.httpClient.get<SingleResponseModel<ExerciseCategory>>(
      `${this.apiUrl}exercisecategories/getbyid/${categoryId}`
    );
  }

  /**
   * Yeni egzersiz kategorisi ekler (Sadece owner)
   */
  add(category: ExerciseCategoryAdd): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}exercisecategories/add`,
      category
    );
  }

  /**
   * Egzersiz kategorisini günceller (Sadece owner)
   */
  update(category: ExerciseCategoryUpdate): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}exercisecategories/update`,
      category
    );
  }

  /**
   * Egzersiz kategorisini siler (Sadece owner)
   */
  delete(categoryId: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}exercisecategories/delete/${categoryId}`
    );
  }
}
