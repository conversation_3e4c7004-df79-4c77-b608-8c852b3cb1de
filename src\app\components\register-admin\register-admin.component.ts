import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../../services/auth.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-register-admin',
  templateUrl: './register-admin.component.html',
  styleUrls: ['./register-admin.component.css'],
  standalone: false
})
export class RegisterAdminComponent implements OnInit {
  registerForm!: FormGroup;
  isLoading = false;
  passwordVisible = false;
  confirmPasswordVisible = false;
  isBanned = false;
  remainingBanTime = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.createRegisterForm();
    this.checkRegisterBanStatus();
  }

  createRegisterForm() {
    this.registerForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { mismatch: true };
    }
    return null;
  }

  checkRegisterBanStatus() {
    // Rate limiting kontrolü - mevcut register component'inden kopyalandı
    const banEndTime = localStorage.getItem('registerBanEndTime');
    if (banEndTime) {
      const banEnd = new Date(banEndTime);
      const now = new Date();
      
      if (now < banEnd) {
        this.isBanned = true;
        this.updateRemainingTime(banEnd);
        
        const interval = setInterval(() => {
          const currentTime = new Date();
          if (currentTime >= banEnd) {
            this.isBanned = false;
            localStorage.removeItem('registerBanEndTime');
            clearInterval(interval);
          } else {
            this.updateRemainingTime(banEnd);
          }
        }, 1000);
      } else {
        localStorage.removeItem('registerBanEndTime');
      }
    }
  }

  updateRemainingTime(banEndTime: Date) {
    const now = new Date();
    const diff = banEndTime.getTime() - now.getTime();
    
    if (diff <= 0) {
      this.remainingBanTime = '';
      return;
    }
    
    const minutes = Math.floor(diff / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    this.remainingBanTime = `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  register() {
    if (this.isBanned) {
      this.toastrService.error(`Kayıt engellenmiş. ${this.remainingBanTime} sonra tekrar deneyin.`, 'Kayıt Engellendi');
      return;
    }
    if (!this.registerForm.valid) {
      this.toastrService.error('Lütfen tüm alanları doğru şekilde doldurun');
      return;
    }

    this.isLoading = true;
    const registerModel = {
      firstName: this.registerForm.value.firstName,
      lastName: this.registerForm.value.lastName,
      email: this.registerForm.value.email,
      password: this.registerForm.value.password
    };

    // Normal register kullan (member rolü atanmaz)
    this.authService.register(registerModel)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Admin kaydı başarılı! Giriş yapabilirsiniz.', 'Başarılı');
            this.router.navigate(['/login']);
          } else {
            this.toastrService.error(response.message || 'Kayıt işlemi başarısız');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Kayıt işlemi sırasında bir hata oluştu');

          // Başarısız kayıt sonrası ban durumunu tekrar kontrol et
          setTimeout(() => {
            this.checkRegisterBanStatus();
          }, 1000);
        }
      });
  }

  // Şifre görünürlüğünü değiştirir
  togglePasswordVisibility(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  toggleConfirmPasswordVisibility(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }
}
