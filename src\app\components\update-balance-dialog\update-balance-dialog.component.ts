import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MemberService } from '../../services/member.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-update-balance-dialog',
  templateUrl: './update-balance-dialog.component.html',
  standalone: false
})
export class UpdateBalanceDialogComponent implements OnInit {
  balanceForm: FormGroup;
  isLoading: boolean = false;
  member: any;

  constructor(
    public dialogRef: MatDialogRef<UpdateBalanceDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { member: any },
    private fb: FormBuilder,
    private memberService: MemberService,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.member = this.data.member;
    this.initForm();
  }

  initForm(): void {
    this.balanceForm = this.fb.group({
      operationType: ['add', Validators.required],
      amount: [0, [Validators.required, Validators.min(0.01)]],
      note: ['']
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  updateBalance(): void {
    if (this.balanceForm.invalid) return;

    this.isLoading = true;
    const formValues = this.balanceForm.value;
    const amount = formValues.operationType === 'add' 
      ? formValues.amount 
      : -formValues.amount;

    const updatedMember = {
      ...this.member,
      balance: this.member.balance + amount
    };

    this.memberService.update(updatedMember).subscribe(
      response => {
        const actionText = formValues.operationType === 'add' ? 'eklendi' : 'çıkarıldı';
        this.toastrService.success(`Bakiye başarıyla ${actionText}`, 'Başarılı');
        this.isLoading = false;
        this.dialogRef.close({
          success: true,
          member: updatedMember,
          amount: amount,
          note: formValues.note
        });
      },
      error => {
        this.toastrService.error('Bakiye güncellenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    );
  }
}
