import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { 
  faArrowLeft, faEdit, faTrashAlt, faDumbbell, 
  faCalendarAlt, faClock, faUsers, faInfoCircle,
  faChartLine, faFileAlt, faCalendar, faBullseye 
} from '@fortawesome/free-solid-svg-icons';

import { WorkoutProgramService } from '../../services/workout-program.service';
import { AuthService } from '../../services/auth.service';
import { DialogService } from '../../services/dialog.service';
import { WorkoutProgramTemplate } from '../../models/workout-program.models';

@Component({
  selector: 'app-workout-program-detail',
  templateUrl: './workout-program-detail.component.html',
  styleUrls: ['./workout-program-detail.component.css'],
  standalone: false
})
export class WorkoutProgramDetailComponent implements OnInit {
  // Icons
  faArrowLeft = faArrowLeft;
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faDumbbell = faDumbbell;
  faCalendarAlt = faCalendarAlt;
  faClock = faClock;
  faUsers = faUsers;
  faInfoCircle = faInfoCircle;
  faChartLine = faChartLine;
  faFileAlt = faFileAlt;
  faCalendar = faCalendar;
  faBullseye = faBullseye;

  // Data
  program?: WorkoutProgramTemplate;
  programId!: number;
  isLoading = true;

  // User permissions
  isOwner = false;
  isAdmin = false;

  constructor(
    private workoutProgramService: WorkoutProgramService,
    private authService: AuthService,
    private dialogService: DialogService,
    private route: ActivatedRoute,
    private router: Router,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.programId = Number(this.route.snapshot.paramMap.get('id'));
    if (!this.programId) {
      this.toastrService.error('Geçersiz program ID', 'Hata');
      this.router.navigate(['/workout-programs']);
      return;
    }

    this.checkUserPermissions();
    this.loadProgram();
  }

  checkUserPermissions(): void {
    this.isOwner = this.authService.hasRole('owner');
    this.isAdmin = this.authService.hasRole('admin');
  }

  loadProgram(): void {
    this.isLoading = true;

    this.workoutProgramService.getById(this.programId).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.program = response.data;
        } else {
          this.toastrService.error('Program bulunamadı', 'Hata');
          this.router.navigate(['/workout-programs']);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading program:', error);
        this.toastrService.error('Program yüklenirken hata oluştu', 'Hata');
        this.router.navigate(['/workout-programs']);
        this.isLoading = false;
      }
    });
  }

  editProgram(): void {
    if (this.program) {
      this.router.navigate(['/workout-programs/edit', this.program.workoutProgramTemplateID]);
    }
  }

  deleteProgram(): void {
    if (!this.program) return;

    this.dialogService.confirm(
      'Program Silme Onayı',
      `"${this.program.programName}" adlı antrenman programını silmek istediğinizden emin misiniz?`,
      'Bu işlem geri alınamaz!'
    ).subscribe((result: boolean) => {
      if (result && this.program) {
        this.workoutProgramService.delete(this.program.workoutProgramTemplateID).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Antrenman programı başarıyla silindi', 'Başarılı');
              this.router.navigate(['/workout-programs']);
            } else {
              this.toastrService.error(response.message || 'Program silinirken hata oluştu', 'Hata');
            }
          },
          error: (error) => {
            console.error('Error deleting workout program:', error);
            this.toastrService.error('Program silinirken hata oluştu', 'Hata');
          }
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/workout-programs']);
  }

  getExperienceLevelBadgeClass(level?: string): string {
    switch (level) {
      case 'Başlangıç': return 'program-badge-success';
      case 'Orta': return 'program-badge-warning';
      case 'İleri': return 'program-badge-danger';
      default: return 'program-badge-secondary';
    }
  }

  getTargetGoalBadgeClass(goal?: string): string {
    switch (goal) {
      case 'Kilo Alma': return 'program-badge-info';
      case 'Kilo Verme': return 'program-badge-warning';
      case 'Kas Yapma': return 'program-badge-primary';
      case 'Kondisyon': return 'program-badge-success';
      case 'Güç': return 'program-badge-danger';
      default: return 'program-badge-secondary';
    }
  }

  formatDate(date?: Date): string {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getTotalExerciseCount(): number {
    if (!this.program?.days) return 0;
    return this.program.days.reduce((total, day) => {
      return total + (day.exercises?.length || 0);
    }, 0);
  }

  getRestDayCount(): number {
    if (!this.program?.days) return 0;
    return this.program.days.filter(day => day.isRestDay).length;
  }

  getWorkoutDayCount(): number {
    if (!this.program?.days) return 0;
    return this.program.days.filter(day => !day.isRestDay).length;
  }

  getEstimatedDuration(): string {
    const workoutDays = this.getWorkoutDayCount();
    if (workoutDays === 0) return '-';
    
    // Her antrenman günü için ortalama 60-90 dakika hesabı
    const avgMinutesPerDay = 75;
    const totalMinutes = workoutDays * avgMinutesPerDay;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    if (hours > 0) {
      return `${hours}s ${minutes}dk (haftalık)`;
    } else {
      return `${minutes}dk (haftalık)`;
    }
  }
}
