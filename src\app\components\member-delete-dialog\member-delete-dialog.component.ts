import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MembershipDetail } from '../../models/membershipDetail';

export interface MemberDeleteDialogData {
  memberName: string;
  memberId: number;
  activeMemberships: MembershipDetail[];
}

@Component({
  selector: 'app-member-delete-dialog',
  standalone: false,
  templateUrl: './member-delete-dialog.component.html',
  styleUrls: ['./member-delete-dialog.component.css']
})
export class MemberDeleteDialogComponent {
  selectedMembershipId: number | null = null;

  constructor(
    public dialogRef: MatDialogRef<MemberDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MemberDeleteDialogData
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onDelete(): void {
    if (this.selectedMembershipId) {
      this.dialogRef.close(this.selectedMembershipId);
    }
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }

  getAvatarColor(name: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];

    if (!name) return colors[0];

    const hash = name.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    return colors[Math.abs(hash) % colors.length];
  }
}
