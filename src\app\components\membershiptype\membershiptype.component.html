<!-- <PERSON><PERSON><PERSON> Türleri Tablosu - <PERSON><PERSON><PERSON> tablo kı<PERSON>, ba<PERSON><PERSON><PERSON><PERSON> yok -->
<div class="table-container">
  <table class="modern-table">
    <thead>
      <tr>
        <th>
          <i class="fas fa-dumbbell me-2"></i>
          Branş
        </th>
        <th>
          <i class="fas fa-tag me-2"></i>
          Tür
        </th>
        <th>
          <i class="fas fa-calendar-day me-2"></i>
          Süre
          <button class="sort-btn" (click)="sortByDuration()">
            <i class="fas" [ngClass]="sortDirection === 'asc' && activeSortField === 'duration' ? 'fa-sort-up' : 'fa-sort-down'"></i>
          </button>
        </th>
        <th>
          <i class="fas fa-money-bill-wave me-2"></i>
          Fiyat
        </th>
        <th>
          <i class="fas fa-cogs me-2"></i>
          <PERSON><PERSON><PERSON><PERSON>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let membershiptype of paginatedMembershipTypes.data" class="zoom-in">
        <td>
          <span class="modern-badge modern-badge-info">{{ membershiptype.branch }}</span>
        </td>
        <td>{{ membershiptype.typeName }}</td>
        <td>{{ getDayDisplay(membershiptype.day) }}</td>
        <td>
          <span class="modern-badge modern-badge-primary">{{ membershiptype.price }} ₺</span>
        </td>
        <td>
          <div class="action-buttons">
            <button
              type="button"
              class="modern-btn modern-btn-danger modern-btn-sm"
              (click)="deleteMembershipType(membershiptype)"
              title="Üyelik Türünü Sil"
            >
              <i class="fas fa-trash-alt"></i>
            </button>
            <button
              type="button"
              class="modern-btn modern-btn-primary modern-btn-sm ms-2"
              (click)="openUpdateDialog(membershiptype)"
              title="Üyelik Türünü Güncelle"
            >
              <i class="fas fa-edit"></i>
            </button>
          </div>
        </td>
      </tr>

      <!-- Veri yoksa gösterilecek mesaj -->
      <tr *ngIf="paginatedMembershipTypes.data.length === 0">
        <td colspan="5" class="text-center py-4">
          <i class="fas fa-dumbbell fa-2x mb-2 text-muted"></i>
          <p class="mb-0">Henüz üyelik türü tanımlanmamış.</p>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<!-- Pagination -->
<div class="d-flex justify-content-between align-items-center mt-4 flex-wrap gap-3" *ngIf="paginatedMembershipTypes.totalCount > 0">
  <div class="d-flex align-items-center gap-3 flex-wrap">
    <div class="table-info">
      <span class="text-muted">
        Toplam {{ paginatedMembershipTypes.totalCount }} kayıttan
        {{ (paginatedMembershipTypes.pageNumber - 1) * paginatedMembershipTypes.pageSize + 1 }} -
        {{ paginatedMembershipTypes.pageNumber * paginatedMembershipTypes.pageSize > paginatedMembershipTypes.totalCount ? paginatedMembershipTypes.totalCount : paginatedMembershipTypes.pageNumber * paginatedMembershipTypes.pageSize }}
        arası gösteriliyor
      </span>
    </div>
    <div class="d-flex align-items-center gap-2">
      <span class="text-muted">Sayfa başına:</span>
      <select
        class="form-select form-select-sm"
        style="width: auto; min-width: 70px;"
        [value]="paginatedMembershipTypes.pageSize"
        (change)="onPageSizeChange($event)">
        <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }}</option>
      </select>
    </div>
  </div>
  <nav aria-label="Page navigation" *ngIf="paginatedMembershipTypes.totalPages > 1">
    <ul class="modern-pagination">
      <li class="modern-page-item" [class.disabled]="paginatedMembershipTypes.pageNumber === 1">
        <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(paginatedMembershipTypes.pageNumber - 1)" style="border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
      <li
        class="modern-page-item"
        *ngFor="let page of getPaginationRange()"
        [class.active]="page === paginatedMembershipTypes.pageNumber"
        [class.disabled]="page === -1"
      >
        <a class="modern-page-link" href="javascript:void(0)" (click)="page !== -1 && onPageChange(page)">
          {{ page === -1 ? '...' : page }}
        </a>
      </li>
      <li class="modern-page-item" [class.disabled]="paginatedMembershipTypes.pageNumber === paginatedMembershipTypes.totalPages">
        <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(paginatedMembershipTypes.pageNumber + 1)" style="border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    </ul>
  </nav>
</div>
