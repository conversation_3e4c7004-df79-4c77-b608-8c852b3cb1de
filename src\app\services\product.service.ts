import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { ResponseModel } from '../models/responseModel';
import { Product } from '../models/product';
import { BaseApiService } from './baseApiService';
import { PaginatedResult, ProductPagingParameters } from '../models/pagination';
import { SingleResponseModel } from '../models/singleResponseModel';

@Injectable({
  providedIn: 'root'
})
export class ProductService extends BaseApiService {
  constructor(private httpClient: HttpClient) {
    super();
  }

  getProducts(): Observable<ListResponseModel<Product>> {
    return this.httpClient.get<ListResponseModel<Product>>(this.apiUrl + 'products/getall');
  }

  getProductsPaginated(parameters: ProductPagingParameters): Observable<SingleResponseModel<PaginatedResult<Product>>> {
    let params = new HttpParams()
      .set('pageNumber', parameters.pageNumber.toString())
      .set('pageSize', parameters.pageSize.toString());

    if (parameters.searchText) {
      params = params.set('searchText', parameters.searchText);
    }
    if (parameters.minPrice !== undefined && parameters.minPrice !== null) {
      params = params.set('minPrice', parameters.minPrice.toString());
    }
    if (parameters.maxPrice !== undefined && parameters.maxPrice !== null) {
      params = params.set('maxPrice', parameters.maxPrice.toString());
    }
    if (parameters.sortBy) {
      params = params.set('sortBy', parameters.sortBy);
    }
    if (parameters.sortDirection) {
      params = params.set('sortDirection', parameters.sortDirection);
    }

    return this.httpClient.get<SingleResponseModel<PaginatedResult<Product>>>(
      this.apiUrl + 'products/getallpaginated', { params }
    );
  }

  addProduct(product: Product): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'products/add', product);
  }

  updateProduct(product: Product): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'products/update', product);
  }

  deleteProduct(productId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'products/delete?id=' + productId, {});
  }
}