import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ExpenseService } from '../../services/expense.service';
import { ExpenseDto } from '../../models/expenseDto.model';

import { ExpensePagingParameters, ExpenseFilterState, ExpenseSortState } from '../../models/expensePagingParameters';
import { PaginatedResult } from '../../models/pagination';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { faEdit, faTrashAlt, faPlus, faFilter, faCalendarAlt, faSort, faSortUp, faSortDown, faFileExport, faFileExcel, faFilePdf } from '@fortawesome/free-solid-svg-icons';
import { ExpenseDialogComponent } from '../expense-dialog/expense-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, registerables, ChartConfiguration } from 'chart.js';
import { ChartUtilsService } from '../../services/chart-utils.service';
import * as XLSX from 'xlsx';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

Chart.register(...registerables);

@Component({
  selector: 'app-expense-management',
  templateUrl:  './expense-management.component.html',
  styleUrls: ['./expense-management.component.css'],
  standalone: false
})
export class ExpenseManagementComponent implements OnInit, OnDestroy, AfterViewInit {

  // Pagination ve filtreleme
  paginatedExpenses: PaginatedResult<ExpenseDto> = {
    data: [],
    pageNumber: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    hasPrevious: false,
    hasNext: false
  };

  isLoading = false;

  // Filtreleme durumu
  filterState: ExpenseFilterState = {
    searchText: '',
    startDate: null,
    endDate: null,
    expenseType: '',
    minAmount: null,
    maxAmount: null,
    selectedYear: new Date().getFullYear(),
    selectedMonth: new Date().getMonth() + 1
  };

  // Başlangıç filtre durumu (ara butonu için)
  private initialFilterState: ExpenseFilterState;

  // Sıralama durumu
  sortState: ExpenseSortState = {
    sortBy: 'ExpenseDate',
    sortDirection: 'desc'
  };

  // Grafik için yıl seçimi
  selectedYear: number;
  selectedMonth: number;
  initialYear: number;
  initialMonth: number;

  searchControl = new FormControl('');

  // Toplam gider tutarı
  totalExpenseAmount: number = 0;

  // Dinamik gider türleri için
  expenseTypeBreakdown: { [key: string]: number } = {};

  // Gider türleri listesi
  get expenseTypes(): string[] {
    return Object.keys(this.expenseTypeBreakdown);
  }

  // Aktif filtreler
  activeStartDate: string = '';
  activeEndDate: string = '';

  // Yıl filtreleme için (aylık trend grafiği)
  availableYears: number[] = [];

  // Gider türleri (dropdown için)
  expenseTypesForDropdown: string[] = [
    'Fatura - Elektrik', 'Fatura - Su', 'Fatura - Doğalgaz', 'Fatura - İnternet',
    'Maaş Ödemesi', 'Kira', 'Malzeme Alımı', 'Temizlik Malzemesi',
    'Ofis Gideri', 'Bakım/Onarım', 'Vergi/Harç', 'Diğer'
  ];

  // Ay isimleri
  months: { value: number, name: string }[] = [
    { value: 1, name: 'Ocak' }, { value: 2, name: 'Şubat' }, { value: 3, name: 'Mart' },
    { value: 4, name: 'Nisan' }, { value: 5, name: 'Mayıs' }, { value: 6, name: 'Haziran' },
    { value: 7, name: 'Temmuz' }, { value: 8, name: 'Ağustos' }, { value: 9, name: 'Eylül' },
    { value: 10, name: 'Ekim' }, { value: 11, name: 'Kasım' }, { value: 12, name: 'Aralık' }
  ];

  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faPlus = faPlus;
  faFilter = faFilter;
  faCalendarAlt = faCalendarAlt;
  faSort = faSort;
  faSortUp = faSortUp;
  faSortDown = faSortDown;
  faFileExport = faFileExport;
  faFileExcel = faFileExcel;
  faFilePdf = faFilePdf;

  private destroy$ = new Subject<void>();

  // Chart instances - PaymentHistory sistemine uygun
  expenseDistributionChart: Chart | null = null;
  monthlyExpenseChart: Chart | null = null;

  // Chart initialization control
  isInitializingCharts = false;
  chartInitRetryCount = 0;
  MAX_CHART_INIT_RETRIES = 3;

  initialLoadComplete = false;

  // Template'de kullanım için
  Math = Math;

  // Performans iyileştirmeleri
  private filterCache = new Map<string, PaginatedResult<ExpenseDto>>();
  private lastFilterParams: string = '';

  // Loading states
  isSearching = false;
  isExporting = false;

  // Spam koruması
  private lastSearchTime = 0;
  private searchCooldown = 1000; // 1 saniye cooldown
  private lastExportTime = 0;
  private exportCooldown = 3000; // 3 saniye cooldown
  private lastDialogOpenTime = 0;
  private dialogCooldown = 500; // 0.5 saniye cooldown

  // Favori filtreler kaldırıldı

  constructor(
    private expenseService: ExpenseService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService,
    private cdRef: ChangeDetectorRef,
    private chartUtils: ChartUtilsService
  ) {
    const currentDate = new Date();
    this.initialYear = currentDate.getFullYear();
    this.initialMonth = currentDate.getMonth() + 1;
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;

    // Filter state'i başlat
    this.filterState.selectedYear = this.initialYear;
    this.filterState.selectedMonth = this.initialMonth;

    // Başlangıç filtre durumunu kaydet
    this.initialFilterState = { ...this.filterState };

    // PaymentHistory benzeri yıl filtreleme
    this.initializeYearFilter();
  }

  ngOnInit(): void {
    // Arama durumunu sıfırla
    this.isSearching = false;

    // PaymentHistory benzeri veri yükleme
    this.loadExpenseTotals();

    // Dashboard verilerini yükle
    this.loadDashboardData();

    // Sayfalanmış verileri yükle
    this.loadExpensesPaginated();

    // Otomatik arama kaldırıldı - sadece ara butonuyla arama yapılacak
    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe((value) => {
      this.filterState.searchText = value || '';
    });

    // Keyboard navigation
    this.setupKeyboardNavigation();

    // Favori filtreler kaldırıldı
  }

  private setupKeyboardNavigation(): void {
    document.addEventListener('keydown', this.handleKeyboardEvent);
  }

  ngAfterViewInit(): void {
    if (this.initialLoadComplete) {
       this.initializeCharts();
    }
  }


  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.destroyExistingCharts();

    // Keyboard event listener'ı temizle
    document.removeEventListener('keydown', this.handleKeyboardEvent);
  }

  private handleKeyboardEvent = (event: KeyboardEvent) => {
    // Keyboard navigation logic
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
      event.preventDefault();
      this.openExpenseDialog();
    }

    if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
      event.preventDefault();
      this.exportToExcel();
    }

    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      const searchInput = document.querySelector('.search-input') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }
    }

    if (event.key === 'Escape') {
      if (this.hasActiveFilters()) {
        this.clearFilters();
      }
    }

    if (event.altKey) {
      if (event.key === 'ArrowLeft' && this.paginatedExpenses.hasPrevious) {
        event.preventDefault();
        this.goToPage(this.paginatedExpenses.pageNumber - 1);
      }
      if (event.key === 'ArrowRight' && this.paginatedExpenses.hasNext) {
        event.preventDefault();
        this.goToPage(this.paginatedExpenses.pageNumber + 1);
      }
    }
  }

  // OPTIMIZE: Yeni tek API çağrısı metodu - 5 API isteği yerine 1 API isteği
  loadDashboardData(): void {
    this.isLoading = true;

    this.expenseService.getDashboardData(this.selectedYear, this.selectedMonth)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            const dashboardData = response.data;

            // Dashboard verileri yüklendi

            // Aylık trend verisi
            const labels: string[] = [];
            const data: number[] = [];
            for (let month = 1; month <= 12; month++) {
              const label = `${month.toString().padStart(2, '0')}/${this.selectedYear}`;
              labels.push(label);
              data.push(dashboardData.monthlyExpenseSummary[month] || 0);
            }


            // Filtreleri uygula ve grafikleri oluştur
            this.applyFilters();
            this.initialLoadComplete = true;
            this.initializeCharts();

          } else {
            this.handleDashboardError(response.message || 'Dashboard verileri yüklenemedi.');
          }
          this.isLoading = false;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error fetching dashboard data:', error);
          this.handleDashboardError('Dashboard verileri yüklenirken bir sunucu hatası oluştu.');
          this.isLoading = false;
          this.cdRef.detectChanges();
        }
      });
  }

  private handleDashboardError(message: string): void {
    this.toastrService.error(message, 'Hata');
    // Hata durumunda varsayılan değerleri ata

    // Boş grafik verisi oluştur
    const labels: string[] = [];
    const data: number[] = [];
    for (let month = 1; month <= 12; month++) {
      labels.push(`${month.toString().padStart(2, '0')}/${this.selectedYear}`);
      data.push(0);
    }


    // Chart initialization sadece ngOnInit'te yapılacak
  }





  // Performanslı pagination metodu (cache'li)
  loadExpensesPaginated(): void {
    this.isLoading = true;

    // Tarih değerlerini Date nesnesine çevir
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (this.filterState.startDate) {
      startDate = new Date(this.filterState.startDate);

      // Eğer başlangıç tarihi var ama bitiş tarihi yoksa, bitiş tarihi bugün olsun
      if (!this.filterState.endDate) {
        endDate = new Date(); // Bugünün tarihi
      }
    }

    if (this.filterState.endDate) {
      endDate = new Date(this.filterState.endDate);
    }

    const parameters: ExpensePagingParameters = {
      pageNumber: this.paginatedExpenses.pageNumber,
      pageSize: this.paginatedExpenses.pageSize,
      searchText: this.filterState.searchText,
      // Sıralama frontend tarafında yapılacağı için API'ye gönderilmiyor
      sortBy: 'ExpenseDate', // Default sıralama
      sortDirection: 'desc', // Default sıralama
      startDate: startDate,
      endDate: endDate,
      expenseType: this.filterState.expenseType,
      minAmount: this.filterState.minAmount || undefined,
      maxAmount: this.filterState.maxAmount || undefined,
      isActive: true
    };

    // Cache key oluştur
    const cacheKey = this.generateCacheKey(parameters);

    // Cache'den kontrol et
    if (this.filterCache.has(cacheKey) && this.lastFilterParams === cacheKey) {
      const cachedData = this.filterCache.get(cacheKey)!;
      this.paginatedExpenses = cachedData;
      this.isLoading = false;
      this.cdRef.detectChanges();
      return;
    }

    this.expenseService.getExpensesPaginated(parameters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.paginatedExpenses = response.data;

            // Sıralamayı uygula
            this.applySorting();

            // Cache'e kaydet (max 10 entry)
            if (this.filterCache.size >= 10) {
              const firstKey = this.filterCache.keys().next().value;
              if (firstKey) {
                this.filterCache.delete(firstKey);
              }
            }
            this.filterCache.set(cacheKey, response.data);
            this.lastFilterParams = cacheKey;
          } else {
            this.toastrService.error(response.message || 'Giderler yüklenemedi.', 'Hata');
          }
          this.isLoading = false;
          this.isSearching = false; // Arama durumunu sıfırla
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error loading paginated expenses:', error);
          this.toastrService.error('Giderler yüklenirken bir hata oluştu.', 'Hata');
          this.isLoading = false;
          this.isSearching = false; // Arama durumunu sıfırla
          this.cdRef.detectChanges();
        }
      });
  }

  private generateCacheKey(parameters: ExpensePagingParameters): string {
    // Güvenli tarih dönüşümü
    let startDateStr = '';
    let endDateStr = '';

    try {
      if (parameters.startDate) {
        if (typeof parameters.startDate === 'string') {
          startDateStr = parameters.startDate;
        } else if (parameters.startDate instanceof Date) {
          startDateStr = parameters.startDate.toISOString();
        } else {
          startDateStr = JSON.stringify(parameters.startDate);
        }
      }

      if (parameters.endDate) {
        if (typeof parameters.endDate === 'string') {
          endDateStr = parameters.endDate;
        } else if (parameters.endDate instanceof Date) {
          endDateStr = parameters.endDate.toISOString();
        } else {
          endDateStr = JSON.stringify(parameters.endDate);
        }
      }
    } catch (error) {
      console.warn('Date conversion error in generateCacheKey:', error);
      startDateStr = parameters.startDate ? String(parameters.startDate) : '';
      endDateStr = parameters.endDate ? String(parameters.endDate) : '';
    }

    return JSON.stringify({
      pageNumber: parameters.pageNumber,
      pageSize: parameters.pageSize,
      searchText: parameters.searchText || '',
      startDate: startDateStr,
      endDate: endDateStr,
      expenseType: parameters.expenseType || '',
      minAmount: parameters.minAmount || 0,
      maxAmount: parameters.maxAmount || 0
    });
  }

  resetPaginationAndLoad(): void {
    this.paginatedExpenses.pageNumber = 1;
    this.clearCache(); // Cache'i temizle
    this.loadExpensesPaginated();
  }

  private clearCache(): void {
    this.filterCache.clear();
    this.lastFilterParams = '';
  }

  // Eski applyFilters metodu (geriye uyumluluk için)
  applyFilters(): void {
    // Bu metot artık sadece dashboard için kullanılıyor
    // Pagination için resetPaginationAndLoad kullanılıyor
    this.updateCharts();
    this.cdRef.detectChanges();
  }








  onFilterInputChange(): void {
    // Filtre değiştiğinde sadece ara butonunu göster, otomatik arama yapma
  }

  shouldShowSearchButton(): boolean {
    // Ara butonu şu durumlarda görünür:
    // 1. Arama textbox'ında değer varsa
    // 2. Herhangi bir filtre başlangıç değerinden farklıysa
    return !!(
      this.searchControl.value ||
      this.filterState.startDate !== this.initialFilterState.startDate ||
      this.filterState.endDate !== this.initialFilterState.endDate ||
      this.filterState.expenseType !== this.initialFilterState.expenseType ||
      this.filterState.minAmount !== this.initialFilterState.minAmount ||
      this.filterState.maxAmount !== this.initialFilterState.maxAmount ||
      this.selectedYear !== this.initialYear ||
      this.selectedMonth !== this.initialMonth
    );
  }

  performSearch(): void {
    // Spam koruması - eğer zaten arama yapılıyorsa çık
    if (this.isSearching) {
      return;
    }

    const now = Date.now();
    if (now - this.lastSearchTime < this.searchCooldown) {
      this.toastrService.warning('Çok hızlı arama yapıyorsunuz. Lütfen bekleyin.', 'Uyarı');
      return;
    }

    this.lastSearchTime = now;
    this.isSearching = true;

    // Aktif filtreleri güncelle (sadece arama yapıldığında)
    this.activeStartDate = this.filterState.startDate || '';
    this.activeEndDate = this.filterState.endDate || '';

    // Search control'daki değeri filterState'e aktar
    this.filterState.searchText = this.searchControl.value || '';

    // PaymentHistory benzeri veri yükleme
    this.loadExpenseTotals();

    // Arama işlemini başlat
    this.resetPaginationAndLoad();
  }

  clearFilters(): void {
    // Temel filtreleri temizle
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;
    this.searchControl.setValue('');
    this.isSearching = false; // Arama durumunu da sıfırla

    // Aktif filtreleri de sıfırla
    this.activeStartDate = '';
    this.activeEndDate = '';

    // Gelişmiş filtreleri başlangıç değerlerine döndür
    this.filterState = { ...this.initialFilterState };

    // PaymentHistory benzeri veri yükleme
    this.loadExpenseTotals();
    this.loadDashboardData();
    this.resetPaginationAndLoad();
    this.toastrService.info('Filtreler temizlendi', 'Bilgi');
  }

  clearSearch(): void {
    this.searchControl.setValue('');
    this.filterState.searchText = '';
    this.resetPaginationAndLoad();
  }

  resetFilters(): void {
    this.clearFilters();
  }

  hasActiveFilters(): boolean {
    // Sadece manuel filtreleri kontrol et (tarih, arama, tür, miktar)
    // Yıl/ay filtreleri dashboard için ayrı tutulur
    return !!(
      this.filterState.searchText ||
      this.filterState.startDate ||
      this.filterState.endDate ||
      this.filterState.expenseType ||
      this.filterState.minAmount ||
      this.filterState.maxAmount
    );
  }

  // Sıralama metotları (frontend tarafında)
  sortBy(column: string): void {
    if (this.sortState.sortBy === column) {
      this.sortState.sortDirection = this.sortState.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortState.sortBy = column;
      this.sortState.sortDirection = 'desc';
    }

    // Frontend tarafında sıralama yap
    this.applySorting();
  }

  // Frontend tarafında sıralama uygula
  private applySorting(): void {
    if (!this.paginatedExpenses.data || this.paginatedExpenses.data.length === 0) {
      return;
    }

    const sortedData = [...this.paginatedExpenses.data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (this.sortState.sortBy) {
        case 'ExpenseDate':
          aValue = new Date(a.expenseDate);
          bValue = new Date(b.expenseDate);
          break;
        case 'ExpenseType':
          aValue = a.expenseType?.toLowerCase() || '';
          bValue = b.expenseType?.toLowerCase() || '';
          break;
        case 'Amount':
          aValue = a.amount;
          bValue = b.amount;
          break;
        case 'Description':
          aValue = a.description?.toLowerCase() || '';
          bValue = b.description?.toLowerCase() || '';
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return this.sortState.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortState.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    // Sıralanmış veriyi güncelle
    this.paginatedExpenses = {
      ...this.paginatedExpenses,
      data: sortedData
    };
  }

  getSortIcon(column: string): any {
    if (this.sortState.sortBy !== column) {
      return this.faSort;
    }
    return this.sortState.sortDirection === 'asc' ? this.faSortUp : this.faSortDown;
  }

  // Sayfalama metotları
  goToPage(page: number): void {
    if (page >= 1 && page <= this.paginatedExpenses.totalPages) {
      this.paginatedExpenses.pageNumber = page;
      this.loadExpensesPaginated();
    }
  }

  changePageSize(size: number): void {
    this.paginatedExpenses.pageSize = size;
    this.resetPaginationAndLoad();
  }

  getPageNumbers(): number[] {
    const totalPages = this.paginatedExpenses.totalPages;
    const currentPage = this.paginatedExpenses.pageNumber;
    const pages: number[] = [];

    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  getMonthName(monthValue: number): string {
    return this.months.find(m => m.value === monthValue)?.name || '';
  }

  getBadgeClass(expenseType: string | null | undefined): string {
    if (!expenseType) return 'modern-badge-secondary';
    const typeLower = expenseType.toLowerCase();
    if (typeLower.includes('fatura')) return 'modern-badge-info';
    if (typeLower.includes('maaş')) return 'modern-badge-primary';
    if (typeLower.includes('kira')) return 'modern-badge-warning';
    if (typeLower.includes('malzeme')) return 'modern-badge-success';
    if (typeLower.includes('temizlik')) return 'modern-badge-info';
    if (typeLower.includes('ofis')) return 'modern-badge-secondary';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'modern-badge-danger';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'modern-badge-warning';
    return 'modern-badge-secondary';
  }



  getExpenseTypeTooltip(expenseType: string | null | undefined): string {
    if (!expenseType) return 'Gider türü belirtilmemiş';

    const typeLower = expenseType.toLowerCase();

    if (typeLower.includes('elektrik')) return 'Elektrik faturası gideri';
    if (typeLower.includes('su')) return 'Su faturası gideri';
    if (typeLower.includes('doğalgaz')) return 'Doğalgaz faturası gideri';
    if (typeLower.includes('internet')) return 'İnternet faturası gideri';
    if (typeLower.includes('maaş')) return 'Personel maaş ödemesi';
    if (typeLower.includes('kira')) return 'Kira ödemesi';
    if (typeLower.includes('malzeme')) return 'Malzeme alım gideri';
    if (typeLower.includes('temizlik')) return 'Temizlik malzemesi gideri';
    if (typeLower.includes('ofis')) return 'Ofis gideri';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'Bakım ve onarım gideri';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'Vergi ve harç ödemesi';

    return `${expenseType} gideri`;
  }

  getExpenseTypeColor(expenseType: string | null | undefined): string {
    if (!expenseType) return '#6c757d'; // Gray for undefined

    const typeLower = expenseType.toLowerCase();

    // Fatura giderleri - Mavi tonları
    if (typeLower.includes('elektrik')) return '#007bff';
    if (typeLower.includes('su')) return '#17a2b8';
    if (typeLower.includes('doğalgaz')) return '#fd7e14';
    if (typeLower.includes('internet')) return '#6f42c1';

    // Personel giderleri - Yeşil tonları
    if (typeLower.includes('maaş')) return '#28a745';

    // Kira - Turuncu
    if (typeLower.includes('kira')) return '#fd7e14';

    // Malzeme giderleri - Mor tonları
    if (typeLower.includes('malzeme')) return '#6f42c1';
    if (typeLower.includes('temizlik')) return '#20c997';

    // Ofis giderleri - İndigo
    if (typeLower.includes('ofis')) return '#6610f2';

    // Bakım/Onarım - Kırmızı
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return '#dc3545';

    // Vergi/Harç - Sarı
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return '#ffc107';

    // Diğer - Gri
    if (typeLower.includes('diğer')) return '#6c757d';

    // Varsayılan - Primary renk
    return '#007bff';
  }

  // Performanslı export metodu (spam korumalı)
  exportToExcel(): void {
    const now = Date.now();
    if (now - this.lastExportTime < this.exportCooldown) {
      this.toastrService.warning('Çok hızlı indirmeye çalışıyorsunuz. Lütfen bekleyin.', 'Uyarı');
      return;
    }

    if (this.isExporting) {
      this.toastrService.warning('Export işlemi devam ediyor...', 'Uyarı');
      return;
    }

    this.lastExportTime = now;
    this.isExporting = true;
    this.toastrService.info('Excel dosyası hazırlanıyor...', 'Bilgi');

    // Aktif filtreleri kontrol et
    const filtersActive = this.hasActiveFilters();

    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (filtersActive) {
      // Filtre varsa, filtreleme sonucunu indir
      if (this.filterState.startDate) {
        startDate = new Date(this.filterState.startDate);

        // Eğer başlangıç tarihi var ama bitiş tarihi yoksa, bitiş tarihi bugün olsun
        if (!this.filterState.endDate) {
          endDate = new Date(); // Bugünün tarihi
        }
      }

      if (this.filterState.endDate) {
        endDate = new Date(this.filterState.endDate);
      }
    } else {
      // Filtre yoksa, mevcut ayın 1'inden bugüne kadar olan verileri indir
      const now = new Date();
      startDate = new Date(now.getFullYear(), now.getMonth(), 1); // Ayın ilk günü
      endDate = new Date(); // Bugün
    }

    const parameters: ExpensePagingParameters = {
      pageNumber: 1,
      pageSize: 10000, // Tüm verileri al
      searchText: this.filterState.searchText,
      // Export için default sıralama
      sortBy: 'ExpenseDate',
      sortDirection: 'desc',
      startDate: startDate,
      endDate: endDate,
      expenseType: this.filterState.expenseType,
      minAmount: this.filterState.minAmount || undefined,
      maxAmount: this.filterState.maxAmount || undefined,
      isActive: true
    };

    this.expenseService.getAllExpensesFiltered(parameters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            // Async olarak Excel dosyasını oluştur
            setTimeout(() => {
              this.generateExcelFile(response.data, startDate, endDate);
              this.isExporting = false;
            }, 100);
          } else {
            this.toastrService.error('Export verileri alınamadı.', 'Hata');
            this.isExporting = false;
          }
        },
        error: (error) => {
          console.error('Error exporting to Excel:', error);
          this.toastrService.error('Excel export sırasında bir hata oluştu.', 'Hata');
          this.isExporting = false;
        }
      });
  }

  private generateExcelFile(expenses: ExpenseDto[], startDate?: Date, endDate?: Date): void {
    try {
      // Yeni bir Workbook oluştur
      const workbook = new Workbook();
      const worksheet = workbook.addWorksheet('Giderler');

      // Sütun başlıklarını tanımla
      worksheet.columns = [
        { header: 'Gider Türü', key: 'expenseType', width: 25 },
        { header: 'Tutar', key: 'amount', width: 20 },
        { header: 'Gider Tarihi', key: 'expenseDate', width: 20 },
        { header: 'Açıklama', key: 'description', width: 35 },
        { header: 'Oluşturma Tarihi', key: 'creationDate', width: 20 },
        { header: '', key: 'empty1', width: 5 }, // Boş sütun
        { header: 'ÖZET', key: 'summary', width: 20 }, // Özet başlığı
        { header: 'TUTAR', key: 'summaryAmount', width: 15 } // Özet tutar
      ];

      // Veri satırlarını eklemeden önce tarihe göre sırala (en yeni en üstte)
      expenses.sort((a: ExpenseDto, b: ExpenseDto) => new Date(b.expenseDate).getTime() - new Date(a.expenseDate).getTime());

      // Export edilecek verilerden toplamları hesapla
      const exportTotals = this.calculateExpenseExportTotals(expenses);

      // Sıralanmış veriyi ekle
      let rowIndex = 2; // Başlık satırından sonra
      expenses.forEach((expense: ExpenseDto) => {
        worksheet.addRow({
          expenseType: expense.expenseType || '-',
          amount: expense.amount,
          expenseDate: new Date(expense.expenseDate).toLocaleDateString('tr-TR'),
          description: expense.description || '-',
          creationDate: new Date(expense.creationDate).toLocaleDateString('tr-TR'),
          empty1: '',
          summary: '',
          summaryAmount: ''
        });
        rowIndex++;
      });

      // Sağ tarafta özet tablosu ekle
      this.addExpenseSummaryTable(worksheet, exportTotals, rowIndex, startDate, endDate);

      // Dosya adını tarih aralığına göre oluştur
      let fileName: string;
      if (startDate && endDate) {
        const startDateStr = startDate.toLocaleDateString('tr-TR').replace(/\./g, '.');
        const endDateStr = endDate.toLocaleDateString('tr-TR').replace(/\./g, '.');
        fileName = `Giderler_${startDateStr}-${endDateStr}.xlsx`;
      } else {
        // Fallback: bugünün tarihi
        const now = new Date();
        const dateSuffix = now.toLocaleDateString('tr-TR').replace(/\./g, '.');
        fileName = `Giderler_${dateSuffix}.xlsx`;
      }

      // Workbook'u buffer'a yaz ve dosyayı indir
      workbook.xlsx.writeBuffer().then((buffer) => {
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        fs.saveAs(blob, fileName);
        this.toastrService.success('Excel dosyası başarıyla indirildi.', 'Başarılı');
      }).catch(error => {
        console.error('Excel export error:', error);
        this.toastrService.error('Excel dosyası oluşturulurken bir hata oluştu', 'Hata');
      });

    } catch (error) {
      console.error('Excel generation error:', error);
      this.toastrService.error('Excel dosyası oluşturulurken bir hata oluştu', 'Hata');
    }
  }

  // Excel export için gider toplamlarını hesapla
  private calculateExpenseExportTotals(expenses: ExpenseDto[]) {
    // Gider türlerine göre toplamları hesapla
    const expensesByType = expenses.reduce((acc, expense) => {
      const type = expense.expenseType || 'Diğer';
      if (!acc[type]) {
        acc[type] = 0;
      }
      acc[type] += expense.amount;
      return acc;
    }, {} as { [key: string]: number });

    // Genel toplam
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    return {
      expensesByType,
      totalAmount,
      totalCount: expenses.length
    };
  }

  // Excel'e gider özet tablosu ekle
  private addExpenseSummaryTable(worksheet: any, totals: any, startRow: number, startDate?: Date, endDate?: Date) {
    // Tarih aralığını başlıkların üstüne ekle (1. satır)
    let currentRow = 1;

    if (startDate && endDate) {
      const startDateStr = startDate.toLocaleDateString('tr-TR');
      const endDateStr = endDate.toLocaleDateString('tr-TR');

      // Eğer bitiş tarihi bugünse ve sadece başlangıç tarihi seçilmişse
      const today = new Date();
      const isEndDateToday = endDate.toDateString() === today.toDateString();
      const isEndDateNotSelected = !this.filterState.endDate;

      if (isEndDateToday && isEndDateNotSelected) {
        // Bugünün tarihini göster
        const todayStr = today.toLocaleDateString('tr-TR');
        worksheet.getCell(`G${currentRow}`).value = `${startDateStr} - ${todayStr}`;
      } else {
        worksheet.getCell(`G${currentRow}`).value = `${startDateStr} - ${endDateStr}`;
      }
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    } else if (startDate && !endDate) {
      // Sadece başlangıç tarihi varsa (bu durum artık yukarıdaki mantıkla ele alınıyor)
      const startDateStr = startDate.toLocaleDateString('tr-TR');
      const todayStr = new Date().toLocaleDateString('tr-TR');
      worksheet.getCell(`G${currentRow}`).value = `${startDateStr} - ${todayStr}`;
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    } else if (!startDate && endDate) {
      // Sadece bitiş tarihi varsa
      const endDateStr = endDate.toLocaleDateString('tr-TR');
      worksheet.getCell(`G${currentRow}`).value = `Başlangıç - ${endDateStr}`;
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    } else {
      // Tarih filtresi yoksa "Tüm Veriler" yazsın
      worksheet.getCell(`G${currentRow}`).value = 'Tüm Veriler';
      worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 10, color: { argb: 'FF0066CC' } };
      // Tarih aralığını G ve H sütunlarına yay
      worksheet.mergeCells(`G${currentRow}:H${currentRow}`);
    }

    // Başlık satırını formatla (2. satır)
    currentRow = 2;
    const headerRow = worksheet.getRow(currentRow);
    headerRow.getCell(7).value = 'ÖZET';
    headerRow.getCell(8).value = 'TUTAR';
    headerRow.getCell(7).font = { bold: true, size: 12 };
    headerRow.getCell(8).font = { bold: true, size: 12 };
    headerRow.getCell(7).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };
    headerRow.getCell(8).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };

    // Özet verilerini ekle (3. satırdan başla)
    currentRow = 3;

    // Gider türlerine göre toplamları ekle
    Object.entries(totals.expensesByType).forEach(([type, amount]) => {
      worksheet.getCell(`G${currentRow}`).value = type;
      worksheet.getCell(`H${currentRow}`).value = amount;
      worksheet.getCell(`H${currentRow}`).numFmt = '#,##0.00 ₺';
      currentRow++;
    });

    // Boş satır
    currentRow++;

    // Toplam gider sayısı
    worksheet.getCell(`G${currentRow}`).value = 'Toplam Gider Sayısı';
    worksheet.getCell(`H${currentRow}`).value = totals.totalCount;
    currentRow++;

    // Boş satır
    currentRow++;

    // Genel toplam
    worksheet.getCell(`G${currentRow}`).value = 'GENEL TOPLAM';
    worksheet.getCell(`H${currentRow}`).value = totals.totalAmount;
    worksheet.getCell(`H${currentRow}`).numFmt = '#,##0.00 ₺';
    worksheet.getCell(`G${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } }; // Yeşil ve kalın
    worksheet.getCell(`H${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } };
    worksheet.getCell(`G${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };
    worksheet.getCell(`H${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };

    // Özet tablosu etrafına border ekle
    for (let row = 1; row <= currentRow; row++) {
      for (let col = 7; col <= 8; col++) {
        const cell = worksheet.getCell(row, col);
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      }
    }
  }

  openExpenseDialog(expense?: ExpenseDto): void {
    const now = Date.now();
    if (now - this.lastDialogOpenTime < this.dialogCooldown) {
      return; // Sessizce engelle
    }

    this.lastDialogOpenTime = now;

    const dialogRef = this.dialog.open(ExpenseDialogComponent, {
      width: '600px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      data: expense ? { ...expense } : null,
      disableClose: false,
      panelClass: 'modern-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) { // Dialog başarıyla kapandıysa (kaydetme/güncelleme yapıldıysa)
        // Tüm verileri yeniden yükle (kartlar dahil)
        this.loadExpenseTotals();
        this.loadDashboardData();
        this.resetPaginationAndLoad();
      }
    });
  }






  deleteExpense(expense: ExpenseDto): void {
    // confirmGeneric yerine confirmExpenseDelete kullanıldı
    this.dialogService.confirmExpenseDelete(expense)
      .subscribe((result: boolean) => {
        if (result) {
          this.isLoading = true;
          this.expenseService.delete(expense.expenseID)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: (response) => {
                if (response.success) {
                  this.toastrService.success('Gider başarıyla silindi.', 'Başarılı');

                  // Tüm verileri yeniden yükle
                  this.loadExpenseTotals();
                  this.loadDashboardData();
                  this.resetPaginationAndLoad();
                } else {
                  this.toastrService.error(response.message || 'Gider silinemedi.', 'Hata');
                   this.isLoading = false;
                }
              },
              error: (error) => {
                console.error('Error deleting expense:', error);
                this.toastrService.error('Gider silinirken bir sunucu hatası oluştu.', 'Hata');
                this.isLoading = false;
              }
            });
        }
      });
  }

  // --- Chart Methods ---

  // PaymentHistory sistemine uygun chart yönetimi
  destroyExistingCharts(): void {
    if (this.expenseDistributionChart) {
      try {
        this.expenseDistributionChart.destroy();
      } catch (e) {
        console.warn('Error destroying expense distribution chart:', e);
      }
      this.expenseDistributionChart = null;
    }

    if (this.monthlyExpenseChart) {
      try {
        this.monthlyExpenseChart.destroy();
      } catch (e) {
        console.warn('Error destroying monthly expense chart:', e);
      }
      this.monthlyExpenseChart = null;
    }

    // Chart.js registry'den temizle
    try {
      const expenseDistributionElement = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
      if (expenseDistributionElement) {
        const existingChart = (Chart as any).getChart(expenseDistributionElement);
        if (existingChart) {
          existingChart.destroy();
        }
      }
    } catch (e) {
      console.warn('Error cleaning expense distribution chart from registry:', e);
    }

    try {
      const monthlyExpenseElement = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;
      if (monthlyExpenseElement) {
        const existingChart = (Chart as any).getChart(monthlyExpenseElement);
        if (existingChart) {
          existingChart.destroy();
        }
      }
    } catch (e) {
      console.warn('Error cleaning monthly expense chart from registry:', e);
    }
  }

  initializeCharts(): void {
    if (this.isInitializingCharts) {
      return;
    }

    this.isInitializingCharts = true;

    const expenseDistributionElement = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
    const monthlyExpenseElement = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;

    if (!expenseDistributionElement || !monthlyExpenseElement) {
      console.warn('Chart elements not found in DOM, retrying...', this.chartInitRetryCount + 1);

      if (this.chartInitRetryCount < this.MAX_CHART_INIT_RETRIES) {
        this.chartInitRetryCount++;
        this.isInitializingCharts = false;

        setTimeout(() => {
          this.initializeCharts();
        }, 200);
        return;
      } else {
        console.error('Max chart initialization retries reached, giving up');
        this.isInitializingCharts = false;
        this.chartInitRetryCount = 0;
        return;
      }
    }

    try {
      // Chart.js'den mevcut chart instance'larını temizle
      this.destroyExistingCharts();

      // Expense distribution chart
      this.createExpenseDistributionChart();

      // Monthly expense trend chart - sadece ilk kez yükle
      if (!this.monthlyExpenseChart) {
        this.loadMonthlyExpenseData();
      } else {
        // Chart zaten varsa sadece yeniden oluştur
        this.createMonthlyExpenseChart();
      }

      // Başarılı initialization sonrası retry count'u sıfırla
      this.chartInitRetryCount = 0;
      this.isInitializingCharts = false;

    } catch (error) {
      console.error('Chart initialization error:', error);
      this.isInitializingCharts = false;

      if (this.chartInitRetryCount < this.MAX_CHART_INIT_RETRIES) {
        this.chartInitRetryCount++;
        setTimeout(() => {
          this.initializeCharts();
        }, 500);
      } else {
        console.error('Max chart initialization retries reached after error, giving up');
        this.chartInitRetryCount = 0;
      }
    }
  }

  createExpenseDistributionChart(): void {
    const expenseDistributionElement = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
    if (!expenseDistributionElement) {
      console.warn('Expense distribution chart element not found');
      return;
    }

    // Pagination verilerini kullan
    const expensesToAnalyze = this.paginatedExpenses.data.length > 0 ? this.paginatedExpenses.data : [];

    if (expensesToAnalyze.length === 0) {
      this.showEmptyExpenseDistributionChart();
      return;
    }

    const typeData = expensesToAnalyze.reduce((acc, expense) => {
      const type = expense.expenseType || 'Diğer';
      acc[type] = (acc[type] || 0) + expense.amount;
      return acc;
    }, {} as { [key: string]: number });

    try {
      const expenseChartConfig = this.chartUtils.getExpenseStatsConfig(typeData) as ChartConfiguration;

      // Eğer chart zaten varsa, önce güvenli bir şekilde destroy et
      if (this.expenseDistributionChart) {
        try {
          this.expenseDistributionChart.destroy();
        } catch (e) {
          console.warn('Error destroying expense distribution chart:', e);
        }
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(expenseDistributionElement);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning expense distribution chart from registry:', e);
      }

      this.expenseDistributionChart = new Chart(expenseDistributionElement, expenseChartConfig);

    } catch (error) {
      console.error('Expense distribution chart creation error:', error);
      this.showEmptyExpenseDistributionChart();
    }
  }

  showEmptyExpenseDistributionChart(): void {
    const expenseDistributionElement = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
    if (!expenseDistributionElement) {
      console.warn('Expense distribution chart element not found');
      return;
    }

    try {
      const emptyData = {
        'Veri Yok': 1
      };

      const expenseChartConfig = this.chartUtils.getExpenseStatsConfig(emptyData) as ChartConfiguration;

      // Eğer chart zaten varsa, önce güvenli bir şekilde destroy et
      if (this.expenseDistributionChart) {
        try {
          this.expenseDistributionChart.destroy();
        } catch (e) {
          console.warn('Error destroying expense distribution chart in showEmpty:', e);
        }
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(expenseDistributionElement);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning expense distribution chart from registry in showEmpty:', e);
      }

      this.expenseDistributionChart = new Chart(expenseDistributionElement, expenseChartConfig);

    } catch (error) {
      console.error('Error creating empty expense distribution chart:', error);
    }
  }

  // Monthly expense data property
  monthlyExpenseData: number[] = Array(12).fill(0);

  loadMonthlyExpenseData(): void {
    // Seçilen yılın aylık gider trendini al
    const yearToLoad = this.selectedYear || new Date().getFullYear();

    console.log('Loading monthly expense data for year:', yearToLoad);

    // Monthly expense API çağrısını yap
    this.expenseService.getMonthlyExpense(yearToLoad).subscribe({
      next: (response) => {
        console.log('Monthly expense API response:', response);

        if (response.success && response.data) {
          // Backend MonthlyExpenseDto formatında veri döndürüyor
          const monthlyExpenseDto = response.data;
          console.log('Monthly expense DTO:', monthlyExpenseDto);

          // monthlyExpense array'ini al
          let monthlyData = monthlyExpenseDto.monthlyExpense || [];

          // Eğer veri array değilse, boş array oluştur
          if (!Array.isArray(monthlyData)) {
            console.warn('Monthly expense data is not an array:', monthlyData);
            monthlyData = Array(12).fill(0);
          }

          // 12 aylık veri olduğundan emin ol
          if (monthlyData.length !== 12) {
            console.warn('Monthly expense data length is not 12:', monthlyData.length);
            const fixedData = Array(12).fill(0);
            for (let i = 0; i < Math.min(monthlyData.length, 12); i++) {
              fixedData[i] = Number(monthlyData[i]) || 0;
            }
            monthlyData = fixedData;
          } else {
            // Decimal değerleri number'a çevir
            monthlyData = monthlyData.map((value: any) => Number(value) || 0);
          }

          // Veriyi kaydet
          this.monthlyExpenseData = monthlyData;
          console.log('Processed monthly expense data:', this.monthlyExpenseData);

          // Chart'ı oluştur
          this.createMonthlyExpenseChart();
        } else {
          console.error('Monthly expense API failed:', response);
          this.toastrService.error('Aylık gider trendi yüklenirken bir hata oluştu', 'Hata');
          this.monthlyExpenseData = Array(12).fill(0);
          this.showEmptyMonthlyExpenseChart();
        }
      },
      error: (error) => {
        console.error('Aylık gider trendi yüklenirken bir hata oluştu:', error);
        this.toastrService.error('Aylık gider trendi yüklenirken bir hata oluştu', 'Hata');
        this.monthlyExpenseData = Array(12).fill(0);
        this.showEmptyMonthlyExpenseChart();
      }
    });
  }

  createMonthlyExpenseChart(): void {
    const monthlyExpenseChartElement = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;
    if (!monthlyExpenseChartElement) {
      console.warn('Monthly expense chart element not found');
      return;
    }

    const monthlyData = {
      monthlyExpense: this.monthlyExpenseData
    };

    console.log('Creating monthly expense chart with data:', monthlyData);

    try {
      const monthlyChartConfig = this.chartUtils.getMonthlyExpenseConfig(monthlyData) as ChartConfiguration;

      // Eğer chart zaten varsa, önce güvenli bir şekilde destroy et
      if (this.monthlyExpenseChart) {
        try {
          this.monthlyExpenseChart.destroy();
        } catch (e) {
          console.warn('Error destroying existing monthly expense chart:', e);
        }
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(monthlyExpenseChartElement);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning monthly expense chart from registry:', e);
      }

      this.monthlyExpenseChart = new Chart(monthlyExpenseChartElement, monthlyChartConfig);
      console.log('Monthly expense chart created successfully');

    } catch (error) {
      console.error('Monthly expense chart creation error:', error);
      this.showEmptyMonthlyExpenseChart();
    }
  }

  showEmptyMonthlyExpenseChart(): void {
    const monthlyExpenseChartElement = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;
    if (!monthlyExpenseChartElement) {
      console.warn('Monthly expense chart element not found');
      return;
    }

    try {
      const emptyData = {
        monthlyExpense: Array(12).fill(0)
      };

      const monthlyChartConfig = this.chartUtils.getMonthlyExpenseConfig(emptyData) as ChartConfiguration;

      // Eğer chart zaten varsa, önce güvenli bir şekilde destroy et
      if (this.monthlyExpenseChart) {
        try {
          this.monthlyExpenseChart.destroy();
        } catch (e) {
          console.warn('Error destroying monthly expense chart in showEmpty:', e);
        }
      }

      // Chart.js registry'den temizle
      try {
        const existingChart = (Chart as any).getChart(monthlyExpenseChartElement);
        if (existingChart) {
          existingChart.destroy();
        }
      } catch (e) {
        console.warn('Error cleaning monthly expense chart from registry in showEmpty:', e);
      }

      this.monthlyExpenseChart = new Chart(monthlyExpenseChartElement, monthlyChartConfig);

    } catch (error) {
      console.error('Error creating empty monthly expense chart:', error);
    }
  }

  updateCharts(): void {
    setTimeout(() => {
      // Chart initialization işlemi devam ediyorsa, bekle
      if (this.isInitializingCharts) {
        setTimeout(() => this.updateCharts(), 200);
        return;
      }

      try {
        // Sadece expense distribution chart'ı güncelle (veri değiştiğinde)
        this.createExpenseDistributionChart();

        // Monthly chart'ı sadece mevcut veriyle güncelle, yeni API çağrısı yapma
        if (this.monthlyExpenseChart && this.monthlyExpenseData) {
          this.createMonthlyExpenseChart();
        }
      } catch (error) {
        console.warn('Error updating charts:', error);
        // Sadece hata durumunda yeniden initialize et
        this.initializeCharts();
      }
    }, 100);
  }







  // Trend göstergeleri kaldırıldı







  // Favori filtre metotları kaldırıldı





  // PaymentHistory benzeri metodlar
  initializeYearFilter() {
    const currentYear = new Date().getFullYear();

    // 2025'ten başlayarak sadece mevcut yıla kadar (dinamik)
    this.availableYears = [];
    const startYear = 2025;
    const endYear = currentYear; // Sadece mevcut yıla kadar

    for (let year = startYear; year <= endYear; year++) {
      this.availableYears.push(year);
    }

    // Eğer mevcut yıl 2025'ten küçükse (test durumu), 2025'i ekle
    if (currentYear < 2025) {
      this.availableYears = [2025];
      this.selectedYear = 2025;
    }
  }

  /**
   * Kartların üstünde gösterilecek tarih aralığı bilgisini döndürür
   */
  getDateRangeText(): string {
    // Aktif filtreleri kullan (sadece arama yapıldığında güncellenir)
    // Eğer aktif filtre yoksa, varsayılan olarak mevcut ayın 1'inden bugüne kadar gösteriliyor
    if (!this.activeStartDate && !this.activeEndDate) {
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const today = new Date();

      // Eğer ayın ilk günündeyse sadece "Bugün" göster
      if (monthStart.getDate() === today.getDate() &&
          monthStart.getMonth() === today.getMonth() &&
          monthStart.getFullYear() === today.getFullYear()) {
        return 'Bugün';
      }

      // Mevcut ayın adını al
      const monthName = monthStart.toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' });

      // Eğer bugün ayın son günüyse "Tüm [Ay Adı]" göster
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      if (today.getDate() === monthEnd.getDate()) {
        return `Tüm ${monthName}`;
      }

      // Aksi halde "1 - [Bugünün günü] [Ay Adı]" göster
      return `1 - ${today.getDate()} ${monthName}`;
    }

    const formatDate = (dateStr: string): string => {
      const date = new Date(dateStr);
      return date.toLocaleDateString('tr-TR', {
        day: '2-digit',
        month: 'long',
        year: 'numeric'
      });
    };

    if (this.activeStartDate && this.activeEndDate) {
      const startFormatted = formatDate(this.activeStartDate);
      const endFormatted = formatDate(this.activeEndDate);

      // Aynı ay içindeyse daha kısa göster
      const startDate = new Date(this.activeStartDate);
      const endDate = new Date(this.activeEndDate);

      if (startDate.getMonth() === endDate.getMonth() &&
          startDate.getFullYear() === endDate.getFullYear()) {
        return `${startDate.getDate()} - ${endDate.getDate()} ${endDate.toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' })}`;
      }

      return `${startFormatted} - ${endFormatted}`;
    }

    if (this.activeStartDate) {
      return `${formatDate(this.activeStartDate)} ve sonrası`;
    }

    if (this.activeEndDate) {
      return `${formatDate(this.activeEndDate)} ve öncesi`;
    }

    return 'Tüm Zamanlar';
  }

  /**
   * Gider dağılımı grafiği için başlık döndürür
   */
  getExpenseDistributionTitle(): string {
    const dateInfo = this.getDateRangeText();
    // Her zaman tarih aralığını göster
    return `Gider Dağılımı (${dateInfo})`;
  }

  /**
   * Aylık gider trendi grafiği için başlık döndürür
   */
  getMonthlyTrendTitle(): string {
    return `Aylık Gider Trendi (${this.selectedYear})`;
  }

  /**
   * Yıl değiştiğinde aylık trend grafiğini günceller
   */
  onYearChange(year: number): void {
    this.selectedYear = year;
    console.log('Year changed to:', year);
    // Sadece veriyi yükle, chart zaten var
    this.loadMonthlyExpenseData();
  }

  // PaymentHistory benzeri veri yükleme metodları
  loadExpenseTotals(): void {
    // Aktif filtreleri kontrol et
    const filtersActive = this.hasActiveFilters();

    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (filtersActive) {
      // Filtre varsa, filtreleme sonucunu kullan
      if (this.filterState.startDate) {
        startDate = new Date(this.filterState.startDate);

        // Eğer başlangıç tarihi var ama bitiş tarihi yoksa, bitiş tarihi bugün olsun
        if (!this.filterState.endDate) {
          endDate = new Date(); // Bugünün tarihi
        }
      }

      if (this.filterState.endDate) {
        endDate = new Date(this.filterState.endDate);
      }
    } else {
      // Filtre yoksa, mevcut ayın 1'inden bugüne kadar olan verileri kullan
      const now = new Date();
      startDate = new Date(now.getFullYear(), now.getMonth(), 1); // Ayın ilk günü
      endDate = new Date(); // Bugün
    }

    const parameters = {
      searchText: this.filterState.searchText,
      startDate: startDate,
      endDate: endDate,
      expenseType: this.filterState.expenseType,
      minAmount: this.filterState.minAmount || undefined,
      maxAmount: this.filterState.maxAmount || undefined
    };

    this.expenseService.getExpenseTotals(parameters).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.totalExpenseAmount = response.data.total || 0;
          // Dinamik gider türleri
          this.expenseTypeBreakdown = response.data.expenseTypeBreakdown || {};

          // Grafikleri güncelle
          this.updateCharts();
        }
      },
      error: (error) => {
        console.error('Gider toplamları yüklenirken hata:', error);
        this.toastrService.error('Gider toplamları yüklenemedi', 'Hata');
      }
    });
  }



  // Dinamik kartlar için metodlar
  getExpenseTypeCardClass(expenseType: string): string {
    const typeLower = expenseType.toLowerCase();

    if (typeLower.includes('kira')) return 'total-cash';
    if (typeLower.includes('elektrik') || typeLower.includes('su') || typeLower.includes('fatura')) return 'total-credit-card';
    if (typeLower.includes('maaş') || typeLower.includes('personel')) return 'total-transfer';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'total-maintenance';

    return 'total-other';
  }

  getExpenseTypeIcon(expenseType: string | null | undefined): string {
    if (!expenseType) return 'fas fa-question-circle';
    const typeLower = expenseType.toLowerCase();

    if (typeLower.includes('kira')) return 'fas fa-home';
    if (typeLower.includes('elektrik')) return 'fas fa-bolt';
    if (typeLower.includes('su')) return 'fas fa-tint';
    if (typeLower.includes('doğalgaz')) return 'fas fa-fire';
    if (typeLower.includes('internet')) return 'fas fa-wifi';
    if (typeLower.includes('maaş') || typeLower.includes('personel')) return 'fas fa-users';
    if (typeLower.includes('malzeme')) return 'fas fa-boxes';
    if (typeLower.includes('temizlik')) return 'fas fa-broom';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'fas fa-tools';
    if (typeLower.includes('ofis')) return 'fas fa-building';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'fas fa-file-invoice-dollar';

    return 'fas fa-ellipsis-h';
  }

  getExpenseTypeAmount(expenseType: string): number {
    return this.expenseTypeBreakdown[expenseType] || 0;
  }

  // Grid sistemi için responsive column class
  // Bu metod artık kullanılmıyor - CSS Grid kullanıyoruz
  getCardColumnClass(): string {
    return 'expense-card-wrapper';
  }

  // Container için grid style'ı döndürür
  getCardsContainerStyle(): any {
    const totalCards = this.expenseTypes.length + 1;

    return {
      'display': 'grid',
      'grid-template-columns': this.getGridTemplateColumns(totalCards),
      'gap': '1rem',
      'width': '100%',
      'align-items': 'stretch',
      'justify-items': 'stretch'
    };
  }

  // Container için CSS class'ları döndürür
  getCardsContainerClass(): string {
    const totalCards = this.expenseTypes.length + 1;
    let classes = 'expense-cards-container';

    if (totalCards === 7) {
      classes += ' seven-cards-layout';
    } else if (totalCards === 9) {
      classes += ' nine-cards-layout';
    } else if (totalCards === 11) {
      classes += ' eleven-cards-layout';
    } else if (totalCards === 13) {
      classes += ' thirteen-cards-layout';
    }

    return classes;
  }

  private getGridTemplateColumns(totalCards: number): string {
    // Responsive grid - her kart sayısı için optimize edilmiş layout
    if (totalCards === 1) {
      return '1fr';
    } else if (totalCards === 2) {
      return 'repeat(2, 1fr)';
    } else if (totalCards === 3) {
      return 'repeat(3, 1fr)';
    } else if (totalCards === 4) {
      return 'repeat(4, 1fr)';
    } else if (totalCards === 5) {
      return 'repeat(5, 1fr)';
    } else if (totalCards === 6) {
      return 'repeat(6, 1fr)';
    } else if (totalCards === 7) {
      return 'repeat(6, 1fr)'; // 6+1 layout: 6 kart üstte, 1 kart altta
    } else if (totalCards === 8) {
      return 'repeat(4, 1fr)'; // 4x2 layout: 4 kart üstte, 4 kart altta
    } else if (totalCards === 9) {
      return 'repeat(4, 1fr)'; // 4+4+1 layout: 4 kart üstte, 4 kart ortada, 1 kart altta
    } else if (totalCards === 10) {
      return 'repeat(5, 1fr)'; // 5x2 layout: 5 kart üstte, 5 kart altta
    } else if (totalCards === 11) {
      return 'repeat(5, 1fr)'; // 5+5+1 layout
    } else if (totalCards === 12) {
      return 'repeat(4, 1fr)'; // 4x3 layout: 4 kart her satırda
    } else if (totalCards === 13) {
      return 'repeat(4, 1fr)'; // 4+4+4+1 layout
    } else {
      return 'repeat(6, 1fr)'; // 6+ sütun
    }
  }
}