<div class="modern-modal-content fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isSubmitting">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <h5 class="mb-3">Üyelik Türü Güncelle</h5>
  
  <form [formGroup]="updateForm" (ngSubmit)="updateMembershipType()">
    <div class="form-section">
      <h6 class="section-title">Branş Bilgileri</h6>
      
      <div class="modern-form-group">
        <label for="branch" class="modern-form-label">Branş</label>
        <select id="branch" formControlName="branch" class="modern-form-control">
          <option value="">Seçiniz</option>
          <option value="Fitness">Fitness</option>
          <option value="Boks">Boks</option>
          <option value="Muay Thai">Muay Thai</option>
          <option value="MMA">MMA</option>
          <option value="Karate">Karate</option>
          <option value="Judo">Judo</option>
          <option value="Güreş"><PERSON><PERSON><PERSON>ş</option>
          <option value="Pilates">Pilates</option>
          <option value="Yoga">Yoga</option>
          <option value="KickBoks">KickBoks</option>
          <option value="Aerobik">Aerobik</option>
          <option value="CrossFit">CrossFit</option>
        </select>
        <small class="text-danger" *ngIf="updateForm.get('branch')?.invalid && updateForm.get('branch')?.touched">
          Branş seçimi zorunludur
        </small>
      </div>

      <div class="modern-form-group">
        <label for="typeName" class="modern-form-label">Paket İsmi</label>
        <input
          type="text"
          id="typeName"
          formControlName="typeName"
          class="modern-form-control"
          placeholder="1 AY, 1 AY ÖĞRENCİ vb."
        />
        <small class="text-danger" *ngIf="updateForm.get('typeName')?.invalid && updateForm.get('typeName')?.touched">
          Paket ismi zorunludur
        </small>
      </div>
    </div>
    
    <div class="form-section">
      <h6 class="section-title">Süre ve Fiyat</h6>
      
      <div class="modern-form-group">
        <label class="modern-form-label">Geçerlilik Süresi</label>
        <div class="d-flex validity-selects">
          <select id="year" formControlName="year" class="modern-form-control me-2">
            <option *ngFor="let i of [].constructor(11); let year = index" [value]="year">
              {{ year }} Yıl
            </option>
          </select>
          
          <select id="month" formControlName="month" class="modern-form-control me-2">
            <option *ngFor="let i of [].constructor(13); let month = index" [value]="month">
              {{ month }} Ay
            </option>
          </select>
          
          <select id="day" formControlName="day" class="modern-form-control day-select">
            <option *ngFor="let i of [].constructor(32); let day = index" [value]="day">
              {{ day }} Gün
            </option>
          </select>
        </div>
        <small class="text-danger" *ngIf="(updateForm.get('year')?.value === 0 && updateForm.get('month')?.value === 0 && updateForm.get('day')?.value === 0) && (updateForm.get('year')?.touched || updateForm.get('month')?.touched || updateForm.get('day')?.touched)">
          Geçerlilik süresi en az 1 gün olmalıdır
        </small>
      </div>

      <div class="modern-form-group">
        <label for="price" class="modern-form-label">Ücret</label>
        <div class="input-group">
          <span class="input-group-text">₺</span>
          <input
            type="number"
            id="price"
            formControlName="price"
            class="modern-form-control"
            min="0"
            placeholder="Ücret"
          />
        </div>
        <small class="text-danger" *ngIf="updateForm.get('price')?.invalid && updateForm.get('price')?.touched">
          Ücret bilgisi zorunludur
        </small>
      </div>
    </div>
    
    <div class="modern-modal-footer">
      <button 
        type="button" 
        class="modern-btn modern-btn-secondary" 
        (click)="onNoClick()"
      >
        İptal
      </button>
      <button 
        type="submit" 
        class="modern-btn modern-btn-primary" 
        [disabled]="!updateForm.valid || isSubmitting"
        #updateButton
      >
        {{ isSubmitting ? 'Güncelleniyor...' : 'Güncelle' }}
      </button>
    </div>
  </form>
</div>
