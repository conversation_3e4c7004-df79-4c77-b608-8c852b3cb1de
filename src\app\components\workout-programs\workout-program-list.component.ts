import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { faEdit, faTrashAlt, faEye, faPlus, faSearch, faFilter, faDumbbell } from '@fortawesome/free-solid-svg-icons';

import { WorkoutProgramService } from '../../services/workout-program.service';
import { AuthService } from '../../services/auth.service';
import { DialogService } from '../../services/dialog.service';
import { 
  WorkoutProgramTemplateList, 
  EXPERIENCE_LEVELS, 
  TARGET_GOALS 
} from '../../models/workout-program.models';

@Component({
  selector: 'app-workout-program-list',
  templateUrl: './workout-program-list.component.html',
  styleUrls: ['./workout-program-list.component.css'],
  standalone: false
})
export class WorkoutProgramListComponent implements OnInit, OnDestroy {
  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faEye = faEye;
  faPlus = faPlus;
  faSearch = faSearch;
  faFilter = faFilter;
  faDumbbell = faDumbbell;

  // Data
  workoutPrograms: WorkoutProgramTemplateList[] = [];
  
  // Pagination
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  pageSize = 20;

  // Filters
  selectedExperienceLevel: string = '';
  selectedTargetGoal: string = '';
  searchText = '';
  
  // UI State
  isLoading = false;
  
  // User permissions
  isOwner = false;
  isAdmin = false;

  // Constants
  experienceLevels = EXPERIENCE_LEVELS;
  targetGoals = TARGET_GOALS;

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private workoutProgramService: WorkoutProgramService,
    private authService: AuthService,
    private toastrService: ToastrService,
    public dialog: MatDialog,
    private dialogService: DialogService,
    private router: Router
  ) {
    this.searchSubject.pipe(
      debounceTime(750),
      takeUntil(this.destroy$)
    ).subscribe(searchValue => {
      this.searchText = searchValue;
      this.currentPage = 1;
      this.loadWorkoutPrograms();
    });
  }

  ngOnInit(): void {
    this.checkUserPermissions();
    this.loadWorkoutPrograms();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  checkUserPermissions(): void {
    this.isOwner = this.authService.hasRole('owner');
    this.isAdmin = this.authService.hasRole('admin');
  }

  loadWorkoutPrograms(): void {
    this.isLoading = true;

    this.workoutProgramService.getAll().subscribe({
      next: (response) => {
        if (response.success) {
          // Client-side filtering (backend'de filtreleme yoksa)
          let filteredPrograms = response.data;

          // Arama filtresi
          if (this.searchText) {
            filteredPrograms = filteredPrograms.filter(program => 
              program.programName.toLowerCase().includes(this.searchText.toLowerCase()) ||
              (program.description && program.description.toLowerCase().includes(this.searchText.toLowerCase()))
            );
          }

          // Deneyim seviyesi filtresi
          if (this.selectedExperienceLevel) {
            filteredPrograms = filteredPrograms.filter(program => 
              program.experienceLevel === this.selectedExperienceLevel
            );
          }

          // Hedef filtresi
          if (this.selectedTargetGoal) {
            filteredPrograms = filteredPrograms.filter(program => 
              program.targetGoal === this.selectedTargetGoal
            );
          }

          // Sayfalama
          this.totalItems = filteredPrograms.length;
          this.totalPages = Math.ceil(this.totalItems / this.pageSize);
          
          const startIndex = (this.currentPage - 1) * this.pageSize;
          const endIndex = startIndex + this.pageSize;
          this.workoutPrograms = filteredPrograms.slice(startIndex, endIndex);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading workout programs:', error);
        this.toastrService.error('Antrenman programları yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearch(event: any): void {
    const searchValue = event.target.value;
    this.searchSubject.next(searchValue);
  }

  onExperienceLevelChange(): void {
    this.currentPage = 1;
    this.loadWorkoutPrograms();
  }

  onTargetGoalChange(): void {
    this.currentPage = 1;
    this.loadWorkoutPrograms();
  }

  hasActiveFilters(): boolean {
    return !!(this.searchText || this.selectedExperienceLevel || this.selectedTargetGoal);
  }

  clearFilters(): void {
    this.selectedExperienceLevel = '';
    this.selectedTargetGoal = '';
    this.searchText = '';
    this.currentPage = 1;
    this.loadWorkoutPrograms();
  }

  openAddProgramModal(): void {
    this.router.navigate(['/workout-programs/add']);
  }

  openEditProgramModal(program: WorkoutProgramTemplateList): void {
    this.router.navigate(['/workout-programs/edit', program.workoutProgramTemplateID]);
  }

  viewProgramDetail(program: WorkoutProgramTemplateList): void {
    this.router.navigate(['/workout-programs/detail', program.workoutProgramTemplateID]);
  }

  deleteProgram(program: WorkoutProgramTemplateList): void {
    this.dialogService.confirm(
      'Program Silme Onayı',
      `"${program.programName}" adlı antrenman programını silmek istediğinizden emin misiniz?`,
      'Bu işlem geri alınamaz!'
    ).subscribe((result: boolean) => {
      if (result) {
        this.workoutProgramService.delete(program.workoutProgramTemplateID).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Antrenman programı başarıyla silindi', 'Başarılı');
              this.loadWorkoutPrograms();
            } else {
              this.toastrService.error(response.message || 'Program silinirken hata oluştu', 'Hata');
            }
          },
          error: (error) => {
            console.error('Error deleting workout program:', error);
            this.toastrService.error('Program silinirken hata oluştu', 'Hata');
          }
        });
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadWorkoutPrograms();
    }
  }

  getPaginationRange(): number[] {
    const range = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }

  getExperienceLevelBadgeClass(level?: string): string {
    switch (level) {
      case 'Başlangıç': return 'modern-badge-success';
      case 'Orta': return 'modern-badge-warning';
      case 'İleri': return 'modern-badge-danger';
      default: return 'modern-badge-secondary';
    }
  }

  getTargetGoalBadgeClass(goal?: string): string {
    switch (goal) {
      case 'Kilo Alma': return 'modern-badge-info';
      case 'Kilo Verme': return 'modern-badge-warning';
      case 'Kas Yapma': return 'modern-badge-primary';
      case 'Kondisyon': return 'modern-badge-success';
      case 'Güç': return 'modern-badge-danger';
      default: return 'modern-badge-secondary';
    }
  }

  formatDate(date?: Date): string {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('tr-TR');
  }
}
