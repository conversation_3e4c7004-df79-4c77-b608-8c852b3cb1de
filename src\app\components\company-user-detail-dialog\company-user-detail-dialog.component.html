<!-- Loading Overlay -->
<div class="loading-overlay" *ngIf="isLoading">
  <div class="spinner-container">
    <div class="modern-spinner"></div>
    <p class="mt-3">Kullanıcı detayları yükleniyor</p>
  </div>
</div>

<div class="dialog-container" [class.content-blur]="isLoading">
  <!-- Dialog Header -->
  <div class="dialog-header">
    <div class="user-header-info" *ngIf="companyUser">
      <div class="avatar-circle-xl" [style.backgroundColor]="getAvatarColor(companyUser.name)">
        {{ getInitials(companyUser.name) }}
      </div>
      <div class="user-header-details">
        <h4 class="user-name">
          {{ companyUser.name }}
          <!-- Readonly modu göstergesi -->
          <span *ngIf="isReadonly" class="modern-badge modern-badge-info ms-2">
            <i class="fas fa-eye me-1"></i>
            Detay <PERSON>
          </span>
        </h4>
        <p class="user-email">{{ companyUser.email }}</p>
        <span class="modern-badge" [ngClass]="getStatusClass()">
          {{ getStatusText() }}
        </span>
      </div>
    </div>
    <button class="btn-close" (click)="onCancel()" type="button">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Tab Navigation -->
  <div class="tab-navigation">
    <button 
      class="tab-button" 
      [class.active]="activeTab === 'personal'"
      (click)="setActiveTab('personal')"
    >
      <i class="fas fa-user me-2"></i>
      Kişisel Bilgiler
    </button>
    <button 
      class="tab-button" 
      [class.active]="activeTab === 'company'"
      (click)="setActiveTab('company')"
    >
      <i class="fas fa-building me-2"></i>
      Şirket Bilgileri
    </button>
    <button 
      class="tab-button" 
      [class.active]="activeTab === 'statistics'"
      (click)="setActiveTab('statistics')"
    >
      <i class="fas fa-chart-bar me-2"></i>
      İstatistikler
    </button>
  </div>

  <!-- Dialog Content -->
  <div class="dialog-content" [class.readonly-mode]="isReadonly">
    <form [formGroup]="companyUserForm" (ngSubmit)="onSubmit()">
      
      <!-- Personal Information Tab -->
      <div class="tab-content" *ngIf="activeTab === 'personal'">

        <div class="form-section">
          <h6 class="section-title">
            <i class="fas fa-user me-2"></i>
            Kişisel Bilgiler
          </h6>

          <div class="row">
            <div class="col-md-12 mb-3">
              <label class="form-label required">İsim Soyisim</label>
              <input
                type="text"
                class="form-control modern-input"
                formControlName="name"
                placeholder="İsim ve soyisim girin"
                [class.is-invalid]="companyUserForm.get('name')?.invalid && companyUserForm.get('name')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('name') }}
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label required">Email</label>
              <input
                type="email"
                class="form-control modern-input"
                formControlName="email"
                placeholder="<EMAIL>"
                [class.is-invalid]="companyUserForm.get('email')?.invalid && companyUserForm.get('email')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('email') }}
              </div>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label required">Telefon</label>
              <input
                type="tel"
                class="form-control modern-input"
                formControlName="phoneNumber"
                placeholder="05xxxxxxxxx (11 haneli)"
                maxlength="11"
                (input)="onPhoneNumberInput($event, 'phoneNumber')"
                [class.is-invalid]="companyUserForm.get('phoneNumber')?.invalid && companyUserForm.get('phoneNumber')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('phoneNumber') }}
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label required">Şehir</label>
              <select
                class="form-select modern-select"
                formControlName="cityID"
                (change)="onCityChange()"
                [class.is-invalid]="companyUserForm.get('cityID')?.invalid && companyUserForm.get('cityID')?.touched"
              >
                <option value="">Şehir seçin</option>
                <option *ngFor="let city of cities" [value]="city.cityID">
                  {{ city.cityName }}
                </option>
              </select>
              <div class="invalid-feedback">
                {{ getFieldError('cityID') }}
              </div>
            </div>
            <div class="col-md-6 mb-3">
              <label class="form-label required">İlçe</label>
              <select
                class="form-select modern-select"
                formControlName="townID"
                [disabled]="!companyUserForm.get('cityID')?.value"
                [class.is-invalid]="companyUserForm.get('townID')?.invalid && companyUserForm.get('townID')?.touched"
              >
                <option value="">İlçe seçin</option>
                <option *ngFor="let town of towns" [value]="town.townID">
                  {{ town.townName }}
                </option>
              </select>
              <div class="invalid-feedback">
                {{ getFieldError('townID') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Company Information Tab -->
      <div class="tab-content" *ngIf="activeTab === 'company'">


        <div class="form-section">
          <h6 class="section-title">
            <i class="fas fa-building me-2"></i>
            Şirket Bilgileri
          </h6>

          <div class="row">
            <div class="col-md-12 mb-3">
              <label class="form-label required">Şirket Adı</label>
              <input
                type="text"
                class="form-control modern-input"
                formControlName="companyName"
                placeholder="Şirket adını girin"
                [class.is-invalid]="companyUserForm.get('companyName')?.invalid && companyUserForm.get('companyName')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('companyName') }}
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12 mb-3">
              <label class="form-label required">Şirket Telefonu</label>
              <input
                type="tel"
                class="form-control modern-input"
                formControlName="companyPhone"
                placeholder="05xxxxxxxxx (11 haneli)"
                maxlength="11"
                (input)="onPhoneNumberInput($event, 'companyPhone')"
                [class.is-invalid]="companyUserForm.get('companyPhone')?.invalid && companyUserForm.get('companyPhone')?.touched"
              />
              <div class="invalid-feedback">
                {{ getFieldError('companyPhone') }}
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12 mb-3">
              <label class="form-label required">Şirket Adresi</label>
              <textarea
                class="form-control modern-textarea"
                formControlName="companyAddress"
                rows="3"
                placeholder="Şirket adresini girin (en az 10 karakter)"
                [class.is-invalid]="companyUserForm.get('companyAddress')?.invalid && companyUserForm.get('companyAddress')?.touched"
              ></textarea>
              <div class="invalid-feedback">
                {{ getFieldError('companyAddress') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Statistics Tab -->
      <div class="tab-content statistics-tab" *ngIf="activeTab === 'statistics'">
        <div class="statistics-grid" *ngIf="companyUser">
          <div class="stat-card">
            <div class="stat-icon stat-icon-members">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <h3>{{ companyUser.totalMembers }}</h3>
              <p>Toplam Üye</p>
            </div>
          </div>
        </div>

        <!-- ID Bilgileri Bölümü -->
        <div class="user-details-section" *ngIf="companyUser">
          <h6 class="section-title">
            <i class="fas fa-hashtag me-2"></i>
            ID Bilgileri
          </h6>

          <div class="detail-row">
            <span class="detail-label">Company User ID:</span>
            <span class="detail-value modern-badge modern-badge-primary">{{ companyUser.companyUserID }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">Company ID:</span>
            <span class="detail-value modern-badge modern-badge-secondary">{{ companyUser.companyID || 'Atanmamış' }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">User ID:</span>
            <span class="detail-value modern-badge modern-badge-info">{{ companyUser.userID || 'Atanmamış' }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">Company Address ID:</span>
            <span class="detail-value modern-badge modern-badge-warning">{{ companyUser.companyAddressId || 'Atanmamış' }}</span>
          </div>

          <div class="detail-row">
            <span class="detail-label">User Company ID:</span>
            <span class="detail-value modern-badge modern-badge-success">{{ companyUser.userCompanyId || 'Atanmamış' }}</span>
          </div>
        </div>

        <div class="user-details-section" *ngIf="companyUser">
          <h6 class="section-title">
            <i class="fas fa-info-circle me-2"></i>
            Hesap Detayları
          </h6>

          <div class="detail-row">
            <span class="detail-label">Son Giriş:</span>
            <span class="detail-value">
              {{ companyUser.lastLoginDate ? (companyUser.lastLoginDate | date:'dd/MM/yyyy HH:mm') : 'Hiç giriş yapmamış' }}
            </span>
          </div>

          <div class="detail-row">
            <span class="detail-label">Kayıt Tarihi:</span>
            <span class="detail-value">
              {{ companyUser.creationDate ? (companyUser.creationDate | date:'dd/MM/yyyy HH:mm') : 'Bilinmiyor' }}
            </span>
          </div>

          <div class="detail-row">
            <span class="detail-label">Son Güncelleme:</span>
            <span class="detail-value">
              {{ companyUser.updatedDate ? (companyUser.updatedDate | date:'dd/MM/yyyy HH:mm') : 'Hiç güncellenmemiş' }}
            </span>
          </div>
        </div>
      </div>

    </form>
  </div>

  <!-- Dialog Footer -->
  <div class="dialog-footer" *ngIf="activeTab !== 'statistics'">
    <div class="footer-buttons">
      <!-- Readonly modda sadece Kapat butonu göster -->
      <button
        *ngIf="isReadonly"
        type="button"
        class="btn-dialog btn-dialog-cancel"
        (click)="onCancel()"
      >
        <i class="fas fa-times me-2"></i>
        Kapat
      </button>

      <!-- Edit modda İptal ve Güncelle butonları göster -->
      <ng-container *ngIf="!isReadonly">
        <button
          type="button"
          class="btn-dialog btn-dialog-cancel"
          (click)="onCancel()"
          [disabled]="isSubmitting"
        >
          <i class="fas fa-times me-2"></i>
          İptal
        </button>
        <button
          type="button"
          class="btn-dialog btn-dialog-save"
          (click)="onSubmit()"
          [disabled]="isSubmitting || companyUserForm.invalid"
        >
          <i *ngIf="!isSubmitting" class="fas fa-save me-2"></i>
          <div *ngIf="isSubmitting" class="spinner-sm me-2"></div>
          {{ isSubmitting ? 'Güncelleniyor...' : 'Güncelle' }}
        </button>
      </ng-container>
    </div>
  </div>
</div>
