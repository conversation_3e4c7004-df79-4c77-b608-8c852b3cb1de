/* QR Code Component Styles - Using project's design system */

.gym-container {
  max-width: 100%;
  margin: auto;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Arial', sans-serif;
  transition: all var(--transition-speed) var(--transition-timing);
}

.gym-panel {
  width: 100%;
  max-width: 380px;
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-speed) var(--transition-timing);
  animation: zoomIn 0.5s var(--transition-timing);
  position: relative;
  overflow: hidden;
}

.gym-panel::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: var(--primary);
  opacity: 0.1;
  border-radius: 50%;
  z-index: 0;
}

.gym-panel::after {
  content: '';
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: var(--primary-dark);
  opacity: 0.1;
  border-radius: 50%;
  z-index: 0;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.gym-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
  margin: 0 auto;
}

.input-area {
  margin-bottom: var(--spacing-md);
  display: flex;
  position: relative;
  z-index: 1;
}

.input-area input {
  flex-grow: 1;
  padding: 14px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-pill);
  font-size: 15px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  outline: none;
  transition: all var(--transition-speed) var(--transition-timing);
  box-shadow: var(--shadow-sm);
}

.input-area input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.input-area button {
  padding: 12px 18px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius-pill);
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  margin-left: 10px;
  box-shadow: var(--shadow-sm);
}

.input-area button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.input-area button:active {
  transform: translateY(1px);
}

.input-area button:disabled {
  background: linear-gradient(135deg, #a0a0a0 0%, #7a7a7a 100%);
  cursor: not-allowed;
  box-shadow: none;
}

.message-area {
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: 14px;
  text-align: center;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.3s var(--transition-timing);
}

.message-area.error {
  background-color: var(--danger-light);
  color: var(--danger);
}

.result-area {
  text-align: center;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.5s var(--transition-timing);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-secondary);
  box-shadow: var(--shadow-sm);
}

.result-area h2 {
  margin: 0 0 var(--spacing-md);
  font-size: 22px;
  color: var(--text-primary);
  font-weight: 600;
}

.result-area p {
  margin: 5px 0;
  font-size: 16px;
  color: var(--text-secondary);
}

.membership-info {
  margin-bottom: var(--spacing-md);
}

.remaining-days {
  color: var(--success);
  font-weight: bold;
  margin: 8px 0;
  padding: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--success-light);
}

.future-membership {
  color: var(--warning);
  font-weight: bold;
  margin: 8px 0;
  padding: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--warning-light);
}

.expired-membership {
  color: var(--danger);
  font-weight: bold;
  margin: 8px 0;
  padding: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--danger-light);
}

.expired-membership-special {
  color: #ffffff;
  font-weight: bold;
  margin: 12px 0;
  padding: 15px;
  border-radius: var(--border-radius-md);
  background-color: #ff3131;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0.5px;
  border: 2px solid #ffffff;
  animation: pulse 2s infinite;
}

.expired-text {
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 49, 49, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 49, 49, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 49, 49, 0);
  }
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: var(--spacing-md) 0;
  position: relative;
  width: 100%;
  overflow: hidden;
}

.qr-validity-timer {
  width: 100%;
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.timer-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--text-secondary);
}

.timer-countdown {
  font-weight: bold;
  color: var(--success);
}

.timer-warning {
  color: var(--danger);
  animation: blink 1s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.progress-bar-container {
  width: 100%;
  height: 6px;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-pill);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--success) 0%, var(--primary) 100%);
  border-radius: var(--border-radius-pill);
  transition: width 1s linear;
}

.progress-warning {
  background: linear-gradient(90deg, var(--danger) 0%, var(--warning) 100%);
}

.qr-code-container qrcode {
  border: 2px solid var(--primary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  background-color: white;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-speed) var(--transition-timing);
  max-width: 100%;
  margin-bottom: var(--spacing-md);
}

.qr-code-container qrcode canvas {
  max-width: 100%;
  height: auto !important;
  display: block;
}

/* QR kod hover animasyonu kaldırıldı - kullanıcı isteği üzerine */

.qr-actions {
  display: flex;
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.download-button {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, var(--success) 0%, #219150 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius-pill);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.download-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.download-button:active {
  transform: translateY(1px);
}

.qr-info-text {
  width: 100%;
  text-align: center;
  font-size: 13px;
  color: var(--text-secondary);
  padding: 8px;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px dashed var(--border-color);
}

/* Special member styles */
.special-member {
  background: linear-gradient(135deg, #ff0080, #7928ca);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 0 25px rgba(255, 105, 180, 0.5);
  border: 3px solid #ffffff;
}

.special-expired-text {
  background-color: transparent;
  color: #ffffff;
  border: 2px solid #ffffff;
  font-weight: bold;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
  padding: 15px;
  margin: 15px 0;
  font-size: 18px;
  letter-spacing: 1px;
  animation: glow 2s infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ff0080, 0 0 20px #ff0080;
  }
  to {
    text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #ff0080, 0 0 40px #ff0080;
  }
}

.special-header {
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.special-header h2 {
  color: #ffffff !important;
  font-size: 32px !important;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
  margin: 15px 0 !important;
  font-weight: 700;
  letter-spacing: 1px;
  background: rgba(255, 255, 255, 0.2);
  padding: 15px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.hearts {
  font-size: 30px;
  margin: 15px 0;
  animation: heartbeat 1.5s infinite;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.7));
}

.special-info {
  color: #ffffff !important;
  font-size: 18px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.special-member .qr-code-container qrcode {
  border: 5px solid #ffffff;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
  border-radius: 20px;
  padding: 20px;
  transform: scale(1.05);
  transition: all 0.3s ease;
}

/* Özel üye QR kod hover animasyonu kaldırıldı - kullanıcı isteği üzerine */

.special-member .download-button {
  background: linear-gradient(135deg, #00c9ff, #92fe9d);
  font-weight: bold;
  color: #333;
  font-size: 18px;
  padding: 16px 24px;
  border: 2px solid #ffffff;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  letter-spacing: 1px;
}

.special-member .download-button:hover {
  background: linear-gradient(135deg, #92fe9d, #00c9ff);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Responsive Styles */
@media (max-width: 767px) {
  .gym-container {
    padding: var(--spacing-md);
  }

  .gym-panel {
    max-width: 100%;
    padding: var(--spacing-lg);
  }

  .gym-icon {
    width: 60px;
    height: 60px;
    font-size: 28px;
  }

  .input-area {
    flex-direction: column;
    gap: 10px;
  }

  .input-area button {
    margin-left: 0;
    width: 100%;
  }

  .result-area h2 {
    font-size: 20px;
  }

  .qr-code-container qrcode {
    max-width: 100%;
    height: auto;
  }
}

@media (max-width: 480px) {
  .gym-panel {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
  }

  .gym-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .result-area {
    padding: var(--spacing-sm);
  }

  .result-area h2 {
    font-size: 18px;
  }

  .remaining-days,
  .future-membership,
  .expired-membership,
  .expired-membership-special {
    font-size: 14px;
    padding: 6px;
  }

  .download-button {
    font-size: 14px;
    padding: 12px 16px;
  }

  .special-header h2 {
    font-size: 20px !important;
  }

  .hearts {
    font-size: 20px;
  }

  .special-info {
    font-size: 16px;
  }
}

@media (max-height: 700px) {
  .gym-panel {
    max-height: 90vh;
    overflow-y: auto;
  }
}

/* Dark Mode Support */
[data-theme="dark"] .qr-code-container qrcode {
  background-color: white; /* QR code needs to stay white for readability */
}
