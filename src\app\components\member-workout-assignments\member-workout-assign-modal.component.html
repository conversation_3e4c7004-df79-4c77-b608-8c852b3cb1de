<!-- <PERSON><PERSON> Header -->
<div class="modal-header">
  <h2 class="modal-title">
    <fa-icon [icon]="faDumbbell" class="modal-icon"></fa-icon>
    {{ modalTitle }}
  </h2>
  <button 
    type="button" 
    class="modal-close-btn"
    (click)="onCancel()"
    [disabled]="isSubmitting">
    <fa-icon [icon]="faTimes"></fa-icon>
  </button>
</div>

<!-- Modal Body -->
<div class="modal-body">
  <form [formGroup]="assignmentForm" (ngSubmit)="onSubmit()">
    
    <!-- Member Selection -->
    <div class="modern-form-group">
      <label class="modern-form-label">
        <fa-icon [icon]="faUser" class="form-icon"></fa-icon>
        Üye
      </label>
      
      <!-- Edit Mode: Display member name as text -->
      <div *ngIf="isEditMode" class="member-display modern-form-control-static">
        {{ data.assignment?.memberName }}
      </div>

      <!-- Add Mode: Show member selection input -->
      <ng-container *ngIf="!isEditMode">
        <input
          type="text"
          class="modern-form-control"
          formControlName="memberID"
          placeholder="Üye adı veya telefon numarası yazın..."
          [class.is-invalid]="isFieldInvalid('memberID')"
          [matAutocomplete]="autoMember">

        <mat-autocomplete #autoMember="matAutocomplete" [displayWith]="displayMember">
          <mat-option *ngFor="let member of filteredMembers | async" [value]="member">
            {{ member.name }} - {{ member.phoneNumber }}
          </mat-option>
        </mat-autocomplete>

        <div class="invalid-feedback" *ngIf="isFieldInvalid('memberID')">
          {{ getFieldError('memberID') }}
        </div>
      </ng-container>
    </div>

    <!-- Workout Program Selection -->
    <div class="modern-form-group">
      <label class="modern-form-label required">
        <fa-icon [icon]="faDumbbell" class="form-icon"></fa-icon>
        Antrenman Programı
      </label>
      <select 
        class="modern-form-control"
        formControlName="workoutProgramTemplateID"
        [class.is-invalid]="isFieldInvalid('workoutProgramTemplateID')">
        <option value="">Program seçiniz...</option>
        <option 
          *ngFor="let program of workoutPrograms" 
          [value]="program.workoutProgramTemplateID">
          {{ program.programName }}
          <span *ngIf="program.experienceLevel"> - {{ program.experienceLevel }}</span>
          <span *ngIf="program.targetGoal"> ({{ program.targetGoal }})</span>
        </option>
      </select>
      <div class="invalid-feedback" *ngIf="isFieldInvalid('workoutProgramTemplateID')">
        {{ getFieldError('workoutProgramTemplateID') }}
      </div>
    </div>





    <!-- Notes -->
    <div class="modern-form-group">
      <label class="modern-form-label">
        <fa-icon [icon]="faStickyNote" class="form-icon"></fa-icon>
        Notlar
      </label>
      <textarea 
        class="modern-form-control"
        formControlName="notes"
        rows="3"
        placeholder="Program hakkında notlarınızı buraya yazabilirsiniz..."></textarea>
      <small class="form-text">Maksimum 1000 karakter</small>
    </div>


  </form>
</div>

<!-- Modal Footer -->
<div class="modal-footer">
  <button 
    type="button" 
    class="modern-btn modern-btn-secondary"
    (click)="onCancel()"
    [disabled]="isSubmitting">
    İptal
  </button>
  
  <button 
    type="submit" 
    class="modern-btn modern-btn-primary"
    (click)="onSubmit()"
    [disabled]="!assignmentForm.valid || isSubmitting">
    <i class="fas fa-spinner fa-spin" *ngIf="isSubmitting"></i>
    <span *ngIf="!isSubmitting">{{ isEditMode ? 'Güncelle' : 'Program Ata' }}</span>
    <span *ngIf="isSubmitting">{{ isEditMode ? 'Güncelleniyor...' : 'Atanıyor...' }}</span>
  </button>
</div>
