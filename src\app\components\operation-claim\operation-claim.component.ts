// src/app/components/operation-claim/operation-claim.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { OperationClaimService } from '../../services/operation-claim.service';
import { OperationClaim } from '../../models/operationClaim';

@Component({
    selector: 'app-operation-claim',
    templateUrl: './operation-claim.component.html',
    styleUrls: ['./operation-claim.component.css'],
    standalone: false
})
export class OperationClaimComponent implements OnInit {
  operationClaims: OperationClaim[] = [];
  filteredClaims: OperationClaim[] = [];
  claimForm: FormGroup;
  isLoading: boolean = false;
  searchTerm: string = '';
  sortColumn: string = 'name';
  sortDirection: string = 'asc';
  
  // Define system roles that cannot be deleted
  systemRoles: string[] = ['admin', 'user', 'manager', 'employee', 'customer'];

  constructor(
    private operationClaimService: OperationClaimService,
    private formBuilder: FormBuilder,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.createClaimForm();
    this.getOperationClaims();
  }

  createClaimForm() {
    this.claimForm = this.formBuilder.group({
      name: ['', Validators.required]
    });
  }

  getOperationClaims() {
    this.isLoading = true;
    this.operationClaimService.getAll().subscribe(
      response => {
        this.operationClaims = response.data;
        this.filteredClaims = [...this.operationClaims];
        this.sortRoles(this.sortColumn); // Initial sort
        this.isLoading = false;
      },
      error => {
        this.toastrService.error('Roller yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    );
  }

  // Filter roles based on search term
  filterRoles(): void {
    if (!this.searchTerm.trim()) {
      this.filteredClaims = [...this.operationClaims];
    } else {
      const term = this.searchTerm.toLowerCase().trim();
      this.filteredClaims = this.operationClaims.filter(claim => 
        claim.name.toLowerCase().includes(term)
      );
    }
    this.sortRoles(this.sortColumn); // Maintain current sort
  }

  // Sort roles by column
  sortRoles(column: string): void {
    this.sortColumn = column;
    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    
    this.filteredClaims.sort((a: any, b: any) => {
      let aValue = a[column];
      let bValue = b[column];
      
      // Handle string columns
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  // Check if a role is a system role
  isSystemRole(roleName: string): boolean {
    return this.systemRoles.includes(roleName.toLowerCase());
  }

  // Get count of system roles
  getSystemRolesCount(): number {
    return this.operationClaims.filter(claim => 
      this.isSystemRole(claim.name)
    ).length;
  }

  // Get count of custom roles
  getCustomRolesCount(): number {
    return this.operationClaims.filter(claim => 
      !this.isSystemRole(claim.name)
    ).length;
  }

  addOperationClaim() {
    if (this.claimForm.valid) {
      this.isLoading = true;
      let claimModel = Object.assign({}, this.claimForm.value);
      this.operationClaimService.add(claimModel).subscribe(
        response => {
          this.toastrService.success(response.message, 'Başarılı');
          this.getOperationClaims();
          this.claimForm.reset();
        },
        error => {
          this.toastrService.error('Rol eklenirken bir hata oluştu', 'Hata');
          this.isLoading = false;
        }
      );
    }
  }

  deleteOperationClaim(claim: OperationClaim) {
    if (confirm('Bu rolü silmek istediğinizden emin misiniz?')) {
      this.operationClaimService.delete(claim.operationClaimId).subscribe(
        response => {
          this.toastrService.success(response.message, 'Başarılı');
          this.getOperationClaims();
        },
        error => {
          this.toastrService.error('Rol silinirken bir hata oluştu', 'Hata');
        }
      );
    }
  }
}
