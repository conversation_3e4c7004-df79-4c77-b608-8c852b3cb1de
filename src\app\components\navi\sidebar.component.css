/* Sidebar Container */
.sidebar {
  width: 280px;
  height: 100%;
  background-color: var(--sidebar-bg);
  color: var(--text-primary);
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20px var(--shadow-color);
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 80px;
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  height: 80px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-container i {
  font-size: 24px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}

.logo-container:hover i {
  transform: scale(1.1);
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.5px;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .logo-text {
  opacity: 0;
}

.toggle-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background-color: var(--sidebar-hover);
  color: var(--primary-color);
  transform: scale(1.1);
}

/* Sidebar Content */
.sidebar-content {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

/* Menu Sections */
.menu-section {
  margin-bottom: 20px;
}

.menu-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 0 10px;
  position: relative;
}

.menu-header:hover {
  background-color: var(--sidebar-hover);
  transform: translateX(5px);
}

.sidebar.collapsed .menu-header {
  justify-content: center;
  padding: 12px;
  margin: 0 5px;
}

.sidebar.collapsed .menu-header:hover {
  transform: scale(1.05);
}

.menu-icon {
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(63, 55, 201, 0.1) 100%);
  margin-right: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.menu-header:hover .menu-icon {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed .menu-icon {
  margin-right: 0;
  width: 38px;
  height: 38px;
}

.menu-icon i {
  font-size: 18px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}

.menu-header:hover .menu-icon i {
  transform: scale(1.1);
}

.menu-title {
  flex: 1;
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.3px;
}

.menu-arrow {
  transition: transform 0.3s ease;
  color: var(--text-muted);
  font-size: 12px;
}

.menu-arrow.rotated {
  transform: rotate(180deg);
}

/* Menu Items */
.menu-items {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
}

.menu-items.expanded {
  max-height: 2000px; /* Increased for more items */
  transition: max-height 1s ease-in-out;
}

.sidebar.collapsed .menu-items {
  max-height: 2000px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px 10px;

  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-radius: 8px;
  margin: 4px 8px;
  position: relative;
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, var(--primary-color), transparent);
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.sidebar.collapsed .menu-item {
  padding: 10px;
  justify-content: center;
  margin: 4px;
}

.menu-item:hover {
  background-color: var(--sidebar-hover);
  color: var(--text-color);
  transform: translateX(5px);
}

.sidebar.collapsed .menu-item:hover {
  transform: scale(1.05);
}

.menu-item.active {
  background-color: var(--sidebar-active);
  color: var(--primary-color);
  font-weight: 600;
}

.menu-item.active::before {
  width: 4px;
  opacity: 1;
}

.menu-item i {
  font-size: 16px;
  width: 20px;
  text-align: center;
  margin-right: 12px;
  position: relative;
  z-index: 1;
}

.sidebar.collapsed .menu-item i {
  margin-right: 0;
  font-size: 18px;
}

/* Single menu item (not in a collapsible section) */
.menu-item.single-item {
  margin: 8px;
  background-color: var(--sidebar-hover);
  border: 1px solid var(--border-color);
}

.menu-item.single-item:hover {
  background-color: var(--sidebar-active);
  border-color: var(--primary-color);
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-profile-container {
  margin-bottom: 10px;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(63, 55, 201, 0.1) 100%);
  font-weight: 500;
  width: 100%;
}

.profile-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.2) 0%, rgba(63, 55, 201, 0.2) 100%);
}

/* Profile Image Styles */
.profile-image-container {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(63, 55, 201, 0.1) 100%);
}

/* Profil fotoğrafı */
.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  transition: all 0.3s ease;
  border: 2px solid var(--primary-color);
}

.profile-image:hover {
  transform: scale(1.05);
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.3);
}

/* Loading durumu */
.profile-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 16px;
}

.profile-loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Varsayılan profil ikonu */
.profile-icon {
  font-size: 32px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.profile-icon:hover {
  transform: scale(1.1);
  color: var(--primary-color);
}

.sidebar.collapsed .profile-btn {
  justify-content: center;
  padding: 12px;
}

.sidebar.collapsed .profile-image-container {
  width: 36px;
  height: 36px;
}

.sidebar.collapsed .profile-image {
  width: 36px;
  height: 36px;
}

.sidebar.collapsed .profile-icon {
  font-size: 36px;
}

.sidebar.collapsed .profile-loading {
  font-size: 18px;
}

.theme-toggle-container {
  margin-bottom: 10px;
}

.theme-toggle-btn, .logout-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
  background: transparent;
  border: none;
  font-weight: 500;
  width: 100%;
}

.theme-toggle-btn {
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(63, 55, 201, 0.1) 100%);
}

.logout-btn {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.theme-toggle-btn:hover, .logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.theme-toggle-btn:hover {
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.2) 0%, rgba(63, 55, 201, 0.2) 100%);
}

.logout-btn:hover {
  background-color: rgba(220, 53, 69, 0.2);
}

.theme-toggle-btn.icon-only, .logout-btn.icon-only {
  justify-content: center;
  padding: 12px;
}

.theme-toggle-btn i {
  font-size: 18px;
  color: var(--primary-color);
}

.logout-btn i {
  font-size: 18px;
  color: #dc3545;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
  .sidebar {
    position: fixed;
    z-index: 1000;
    width: 260px;
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  /* Mobile'da profil fotoğrafı boyutları */
  .profile-image-container {
    width: 28px;
    height: 28px;
  }

  .profile-image {
    width: 28px;
    height: 28px;
  }

  .profile-icon {
    font-size: 28px;
  }
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .menu-header,
.sidebar.collapsed .menu-item {
  position: relative;
}

.sidebar.collapsed .menu-header::after,
.sidebar.collapsed .menu-item::after {
  content: attr(data-title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  padding: 5px 10px;
  border-radius: 5px;
  white-space: nowrap;
  box-shadow: 0 2px 5px var(--shadow-color);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 1001;
}

.sidebar.collapsed .menu-header:hover::after,
.sidebar.collapsed .menu-item:hover::after {
  opacity: 1;
  transform: translateY(-50%) translateX(10px);
}

/* Logo Link Styling */
.logo-link {
  text-decoration: none;
  color: inherit;
  display: inline-block;
  cursor: pointer;
}

.logo-link:hover,
.logo-link:focus {
  text-decoration: none;
  color: inherit;
}

/* Scrollbar Styles */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: var(--sidebar-bg);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Dark Mode Scrollbar */
[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Firefox Scrollbar */
.sidebar {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) var(--sidebar-bg);
}

.sidebar-content {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

/* Dark Mode Firefox Scrollbar */
[data-theme="dark"] .sidebar {
  scrollbar-color: rgba(255, 255, 255, 0.2) var(--sidebar-bg);
}

[data-theme="dark"] .sidebar-content {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}
