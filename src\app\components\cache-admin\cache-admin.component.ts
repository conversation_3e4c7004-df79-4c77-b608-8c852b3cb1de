import { Component, OnInit } from '@angular/core';
import { CacheAdminService, CacheStatistics, CacheItemDetail, TenantCacheInfo, PerformanceMetrics } from '../../services/cache-admin.service';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-cache-admin',
  templateUrl: './cache-admin.component.html',
  styleUrls: ['./cache-admin.component.css'],
  standalone: false
})
export class CacheAdminComponent implements OnInit {
  // Mevcut özellikler
  statistics: CacheStatistics | null = null;
  healthInfo: { [key: string]: any } | null = null;
  allKeys: string[] = [];
  cacheSize: number = 0;
  isLoading = false;
  searchPattern = '';
  filteredKeys: string[] = [];
  selectedTenantId: number | null = null;
  selectedEntityName = '';

  // Yeni özellikler
  activeTenants: number[] = [];
  tenantSizes: { [key: number]: number } = {};
  performanceMetrics: PerformanceMetrics | null = null;
  selectedTenantDetails: CacheItemDetail[] = [];
  entityCounts: { [key: string]: number } = {};
  detailedStatistics: any = null;

  // UI kontrol
  activeTab = 'overview'; // overview, tenants, performance, details
  selectedTenantForDetails: number | null = null;
  showTenantDetails = false;

  // Predefined entity names for dropdown
  entityNames = [
    'Member', 'Product', 'Payment', 'Membership', 'City', 'Town',
    'OperationClaim', 'MembershipFreezeHistory', 'User', 'Company', 'MembershipType'
  ];

  constructor(
    private cacheAdminService: CacheAdminService,
    private toastrService: ToastrService,
    private authService: AuthService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Yetki kontrolü
    if (!this.authService.hasRole('owner')) {
      this.toastrService.error('Bu sayfaya erişim yetkiniz bulunmamaktadır.');
      this.router.navigate(['/unauthorized']);
      return;
    }

    this.loadCacheData();
  }

  loadCacheData(): void {
    this.isLoading = true;

    // Paralel olarak tüm cache bilgilerini yükle
    Promise.all([
      this.loadStatistics(),
      this.loadHealthInfo(),
      this.loadAllKeys(),
      this.loadCacheSize(),
      this.loadActiveTenants(),
      this.loadTenantSizes(),
      this.loadPerformanceMetrics(),
      this.loadDetailedStatistics()
    ]).finally(() => {
      this.isLoading = false;
    });
  }

  private loadStatistics(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getStatistics().subscribe({
        next: (response) => {
          if (response.success) {
            this.statistics = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache istatistikleri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  private loadHealthInfo(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getHealthInfo().subscribe({
        next: (response) => {
          if (response.success) {
            this.healthInfo = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache sağlık bilgileri yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  private loadAllKeys(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getAllKeys().subscribe({
        next: (response) => {
          if (response.success) {
            this.allKeys = response.data;
            this.filteredKeys = [...this.allKeys];
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache anahtarları yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  private loadCacheSize(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getCacheSize().subscribe({
        next: (response) => {
          if (response.success) {
            this.cacheSize = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: (error) => {
          this.toastrService.error('Cache boyutu yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  searchKeys(): void {
    if (!this.searchPattern.trim()) {
      this.filteredKeys = [...this.allKeys];
      return;
    }

    this.cacheAdminService.getKeysByPattern(this.searchPattern).subscribe({
      next: (response) => {
        if (response.success) {
          this.filteredKeys = response.data;
          this.toastrService.success(response.message);
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Arama yapılırken hata oluştu');
      }
    });
  }

  clearAllCache(): void {
    if (!confirm('Tüm cache verilerini silmek istediğinizden emin misiniz?')) {
      return;
    }

    this.cacheAdminService.clearAll().subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.message);
          this.loadCacheData();
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Cache temizlenirken hata oluştu');
      }
    });
  }

  clearTenantCache(): void {
    if (!this.selectedTenantId) {
      this.toastrService.warning('Lütfen bir tenant ID girin');
      return;
    }

    if (!confirm(`Tenant ${this.selectedTenantId} cache verilerini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.cacheAdminService.clearTenant(this.selectedTenantId).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.message);
          this.loadCacheData();
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Tenant cache temizlenirken hata oluştu');
      }
    });
  }

  clearEntityCache(): void {
    if (!this.selectedTenantId || !this.selectedEntityName) {
      this.toastrService.warning('Lütfen tenant ID ve entity adını seçin');
      return;
    }

    if (!confirm(`Tenant ${this.selectedTenantId} - ${this.selectedEntityName} entity cache verilerini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.cacheAdminService.clearEntity(this.selectedTenantId, this.selectedEntityName).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.message);
          this.loadCacheData();
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Entity cache temizlenirken hata oluştu');
      }
    });
  }

  testCache(): void {
    this.cacheAdminService.testCache().subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.message);
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: (error) => {
        this.toastrService.error('Cache test edilirken hata oluştu');
      }
    });
  }

  refreshData(): void {
    this.loadCacheData();
    this.toastrService.info('Veriler yenilendi');
  }

  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getHitRatioColor(ratio: number): string {
    if (ratio >= 0.8) return 'text-success';
    if (ratio >= 0.6) return 'text-warning';
    return 'text-danger';
  }

  // Yeni metodlar
  private loadActiveTenants(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getActiveTenants().subscribe({
        next: (response) => {
          if (response.success) {
            this.activeTenants = response.data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  private loadTenantSizes(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getAllTenantSizes().subscribe({
        next: (response) => {
          if (response.success) {
            this.tenantSizes = response.data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  private loadPerformanceMetrics(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getPerformanceMetrics().subscribe({
        next: (response) => {
          if (response.success) {
            this.performanceMetrics = response.data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  private loadDetailedStatistics(): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getDetailedStatistics().subscribe({
        next: (response) => {
          if (response.success) {
            this.detailedStatistics = response.data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  loadTenantDetails(tenantId: number): void {
    this.selectedTenantForDetails = tenantId;
    this.showTenantDetails = true;

    // Tenant detaylarını yükle
    Promise.all([
      this.loadTenantCacheDetails(tenantId),
      this.loadTenantEntityCounts(tenantId)
    ]);
  }

  private loadTenantCacheDetails(tenantId: number): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getCacheDetails(tenantId).subscribe({
        next: (response) => {
          if (response.success) {
            this.selectedTenantDetails = response.data;
          } else {
            this.toastrService.error(response.message);
          }
          resolve();
        },
        error: () => {
          this.toastrService.error('Tenant detayları yüklenirken hata oluştu');
          resolve();
        }
      });
    });
  }

  private loadTenantEntityCounts(tenantId: number): Promise<void> {
    return new Promise((resolve) => {
      this.cacheAdminService.getEntityCounts(tenantId).subscribe({
        next: (response) => {
          if (response.success) {
            this.entityCounts = response.data;
          }
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  getTenantCacheSize(tenantId: number): string {
    const size = this.tenantSizes[tenantId] || 0;
    return this.formatBytes(size);
  }

  getTenantKeyCount(tenantId: number): number {
    return this.selectedTenantDetails.filter(item => item.tenantId === tenantId).length;
  }

  clearTenantCacheById(tenantId: number): void {
    if (!confirm(`Tenant ${tenantId} cache verilerini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    this.cacheAdminService.clearTenant(tenantId).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.message);
          this.loadCacheData();
        } else {
          this.toastrService.error(response.message);
        }
      },
      error: () => {
        this.toastrService.error('Tenant cache temizlenirken hata oluştu');
      }
    });
  }

  getMemoryUsagePercentage(): number {
    if (!this.performanceMetrics) return 0;
    return this.performanceMetrics.cacheMemoryPercentage;
  }

  getMemoryUsageColor(): string {
    const percentage = this.getMemoryUsagePercentage();
    if (percentage >= 80) return 'text-danger';
    if (percentage >= 60) return 'text-warning';
    return 'text-success';
  }

  getExpiredKeysCount(): number {
    return this.performanceMetrics?.expiredKeys || 0;
  }

  closeTenantDetails(): void {
    this.showTenantDetails = false;
    this.selectedTenantForDetails = null;
    this.selectedTenantDetails = [];
    this.entityCounts = {};
  }

  getEntityCountsArray(): { name: string, count: number }[] {
    return Object.entries(this.entityCounts).map(([name, count]) => ({ name, count }));
  }

  formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleString('tr-TR');
  }

  getStatusColor(status: string): string {
    return status === 'Active' ? 'text-success' : 'text-danger';
  }
}
