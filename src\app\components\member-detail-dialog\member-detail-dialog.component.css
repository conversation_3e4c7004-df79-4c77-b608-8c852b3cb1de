/* Member Detail Dialog Component Styles */

/* Main Dialog Container */
.member-detail-dialog {
  background-color: var(--card-bg-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  width: 100%;
  max-width: 100%;
  margin: 0;
  overflow: hidden;
  animation: zoomIn 0.3s var(--transition-timing);
  border: 1px solid var(--border-color);
  position: relative;
}

/* Dialog Header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--primary-color);
  color: white;
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.btn-close {
  background: transparent;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.btn-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Dialog Content */
.dialog-content {
  padding: var(--spacing-md);
  max-height: 75vh;
  overflow-y: auto;
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* Error Container */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  color: var(--danger);
}

/* Member Details */
.member-details {
  animation: fadeIn 0.5s var(--transition-timing);
}

/* Member Header Card */
.member-header-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.member-header-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.member-avatar {
  margin-right: var(--spacing-lg);
}

.avatar-circle-lg {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 600;
  color: white;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.profile-icon {
  font-size: 3rem;
  color: white;
}

.member-header-info {
  flex: 1;
}

.member-header-info h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
}


.member-status {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--border-radius-pill);
  margin-bottom: var(--spacing-sm);
}

.status-active {
  background-color: var(--success-light);
  color: var(--success);
}

.status-expired {
  background-color: var(--danger-light);
  color: var(--danger);
}

.status-frozen {
  background-color: var(--info-light);
  color: var(--info);
}

.member-contact {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  color: var(--text-muted);
}

.member-contact div {
  display: flex;
  align-items: center;
}

.member-contact i {
  margin-right: var(--spacing-xs);
}

/* Tabs Container */
.tabs-container {
  background-color: var(--card-bg-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.tabs-header {
  display: flex;
  overflow-x: auto;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.tab {
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  white-space: nowrap;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease, color 0.3s ease;
  font-weight: 500;
  color: var(--text-muted);
  position: relative;
}

.tab i {
  margin-right: var(--spacing-xs);
}

.tab.active {
  color: var(--primary-color);
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
}

.tab:hover:not(.active) {
  background-color: var(--bg-tertiary);
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  border-radius: 10px;
  background-color: var(--primary-light);
  color: var(--primary-color);
  margin-left: var(--spacing-xs);
}

/* Tab Content */
.tab-content {
  padding: var(--spacing-lg);
  animation: fadeIn 0.3s var(--transition-timing);
}

/* Info Section */
.info-section {
  margin-bottom: var(--spacing-lg);
}

.info-section h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-xs);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.info-item {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.info-item:hover {
  transform: translateY(-3px);
  background-color: var(--bg-tertiary);
}

.info-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.info-value {
  font-weight: 500;
  color: var(--text-color);
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  margin-bottom: var(--spacing-md);
}

.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: var(--spacing-md);
}

.modern-table th {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: var(--spacing-md);
  border-bottom: 2px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-muted);
  text-align: left;
}

.modern-table td {
  padding: var(--spacing-md);
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: background-color 0.3s ease;
}

.modern-table tbody tr:hover {
  background-color: var(--bg-tertiary);
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--border-radius-pill);
  text-align: center;
}

/* No Data Message */
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  color: var(--text-muted);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  font-style: italic;
}

.no-data i {
  margin-right: var(--spacing-xs);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .member-header-card {
    flex-direction: column;
    text-align: center;
  }

  .member-avatar {
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }

  .avatar-circle-lg {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }

  .profile-icon {
    font-size: 2.5rem;
  }

  .member-contact {
    justify-content: center;
  }

  .tabs-header {
    flex-wrap: wrap;
  }

  .tab {
    flex: 1 1 auto;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .dialog-content {
    padding: var(--spacing-sm);
  }

  .tab {
    font-size: 0.875rem;
  }

  .tab i {
    margin-right: 0;
  }

  .tab span:not(.badge) {
    display: none;
  }

  .badge {
    margin-left: 0;
  }
}

/* Kalan gün bilgisi için stil */
.remaining-days {
  font-size: 0.9em; /* Biraz daha küçük font */
  color: var(--text-muted); /* Soluk renk */
  font-weight: 400; /* Normal kalınlık */
  margin-left: 8px; /* İsimden biraz boşluk */
}

/* Dark Mode Specific Adjustments */
[data-theme="dark"] .member-header-card {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .info-item {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .info-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .no-data {
  background-color: var(--bg-tertiary);
}
