import { Component, OnDestroy } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { RateLimitService } from '../../services/rate-limit.service';
import { ToastrService } from 'ngx-toastr';
import { interval, Subscription } from 'rxjs';

interface MembershipInfo {
  branch: string;
  startDate: string;
  endDate: string;
  remainingDays: number;
}

interface MemberQRInfo {
  name: string;
  scanNumber: string;
  memberships: MembershipInfo[];
}

@Component({
    selector: 'app-member-qrcode',
    templateUrl: './member-qrcode.component.html',
    styleUrls: ['./member-qrcode.component.css'],
    standalone: false
})
export class MemberQRCodeComponent implements OnDestroy {
  phoneNumber: string = '';
  memberInfo: MemberQRInfo | null = null;
  message: string = '';
  isError: boolean = false;
  showLookupButton: boolean = true;
  lastQueriedNumber: string = '';
  isLoading: boolean = false;
  isSpecialMember: boolean = false;

  // QR kod yenileme i<PERSON>
  qrCodeValiditySeconds: number = 300; // 5 dakika (300 saniye)
  remainingSeconds: number = 0;
  timerSubscription?: Subscription;

  constructor(
    private memberService: MemberService,
    private rateLimitService: RateLimitService,
    private toastrService: ToastrService
  ) {}

  ngOnDestroy() {
    // Component yok edildiğinde timer'ı temizle
    this.clearTimer();
  }

  formatPhoneNumber(number: string): string {
    // Telefon numarasındaki tüm boşlukları ve özel karakterleri kaldır
    let cleaned = number.replace(/\D/g, '');

    // Eğer numara 10 haneliyse başına 0 ekle
    if (cleaned.length === 10) {
      cleaned = '0' + cleaned;
    }

    return cleaned;
  }

  lookupMember() {
    if (!this.phoneNumber) {
      this.message = 'Lütfen bir telefon numarası girin.';
      this.isError = true;
      this.memberInfo = null;
      return;
    }

    // Telefon numarasını formatla
    let formattedPhoneNumber = this.formatPhoneNumber(this.phoneNumber);

    // Özel numara kontrolü - SADECE '05363304276' telefon numarası için
    if (formattedPhoneNumber === '05363304276') {
      this.isSpecialMember = true;
    } else {
      this.isSpecialMember = false;
    }

    this.isLoading = true;
    this.showLookupButton = false;
    this.lastQueriedNumber = formattedPhoneNumber;

    this.memberService.getMemberQRInfo(formattedPhoneNumber).subscribe({
      next: (response: any) => {
        if (response.success) {
          this.memberInfo = response.data;
          this.isError = false;
          this.message = response.message;

          // QR kod geçerlilik süresini başlat
          this.startQRCodeTimer();
        } else {
          this.memberInfo = null;
          this.message = response.message || 'Üye bulunamadı.';
          this.isError = true;
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.memberInfo = null;
        this.message = error.error?.message || 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
        this.isError = true;
        this.isLoading = false;
        console.error('Error:', error);
      }
    });
  }

  onPhoneNumberChange() {
    if (this.phoneNumber !== this.lastQueriedNumber) {
      this.showLookupButton = true;
    }
  }

  downloadQRCode() {
    // Rate limit kontrolü
    this.rateLimitService.getRemainingFileDownloads().subscribe({
      next: (rateLimitResponse) => {
        if (rateLimitResponse.success && rateLimitResponse.remainingDownloads <= 0) {
          this.toastrService.error('Dosya indirme limitini aştınız. 10 dakikada en fazla 5 dosya indirebilirsiniz.', 'Limit Aşıldı');
          return;
        }

        this.performQRDownload();
      },
      error: (error) => {
        console.error('Rate limit kontrolü başarısız:', error);
        this.performQRDownload(); // Rate limit kontrolü başarısız olursa yine de devam et
      }
    });
  }

  private performQRDownload() {
    const qrCodeElement = document.querySelector('qrcode canvas');
    if (qrCodeElement instanceof HTMLCanvasElement) {
      const image = qrCodeElement.toDataURL("image/png").replace("image/png", "image/octet-stream");
      const link = document.createElement('a');
      link.download = `QR_Code_${this.memberInfo?.name || 'Member'}.png`;
      link.href = image;
      link.click();

      // Başarılı indirme kaydı
      this.rateLimitService.recordFileDownload().subscribe({
        next: (recordResponse) => {
          this.toastrService.success('QR kod başarıyla indirildi.', 'Başarılı');
        },
        error: (recordError) => {
          console.error('File download record error:', recordError);
          this.toastrService.success('QR kod başarıyla indirildi.', 'Başarılı');
        }
      });
    } else {
      console.error('QR code canvas element not found');
      this.message = 'QR kodu oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.';
      this.isError = true;
    }
  }

  getFutureMemberships(): MembershipInfo[] {
    if (!this.memberInfo || !this.memberInfo.memberships) return [];
    const now = new Date();
    return this.memberInfo.memberships.filter(m => new Date(m.startDate) > now);
  }

  getActiveMemberships(): MembershipInfo[] {
    if (!this.memberInfo || !this.memberInfo.memberships) return [];
    const now = new Date();
    return this.memberInfo.memberships.filter(m =>
      new Date(m.startDate) <= now && new Date(m.endDate) > now
    );
  }

  getExpiredMemberships(): MembershipInfo[] {
    if (!this.memberInfo || !this.memberInfo.memberships) return [];
    const now = new Date();
    return this.memberInfo.memberships.filter(m => new Date(m.endDate) <= now);
  }

  // QR kod geçerlilik süresi için timer başlat
  startQRCodeTimer() {
    // Önceki timer'ı temizle
    this.clearTimer();

    // Süreyi başlat
    this.remainingSeconds = this.qrCodeValiditySeconds;

    // Her saniye güncellenen bir timer oluştur
    this.timerSubscription = interval(1000).subscribe(() => {
      this.remainingSeconds--;

      // Süre dolduğunda QR kodu otomatik yenile
      if (this.remainingSeconds <= 0) {
        this.refreshQRCode();
      }
    });
  }

  // Timer'ı temizle
  clearTimer() {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
      this.timerSubscription = undefined;
    }
  }

  // QR kodu yenile
  refreshQRCode() {
    if (this.lastQueriedNumber) {
      this.lookupMember();
    }
  }

  // Kalan süreyi dakika:saniye formatında göster
  getFormattedRemainingTime(): string {
    const minutes = Math.floor(this.remainingSeconds / 60);
    const seconds = this.remainingSeconds % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }

  // Kalan süre yüzdesini hesapla (progress bar için)
  getRemainingTimePercentage(): number {
    return (this.remainingSeconds / this.qrCodeValiditySeconds) * 100;
  }
}