<div class="gym-container">
  <div class="gym-panel">
    <div class="header">
      <div class="gym-icon">
        <i class="fas fa-dumbbell"></i>
      </div>
    </div>

    <div class="input-area">
      <input
        type="tel"
        [(ngModel)]="phoneNumber"
        inputmode="numeric"
        maxlength="11"
        (ngModelChange)="onPhoneNumberChange()"
        placeholder="05XXXXXXXXX"
        aria-label="Telefon Numarası"
      />
      <button
        *ngIf="showLookupButton"
        (click)="lookupMember()"
        [disabled]="!phoneNumber"
        aria-label="Üye Sorgula"
      >
        <i class="fas fa-search"></i> Sorgula
      </button>
    </div>

    <div *ngIf="isLoading" class="message-area">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i> Yükleniyor
      </div>
    </div>

    <div *ngIf="isError" class="message-area error">
      <i class="fas fa-exclamation-circle"></i> {{ message }}
    </div>

    <div *ngIf="memberInfo" class="result-area" [ngClass]="{'special-member': isSpecialMember}">
      <div *ngIf="isSpecialMember" class="special-header">
        <div class="hearts">❤️ ❤️ ❤️</div>
        <h2>Canım Sevgilim 😊</h2>
        <div class="hearts">❤️ ❤️ ❤️</div>
      </div>
      <h2 *ngIf="!isSpecialMember">{{ memberInfo.name }}</h2>

      <div class="membership-info" [ngClass]="{'special-info': isSpecialMember}">
        <div
          *ngFor="let membership of getFutureMemberships()"
          class="future-membership"
        >
          <i class="fas fa-calendar-plus"></i> {{ membership.branch }} {{ message }}
        </div>

        <div
          *ngFor="let membership of getActiveMemberships()"
          class="remaining-days"
        >
          <i class="fas fa-calendar-check"></i> {{ membership.branch }} üyeliğinizin bitmesine
          <strong>{{ membership.remainingDays }}</strong> gün kalmıştır.
        </div>

        <div
          *ngFor="let membership of getExpiredMemberships()"
          class="expired-membership"
        >
          <i class="fas fa-calendar-times"></i> {{ membership.branch }} üyeliğiniz sona ermiştir
        </div>

        <div
          *ngIf="memberInfo.memberships.length === 0"
          [ngClass]="{'expired-membership-special': !isSpecialMember, 'special-expired-text': isSpecialMember}"
        >
          <i class="fas fa-exclamation-triangle"></i> <span class="expired-text">Üyeliğinizin Süresi Dolmuştur</span>
        </div>
      </div>

      <div *ngIf="memberInfo.scanNumber" class="qr-code-container">
        <qrcode
          [qrdata]="memberInfo.scanNumber"
          [width]="220"
          [errorCorrectionLevel]="'M'"
        ></qrcode>

        <!-- QR Kod Geçerlilik Süresi Göstergesi -->
        <div class="qr-validity-timer">
          <div class="timer-label">
            <span>QR Kod Geçerlilik Süresi:</span>
            <span class="timer-countdown" [ngClass]="{'timer-warning': remainingSeconds < 60}">
              {{ getFormattedRemainingTime() }}
            </span>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar" [style.width.%]="getRemainingTimePercentage()"
                 [ngClass]="{'progress-warning': remainingSeconds < 60}"></div>
          </div>
        </div>

        <div class="qr-actions">
          <button class="download-button" (click)="downloadQRCode()">
            <i class="fas fa-download"></i> QR Kodunu İndir
          </button>
        </div>

        <div class="qr-info-text">
          <i class="fas fa-info-circle"></i> Bu QR kod 5 dakika geçerlidir ve süre sonunda otomatik olarak yenilenir.
        </div>
      </div>
    </div>
  </div>
</div>
