import { Component, OnInit } from '@angular/core';
import { DeletedCompanyUser } from '../../models/deletedCompanyUser';
import { CompanyUserService } from '../../services/company-user.service';
import { ToastrService } from 'ngx-toastr';
import { faTrashRestore, faInfoCircle, faSyncAlt } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-deleted-companies',
  templateUrl: './deleted-companies.component.html',
  styleUrls: ['./deleted-companies.component.css'],
  standalone: false
})
export class DeletedCompaniesComponent implements OnInit {
  deletedCompanies: DeletedCompanyUser[] = [];
  isLoading = false;
  viewMode = 'table'; // 'table' or 'card'

  // Icons
  faTrashRestore = faTrashRestore;
  faInfoCircle = faInfoCircle;
  faSyncAlt = faSyncAlt;

  constructor(
    private companyUserService: CompanyUserService,
    private toastrService: ToastrService
  ) { }

  ngOnInit(): void {
    this.loadDeletedCompanies();
  }

  loadDeletedCompanies(): void {
    this.isLoading = true;
    
    this.companyUserService.getDeletedCompanyUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.deletedCompanies = response.data;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching deleted companies:', error);
        this.toastrService.error('Silinen salonlar yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  restoreCompany(company: DeletedCompanyUser): void {
    const confirmMessage = `"${company.name}" adlı salonu geri yüklemek istediğinizden emin misiniz?\n\nBu işlem şunları geri yükleyecek:\n• Salon sahibi bilgileri\n• Salon bilgileri\n• Salon adresi\n• Kullanıcı hesabı`;
    
    if (confirm(confirmMessage)) {
      this.companyUserService.restoreCompanyUser(company.companyUserID).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success(response.message, 'Başarılı');
            this.loadDeletedCompanies();
          } else {
            this.toastrService.error(response.message, 'Hata');
          }
        },
        error: (error) => {
          console.error('Error restoring company:', error);
          this.toastrService.error('Salon geri yüklenirken bir hata oluştu.', 'Hata');
        }
      });
    }
  }

  getInitials(name: string): string {
    if (!name) return 'CU';
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  }

  getAvatarColor(name: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    if (!name) return colors[0];
    
    const charCode = name.charCodeAt(0);
    return colors[charCode % colors.length];
  }

  formatDate(date: Date | undefined): string {
    if (!date) return 'Bilinmiyor';
    return new Date(date).toLocaleDateString('tr-TR');
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'table' ? 'card' : 'table';
  }
}
