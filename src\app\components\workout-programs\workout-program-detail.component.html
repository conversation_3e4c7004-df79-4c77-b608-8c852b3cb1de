<div class="container-fluid">
  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
    <p class="mt-3 text-muted">Program yükleniyor</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && program">
    <!-- Header -->
    <div class="modern-card mb-4">
      <div class="modern-card-header">
        <div class="d-flex align-items-center">
          <button
            class="modern-btn modern-btn-outline-secondary me-3"
            (click)="goBack()">
            <fa-icon [icon]="faArrowLeft"></fa-icon>
          </button>
          <div>
            <h4 class="mb-0">{{program.programName}}</h4>
            <small class="text-muted">Antrenman Programı Detayı</small>
          </div>
        </div>
        <div class="d-flex gap-2">
          <button
            *ngIf="isOwner || isAdmin"
            class="modern-btn modern-btn-outline-primary"
            (click)="editProgram()">
            <fa-icon [icon]="faEdit" class="modern-btn-icon"></fa-icon>
            Düzenle
          </button>
          <button
            *ngIf="isOwner || isAdmin"
            class="modern-btn modern-btn-outline-danger"
            (click)="deleteProgram()">
            <fa-icon [icon]="faTrashAlt" class="modern-btn-icon"></fa-icon>
            Sil
          </button>
        </div>
      </div>
    </div>

    <!-- Program Overview -->
    <div class="row mb-4">
      <!-- Basic Info -->
      <div class="col-12">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
              Program Bilgileri
            </h5>
          </div>
          <div class="modern-card-body">
            <div class="program-info-grid">
              <div class="info-item">
                <div class="info-header">
                  <fa-icon [icon]="faChartLine" class="text-primary"></fa-icon>
                  Deneyim Seviyesi
                </div>
                <div class="info-content">
                  <span 
                    [ngClass]="getExperienceLevelBadgeClass(program.experienceLevel)">
                    {{program.experienceLevel || '-'}}
                  </span>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-header">
                  <fa-icon [icon]="faBullseye" class="text-primary"></fa-icon>
                  Hedef
                </div>
                <div class="info-content">
                  <span 
                    [ngClass]="getTargetGoalBadgeClass(program.targetGoal)">
                    {{program.targetGoal || '-'}}
                  </span>
                </div>
              </div>

              <div class="info-item">
                <div class="info-header">
                  <fa-icon [icon]="faCalendar" class="text-primary"></fa-icon>
                  Oluşturma Tarihi
                </div>
                <div class="info-content">{{formatDate(program.creationDate)}}</div>
              </div>
              
              <div class="info-item description" *ngIf="program.description">
                <div class="info-header">
                  <fa-icon [icon]="faFileAlt" class="text-primary"></fa-icon>
                  Açıklama
                </div>
                <div class="info-content">
                  <p class="program-description">{{program.description}}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Program Days -->
    <div class="modern-card">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
          Program Günleri
        </h5>
      </div>
      <div class="modern-card-body">
        <div class="row g-4">
          <div 
            *ngFor="let day of program.days; let i = index" 
            class="col-lg-6">
            
            <div class="day-detail-card">
              <div class="day-detail-header">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">
                    <span class="day-number">{{day.dayNumber}}. Gün</span>
                    <span class="day-name">{{day.dayName}}</span>
                  </h6>
                  <div>
                    <span 
                      *ngIf="day.isRestDay"
                      class="program-badge program-badge-warning">
                      Dinlenme
                    </span>
                    <span 
                      *ngIf="!day.isRestDay"
                      class="program-badge program-badge-primary">
                      {{day.exercises?.length || 0}} Egzersiz
                    </span>
                  </div>
                </div>
              </div>

              <div class="day-detail-body">
                <!-- Rest Day -->
                <div *ngIf="day.isRestDay" class="text-center py-3">
                  <fa-icon [icon]="faClock" class="text-muted mb-2" style="font-size: 2rem;"></fa-icon>
                  <p class="text-muted mb-0">Bu gün dinlenme günüdür</p>
                </div>

                <!-- Workout Day -->
                <div *ngIf="!day.isRestDay && day.exercises && day.exercises.length > 0">
                  <div class="exercise-list">
                    <div 
                      *ngFor="let exercise of day.exercises; let j = index" 
                      class="exercise-item">
                      
                      <div class="exercise-info">
                        <div class="exercise-name">
                          {{j + 1}}. {{exercise.exerciseName}}
                        </div>
                        <div class="exercise-details">
                          <span class="exercise-sets">{{exercise.sets}} set</span>
                          <span class="exercise-reps">{{exercise.reps}} tekrar</span>
                        </div>
                        <div *ngIf="exercise.notes" class="exercise-notes">
                          <small class="text-muted">{{exercise.notes}}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Empty Workout Day -->
                <div *ngIf="!day.isRestDay && (!day.exercises || day.exercises.length === 0)" class="text-center py-3">
                  <fa-icon [icon]="faDumbbell" class="text-muted mb-2" style="font-size: 1.5rem;"></fa-icon>
                  <p class="text-muted mb-0">Bu güne henüz egzersiz eklenmemiş</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="!program.days || program.days.length === 0" class="text-center py-5">
          <fa-icon [icon]="faCalendarAlt" class="text-muted mb-3" style="font-size: 3rem;"></fa-icon>
          <h6 class="text-muted">Bu programa henüz gün eklenmemiş</h6>
          <p class="text-muted">Programa gün eklemek için düzenleme sayfasını kullanın</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !program" class="text-center py-5">
    <fa-icon [icon]="faInfoCircle" class="text-muted mb-3" style="font-size: 3rem;"></fa-icon>
    <h5 class="text-muted">Program bulunamadı</h5>
    <p class="text-muted">Aradığınız antrenman programı mevcut değil veya silinmiş olabilir</p>
    <button class="modern-btn modern-btn-primary" (click)="goBack()">
      Geri Dön
    </button>
  </div>
</div>
