import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AuthService } from './auth.service';
import { FileUploadService } from './file-upload.service';

export interface ProfileImageState {
  imageUrl: string | null;
  hasImage: boolean;
  isLoading: boolean;
  lastUpdated: number | null;
}

@Injectable({
  providedIn: 'root'
})
export class ProfileImageService {
  private profileImageSubject = new BehaviorSubject<ProfileImageState>({
    imageUrl: null,
    hasImage: false,
    isLoading: false,
    lastUpdated: null
  });

  public profileImage$ = this.profileImageSubject.asObservable();

  constructor(
    private authService: AuthService,
    private fileUploadService: FileUploadService
  ) {
    // Auth state değişikliklerini dinle
    this.authService.currentUser.subscribe(user => {
      if (user) {
        this.loadProfileImage();
      } else {
        this.clearProfileImage();
      }
    });
  }

  /**
   * Profil fotoğrafını yükle (cache-aware)
   */
  loadProfileImage(): void {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser?.nameidentifier) {
      this.clearProfileImage();
      return;
    }

    const userId = parseInt(currentUser.nameidentifier);

    // Önce profil fotoğrafının var olup olmadığını kontrol et
    this.checkProfileImageExists(userId);
  }

  /**
   * Profil fotoğrafının var olup olmadığını kontrol et
   */
  private checkProfileImageExists(userId: number): void {
    // Loading state'ini başlat
    this.setLoading(true);

    const baseUrl = this.fileUploadService.getProfileImageUrl(userId);

    // Cache timestamp kontrolü
    const cacheKey = `profile_image_timestamp_${userId}`;
    const timestamp = localStorage.getItem(cacheKey);

    const imageUrl = timestamp ? `${baseUrl}?t=${timestamp}` : baseUrl;

    // Image yükleme testi
    const img = new Image();
    img.onload = () => {
      // Fotoğraf başarıyla yüklendi
      this.profileImageSubject.next({
        imageUrl: imageUrl,
        hasImage: true,
        isLoading: false,
        lastUpdated: timestamp ? parseInt(timestamp) : null
      });
    };

    img.onerror = () => {
      // Fotoğraf yüklenemedi, varsayılan duruma geç
      this.clearProfileImage();
    };

    // Timeout ekle - 5 saniye sonra loading'i durdur
    setTimeout(() => {
      if (this.profileImageSubject.value.isLoading) {
        this.clearProfileImage();
      }
    }, 5000);

    img.src = imageUrl;
  }

  /**
   * Profil fotoğrafını temizle
   */
  clearProfileImage(): void {
    this.profileImageSubject.next({
      imageUrl: null,
      hasImage: false,
      isLoading: false,
      lastUpdated: null
    });
  }

  /**
   * Profil fotoğrafı yüklendikten sonra cache bypass ile güncelle
   */
  refreshAfterUpload(): void {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser?.nameidentifier) return;

    const userId = parseInt(currentUser.nameidentifier);
    const timestamp = Date.now();
    const cacheKey = `profile_image_timestamp_${userId}`;
    
    // Timestamp'i localStorage'a kaydet
    localStorage.setItem(cacheKey, timestamp.toString());
    
    const baseUrl = this.fileUploadService.getProfileImageUrl(userId);
    const imageUrl = `${baseUrl}?t=${timestamp}`;

    this.profileImageSubject.next({
      imageUrl: imageUrl,
      hasImage: true,
      isLoading: false,
      lastUpdated: timestamp
    });
  }

  /**
   * Profil fotoğrafı silindikten sonra temizle
   */
  refreshAfterDelete(): void {
    const currentUser = this.authService.currentUserValue;
    if (currentUser?.nameidentifier) {
      const userId = parseInt(currentUser.nameidentifier);
      const cacheKey = `profile_image_timestamp_${userId}`;
      localStorage.removeItem(cacheKey);
    }

    this.clearProfileImage();
  }

  /**
   * Loading state'i güncelle
   */
  setLoading(isLoading: boolean): void {
    const currentState = this.profileImageSubject.value;
    this.profileImageSubject.next({
      ...currentState,
      isLoading
    });
  }

  /**
   * Mevcut profil fotoğrafı state'ini al
   */
  getCurrentState(): ProfileImageState {
    return this.profileImageSubject.value;
  }

  /**
   * Profil fotoğrafı var mı kontrolü
   */
  hasProfileImage(): boolean {
    return this.profileImageSubject.value.hasImage;
  }

  /**
   * Profil fotoğrafı URL'ini al (cache-aware)
   */
  getProfileImageUrl(): string | null {
    return this.profileImageSubject.value.imageUrl;
  }
}
