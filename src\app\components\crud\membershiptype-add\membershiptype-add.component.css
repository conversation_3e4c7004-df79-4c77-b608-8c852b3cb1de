/* Membership Type Add Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Form Section */
.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.section-title {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s var(--transition-timing);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.5s var(--transition-timing);
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Modern Loading - handled by app-loading-spinner component */

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .form-section {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .form-section:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
}

/* Fix for price input group */
.input-group {
  display: flex;
  align-items: center;
  height: 38px; /* Set a fixed height to match input height */
}

.input-group .modern-form-control {
  flex: 1;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group .input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  border-top-left-radius: var(--border-radius-sm);
  border-bottom-left-radius: var(--border-radius-sm);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  height: 100%;
  background-color: var(--bg-tertiary);
  width: 40px; /* Fixed width for the currency symbol */
}

/* Card Header Enhancements */
.modern-card-header h5 {
  font-size: 1.1rem;
  font-weight: 600;
}

.modern-card-header i {
  color: var(--primary);
}

/* Section Title Enhancements */
.section-title i {
  color: var(--primary);
  font-size: 0.9rem;
}

/* Form Enhancements */
.validity-selects {
  gap: 0.5rem;
}

.validity-selects select {
  flex: 1;
  min-width: 0;
}

/* Table Container Enhancements for embedded component */
:host ::ng-deep .table-container {
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

:host ::ng-deep .modern-table {
  margin-bottom: 0;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .validity-selects {
    flex-direction: column;
    gap: 0.75rem;
  }

  .validity-selects select {
    margin-right: 0 !important;
    margin-bottom: 0;
  }

  /* Ensure input group stays horizontal even on mobile */
  .input-group {
    flex-direction: row;
  }

  /* Stack cards vertically on mobile */
  .row .col-md-4,
  .row .col-md-8 {
    margin-bottom: 1.5rem;
  }

  .row .col-md-8 {
    margin-bottom: 0;
  }
}
