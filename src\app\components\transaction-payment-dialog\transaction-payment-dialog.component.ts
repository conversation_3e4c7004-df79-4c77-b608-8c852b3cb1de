import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-transaction-payment-dialog',
    templateUrl: './transaction-payment-dialog.component.html',
    styleUrls: ['./transaction-payment-dialog.component.css'],
    standalone: false,
    
})
export class TransactionPaymentDialogComponent {
  selectedPaymentMethod: string | undefined;

  constructor(
    public dialogRef: MatDialogRef<TransactionPaymentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { title: string; message: string; paymentMethods?: string[] }
  ) {
    
    
    
    
    
  }
}