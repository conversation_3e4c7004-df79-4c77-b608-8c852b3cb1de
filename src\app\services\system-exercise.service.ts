import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';
import { PaginatedResult } from '../models/pagination';
import { BaseApiService } from './baseApiService';

export interface SystemExercise {
  systemExerciseID: number;
  exerciseCategoryID: number;
  categoryName: string;
  exerciseName: string;
  description?: string;
  instructions?: string;
  muscleGroups?: string;
  equipment?: string;
  difficultyLevel?: number;
  difficultyLevelText?: string;
  isActive?: boolean;
  creationDate?: Date;
}

export interface SystemExerciseAdd {
  exerciseCategoryID: number;
  exerciseName: string;
  description?: string;
  instructions?: string;
  muscleGroups?: string;
  equipment?: string;
  difficultyLevel?: number;
}

export interface SystemExerciseUpdate {
  systemExerciseID: number;
  exerciseCategoryID: number;
  exerciseName: string;
  description?: string;
  instructions?: string;
  muscleGroups?: string;
  equipment?: string;
  difficultyLevel?: number;
  isActive?: boolean;
}

export interface SystemExerciseFilter {
  exerciseCategoryID?: number;
  searchTerm?: string;
  difficultyLevel?: number;
  equipment?: string;
  exerciseType?: string; // "System" veya "Company"
  isActive?: boolean;
  page: number;
  pageSize: number;
}

@Injectable({
  providedIn: 'root'
})
export class SystemExerciseService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Tüm sistem egzersizlerini getirir
   */
  getAllSystemExercises(): Observable<ListResponseModel<SystemExercise>> {
    return this.httpClient.get<ListResponseModel<SystemExercise>>(
      `${this.apiUrl}systemexercises/getall`
    );
  }

  /**
   * Kategoriye göre sistem egzersizlerini getirir
   */
  getByCategory(categoryId: number): Observable<ListResponseModel<SystemExercise>> {
    return this.httpClient.get<ListResponseModel<SystemExercise>>(
      `${this.apiUrl}systemexercises/getbycategory/${categoryId}`
    );
  }

  /**
   * Filtrelenmiş sistem egzersizlerini sayfalı olarak getirir
   */
  getFiltered(filter: SystemExerciseFilter): Observable<SingleResponseModel<PaginatedResult<SystemExercise>>> {
    return this.httpClient.post<SingleResponseModel<PaginatedResult<SystemExercise>>>(
      `${this.apiUrl}systemexercises/getfiltered`,
      filter
    );
  }

  /**
   * Sistem egzersizlerinde arama yapar
   */
  search(searchTerm: string): Observable<ListResponseModel<SystemExercise>> {
    return this.httpClient.get<ListResponseModel<SystemExercise>>(
      `${this.apiUrl}systemexercises/search?searchTerm=${encodeURIComponent(searchTerm)}`
    );
  }

  /**
   * ID'ye göre sistem egzersizi detayını getirir
   */
  getDetail(exerciseId: number): Observable<SingleResponseModel<SystemExercise>> {
    return this.httpClient.get<SingleResponseModel<SystemExercise>>(
      `${this.apiUrl}systemexercises/getdetail/${exerciseId}`
    );
  }

  /**
   * ID'ye göre sistem egzersizi getirir
   */
  getById(exerciseId: number): Observable<SingleResponseModel<SystemExercise>> {
    return this.httpClient.get<SingleResponseModel<SystemExercise>>(
      `${this.apiUrl}systemexercises/getbyid/${exerciseId}`
    );
  }

  /**
   * Yeni sistem egzersizi ekler (Sadece owner)
   */
  add(exercise: SystemExerciseAdd): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}systemexercises/add`,
      exercise
    );
  }

  /**
   * Sistem egzersizini günceller (Sadece owner)
   */
  update(exercise: SystemExerciseUpdate): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}systemexercises/update`,
      exercise
    );
  }

  /**
   * Sistem egzersizini siler (Sadece owner)
   */
  delete(exerciseId: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}systemexercises/delete/${exerciseId}`
    );
  }
}
