/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.company-selector-container {
  display: flex;
  align-items: center;
}

.company-selector {
  min-width: 200px;
}

.loading-indicator {
  margin-left: 10px;
}

mat-form-field {
  width: 100%;
}
