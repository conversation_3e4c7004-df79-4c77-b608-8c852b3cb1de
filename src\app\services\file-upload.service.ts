import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';
import { ResponseModel } from '../models/responseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ImageCompressionService } from './image-compression.service';

@Injectable({
  providedIn: 'root'
})
export class FileUploadService extends BaseApiService {

  constructor(
    private httpClient: HttpClient,
    private imageCompressionService: ImageCompressionService
  ) {
    super();
  }

  uploadProfileImage(file: File): Observable<SingleResponseModel<string>> {
    const formData = new FormData();
    formData.append('file', file);

    let newPath = this.apiUrl + 'user/upload-profile-image';
    return this.httpClient.post<SingleResponseModel<string>>(newPath, formData);
  }

  async uploadCompressedProfileImage(file: File): Promise<Observable<SingleResponseModel<string>>> {
    try {
      // Resmi sıkıştır
      const compressedFile = await this.imageCompressionService.compressImage(file);

      // Sıkıştırılmış dosyayı yükle
      return this.uploadProfileImage(compressedFile);
    } catch (error) {
      throw new Error('Resim sıkıştırma işlemi başarısız oldu: ' + error);
    }
  }

  deleteProfileImage(): Observable<ResponseModel> {
    let newPath = this.apiUrl + 'user/delete-profile-image';
    return this.httpClient.delete<ResponseModel>(newPath);
  }

  getProfileImageUrl(userId: number): string {
    return this.apiUrl + `user/profile-image/${userId}`;
  }

  validateImageFile(file: File): { isValid: boolean; message?: string } {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        message: 'Sadece JPG, JPEG ve PNG dosyaları yüklenebilir.'
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        message: 'Dosya boyutu 5MB\'dan büyük olamaz.'
      };
    }

    return { isValid: true };
  }
}
