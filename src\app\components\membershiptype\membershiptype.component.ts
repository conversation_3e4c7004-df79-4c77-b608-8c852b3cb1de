
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MembershipType } from '../../models/membershipType';
import { MembershipTypeService } from '../../services/membership-type.service';
import { ResponseModel } from '../../models/responseModel';
import { ToastrService } from 'ngx-toastr';
import { MembershiptypeUpdateComponent } from '../crud/membershiptype-update/membershiptype-update.component';
import { MembershiptypeAddComponent } from '../crud/membershiptype-add/membershiptype-add.component';
import { faTrashAlt, faEdit, faSortUp, faSortDown } from '@fortawesome/free-solid-svg-icons';
import { DialogService } from '../../services/dialog.service';
import { MembershipTypePagingParameters } from '../../models/membershipTypePagingParameters';
import { PaginatedResult } from '../../models/pagination';

@Component({
    selector: 'app-membershiptype',
    templateUrl: './membershiptype.component.html',
    styleUrls: ['./membershiptype.component.css'],
    standalone: false
})
export class MembershiptypeComponent implements OnInit {
  @Output() loadingStateChange = new EventEmitter<boolean>();

  // Pagination properties
  paginatedMembershipTypes: PaginatedResult<MembershipType> = {
    data: [],
    pageNumber: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    hasPrevious: false,
    hasNext: false
  };

  pageSizeOptions: number[] = [10, 25, 50];

  // Legacy property for backward compatibility
  membershipTypes: MembershipType[] = [];

  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  faSortUp = faSortUp;
  faSortDown = faSortDown;
  sortDirection: 'asc' | 'desc' = 'asc'; // Default sorting is ascending (low to high)
  isLoading: boolean = false;
  activeSortField: 'id' | 'duration' = 'id'; // Track which field is being sorted

  constructor(
    private membershipTypeService: MembershipTypeService,
    private toastrService: ToastrService,
    private dialog: MatDialog,
    private dialogService: DialogService
  ) {}

  ngOnInit(): void {
    this.loadMembershipTypes();
  }

  loadMembershipTypes() {
    this.isLoading = true;
    this.loadingStateChange.emit(true);

    const parameters: MembershipTypePagingParameters = {
      pageNumber: this.paginatedMembershipTypes.pageNumber,
      pageSize: this.paginatedMembershipTypes.pageSize
    };

    this.membershipTypeService.getMembershipTypesPaginated(parameters).subscribe({
      next: (response) => {
        if (response.success) {
          this.paginatedMembershipTypes = response.data;
          // Legacy support - update the old array for backward compatibility
          this.membershipTypes = response.data.data;
        }
        this.isLoading = false;
        this.loadingStateChange.emit(false);
      },
      error: (error) => {
        this.toastrService.error('Üyelik türleri yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
        this.loadingStateChange.emit(false);
      }
    });
  }

  // Legacy method for backward compatibility
  getMembershipTypes() {
    this.loadMembershipTypes();
  }

  // New method to sort by ID
  sortById(direction: 'asc' | 'desc' = 'desc') {
    this.sortDirection = direction;
    this.activeSortField = 'id';
    
    // Sort by membershipTypeID (higher ID = newer entry)
    this.membershipTypes.sort((a, b) => {
      if (direction === 'asc') {
        return a.membershipTypeID - b.membershipTypeID;
      } else {
        return b.membershipTypeID - a.membershipTypeID;
      }
    });
  }

  sortByDuration(direction?: 'asc' | 'desc') {
    // If direction is provided, use it; otherwise toggle the current direction
    if (direction) {
      this.sortDirection = direction;
    } else {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    }
    
    this.activeSortField = 'duration';

    // Sort the membershipTypes array based on the day property
    this.membershipTypes.sort((a, b) => {
      if (this.sortDirection === 'asc') {
        return a.day - b.day; // Low to high
      } else {
        return b.day - a.day; // High to low
      }
    });
  }
  
  
  openUpdateDialog(membershipType: MembershipType) {
    const dialogRef = this.dialog.open(MembershiptypeUpdateComponent, {
      width: '600px',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: membershipType
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadMembershipTypes();
      }
    });
  }

  openAddDialog() {
    const dialogRef = this.dialog.open(MembershiptypeAddComponent, {
      width: '500px',
      maxWidth: '100vw',
      maxHeight: '100vh'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadMembershipTypes();
      }
    });
  }

  // Pagination methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.paginatedMembershipTypes.totalPages && page !== this.paginatedMembershipTypes.pageNumber) {
      this.paginatedMembershipTypes.pageNumber = page;
      this.loadMembershipTypes();
    }
  }

  onPageSizeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const newPageSize = parseInt(target.value);
    this.paginatedMembershipTypes.pageSize = newPageSize;
    this.paginatedMembershipTypes.pageNumber = 1; // Reset to first page
    this.loadMembershipTypes();
  }

  getPaginationRange(): number[] {
    const totalPages = this.paginatedMembershipTypes.totalPages;
    const currentPage = this.paginatedMembershipTypes.pageNumber;
    const range: number[] = [];

    if (totalPages <= 7) {
      // Show all pages if total pages <= 7
      for (let i = 1; i <= totalPages; i++) {
        range.push(i);
      }
    } else {
      // Show smart pagination
      if (currentPage <= 4) {
        for (let i = 1; i <= 5; i++) {
          range.push(i);
        }
        range.push(-1); // Ellipsis
        range.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        range.push(1);
        range.push(-1); // Ellipsis
        for (let i = totalPages - 4; i <= totalPages; i++) {
          range.push(i);
        }
      } else {
        range.push(1);
        range.push(-1); // Ellipsis
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          range.push(i);
        }
        range.push(-1); // Ellipsis
        range.push(totalPages);
      }
    }

    return range;
  }

  deleteMembershipType(membershipType: MembershipType) {
    this.dialogService.confirmMembershipTypeDelete(membershipType).subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.membershipTypeService.delete(membershipType.membershipTypeID).subscribe({
          next: (response: ResponseModel) => {
            this.isLoading = false;
            if (response.success) {
              // Reload the current page
              this.loadMembershipTypes();
              this.toastrService.success(response.message, 'Başarılı');
            } else {
              this.toastrService.error(response.message, 'Hata');
            }
          },
          error: (error) => {
            this.isLoading = false;
            this.toastrService.error('Üyelik Paketi Bir Üyeye Bağlı, Silinemez.', 'Hata');
          }
        });
      }
    });
  }

  getDayDisplay(day: number): string {
    return (day === 30 || day === 31) ? '1 Ay' : `${day} Gün`;
  }
}
