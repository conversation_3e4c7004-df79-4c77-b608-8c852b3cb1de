Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8EA480000 ntdll.dll
7FF8E8D00000 KERNEL32.DLL
7FF8E7670000 KERNELBASE.dll
7FF8E8F00000 USER32.dll
7FF8E7EA0000 win32u.dll
7FF8E8810000 GDI32.dll
7FF8E7BE0000 gdi32full.dll
7FF8E8020000 msvcp_win.dll
7FF8E80D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8E8940000 advapi32.dll
7FF8E9280000 msvcrt.dll
7FF8E9F00000 sechost.dll
7FF8E9450000 RPCRT4.dll
7FF8E6CF0000 CRYPTBASE.DLL
7FF8E75D0000 bcryptPrimitives.dll
7FF8E8CC0000 IMM32.DLL
