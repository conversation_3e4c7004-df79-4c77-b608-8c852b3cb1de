import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseApiService } from './baseApiService';
import { CompanyContextService } from './company-context.service';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class BaseDataService<T> extends BaseApiService {
  protected apiEndpoint: string;

  constructor(
    protected http: HttpClient,
    protected companyContext: CompanyContextService
  ) {
    super();
  }

  // Tüm kayıtları getir
  getAll(params?: any): Observable<T[]> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }

    return this.http.get<any>(`${this.apiUrl}${this.apiEndpoint}`, { params: httpParams })
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          }
          return [];
        })
      );
  }

  // ID'ye göre kayıt getir
  getById(id: number): Observable<T> {
    return this.http.get<any>(`${this.apiUrl}${this.apiEndpoint}/${id}`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          }
          throw new Error(response.message || 'Kayıt bulunamadı');
        })
      );
  }

  // Yeni kayıt ekle
  add(entity: any): Observable<any> {
    // Eğer entity bir ICompanyEntity ise ve CompanyID özelliği yoksa veya -1 ise
    // mevcut CompanyId'yi ekle
    if (entity && !entity.companyID && this.companyContext.currentCompanyId > 0) {
      entity.companyID = this.companyContext.currentCompanyId;
    }

    return this.http.post<any>(`${this.apiUrl}${this.apiEndpoint}/add`, entity)
      .pipe(
        map(response => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message || 'Kayıt eklenirken bir hata oluştu');
        })
      );
  }

  // Kayıt güncelle
  update(entity: any): Observable<any> {
    // Eğer entity bir ICompanyEntity ise ve CompanyID özelliği yoksa veya -1 ise
    // mevcut CompanyId'yi ekle
    if (entity && !entity.companyID && this.companyContext.currentCompanyId > 0) {
      entity.companyID = this.companyContext.currentCompanyId;
    }

    return this.http.post<any>(`${this.apiUrl}${this.apiEndpoint}/update`, entity)
      .pipe(
        map(response => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message || 'Kayıt güncellenirken bir hata oluştu');
        })
      );
  }

  // Kayıt sil
  delete(id: number): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}${this.apiEndpoint}/delete`, { id })
      .pipe(
        map(response => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message || 'Kayıt silinirken bir hata oluştu');
        })
      );
  }

  // Özel bir endpoint için POST isteği
  post(endpoint: string, data: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}${this.apiEndpoint}/${endpoint}`, data)
      .pipe(
        map(response => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message || 'İşlem sırasında bir hata oluştu');
        })
      );
  }

  // Özel bir endpoint için GET isteği
  get(endpoint: string, params?: any): Observable<any> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }

    return this.http.get<any>(`${this.apiUrl}${this.apiEndpoint}/${endpoint}`, { params: httpParams })
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          }
          return null;
        })
      );
  }
}
