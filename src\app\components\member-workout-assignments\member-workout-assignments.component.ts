import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { faPlus, faEdit, faTrashAlt, faSearch, faFilter, faUsers, faDumbbell } from '@fortawesome/free-solid-svg-icons';

import { MemberWorkoutProgramService, MemberWorkoutProgramList } from '../../services/member-workout-program.service';
import { AuthService } from '../../services/auth.service';
import { DialogService } from '../../services/dialog.service';
import { MemberWorkoutAssignModalComponent } from './member-workout-assign-modal.component';

@Component({
  selector: 'app-member-workout-assignments',
  templateUrl: './member-workout-assignments.component.html',
  styleUrls: ['./member-workout-assignments.component.css'],
  standalone: false
})
export class MemberWorkoutAssignmentsComponent implements OnInit, OnDestroy {
  // Icons
  faPlus = faPlus;
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSearch = faSearch;
  faFilter = faFilter;
  faUsers = faUsers;
  faDumbbell = faDumbbell;

  // Data
  assignments: MemberWorkoutProgramList[] = [];
  filteredAssignments: MemberWorkoutProgramList[] = [];
  
  // Pagination
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  pageSize = 20;

  // Filters
  searchText = '';
  
  // UI State
  isLoading = false;
  
  // User permissions
  isOwner = false;
  isAdmin = false;



  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private memberWorkoutProgramService: MemberWorkoutProgramService,
    private authService: AuthService,
    private dialogService: DialogService,
    private router: Router,
    private toastrService: ToastrService,
    public dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.checkUserPermissions();
    this.setupSearch();
    this.loadAssignments();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  checkUserPermissions(): void {
    this.isOwner = this.authService.hasRole('owner');
    this.isAdmin = this.authService.hasRole('admin');
  }

  setupSearch(): void {
    this.searchSubject.pipe(
     
      takeUntil(this.destroy$)
    ).subscribe(searchValue => {
      this.searchText = searchValue;
      this.applyFilters();
    });
  }

  loadAssignments(): void {
    this.isLoading = true;

    this.memberWorkoutProgramService.getCompanyAssignments().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.assignments = response.data;
          this.totalItems = this.assignments.length;
          this.applyFilters();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading assignments:', error);
        this.toastrService.error('Program atamaları yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }



  applyFilters(): void {
    let filtered = [...this.assignments];

    // Search filter
    if (this.searchText.trim()) {
      const searchLower = this.searchText.toLowerCase();
      filtered = filtered.filter(assignment => 
        assignment.memberName.toLowerCase().includes(searchLower) ||
        assignment.programName.toLowerCase().includes(searchLower)
      );
    }



    this.filteredAssignments = filtered;
    this.totalItems = filtered.length;
    this.totalPages = Math.ceil(this.totalItems / this.pageSize);
    this.currentPage = 1;
  }

  onSearch(event: any): void {
    const searchValue = event.target.value;
    this.searchSubject.next(searchValue);
  }



  openAssignModal(): void {
    const dialogRef = this.dialog.open(MemberWorkoutAssignModalComponent, {
      width: '600px',
      data: { mode: 'add' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadAssignments();
      }
    });
  }

  editAssignment(assignment: MemberWorkoutProgramList): void {
    const dialogRef = this.dialog.open(MemberWorkoutAssignModalComponent, {
      width: '600px',
      data: { 
        mode: 'edit',
        assignment: assignment
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadAssignments();
      }
    });
  }





  deleteAssignment(assignment: MemberWorkoutProgramList): void {
    this.dialogService.confirm(
      'Program Atamasını Sil',
      `${assignment.memberName} üyesinin "${assignment.programName}" program atamasını silmek istediğinizden emin misiniz?`
    ).subscribe((result: boolean) => {
      if (result) {
        this.memberWorkoutProgramService.deleteAssignment(assignment.memberWorkoutProgramID).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Program ataması başarıyla silindi', 'Başarılı');
              this.loadAssignments();
            } else {
              this.toastrService.error(response.message, 'Hata');
            }
          },
          error: (error) => {
            console.error('Error deleting assignment:', error);
            this.toastrService.error('Program ataması silinirken hata oluştu', 'Hata');
          }
        });
      }
    });
  }

  getPaginatedAssignments(): MemberWorkoutProgramList[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.filteredAssignments.slice(startIndex, endIndex);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
  }

  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'modern-badge-success' : 'modern-badge-secondary';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Aktif' : 'Pasif';
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString('tr-TR');
  }


}
