import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { LicensePackage } from '../models/licensePackage'; 
import { BaseApiService } from './baseApiService';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';

@Injectable({
  providedIn: 'root'
})
export class LicensePackageService extends BaseApiService {
  constructor(private httpClient: HttpClient) {
    super();
  }

  getAll(): Observable<ListResponseModel<LicensePackage>> {
    return this.httpClient.get<ListResponseModel<LicensePackage>>(this.apiUrl + 'LicensePackages/getall');
  }

  getById(id: number): Observable<SingleResponseModel<LicensePackage>> {
    return this.httpClient.get<SingleResponseModel<LicensePackage>>(this.apiUrl + 'LicensePackages/getbyid?id=' + id);
  }

  add(licensePackage: LicensePackage): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'LicensePackages/add', licensePackage);
  }

  update(licensePackage: LicensePackage): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'LicensePackages/update', licensePackage);
  }

  delete(id: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(this.apiUrl + 'LicensePackages/delete?id=' + id);
  }
}
