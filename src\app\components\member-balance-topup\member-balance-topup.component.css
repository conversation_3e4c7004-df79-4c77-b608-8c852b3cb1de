/* Member Balance Top-up Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern Filter Button */
.modern-filter-btn {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  padding: 8px 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
}

.modern-filter-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--primary);
  color: var(--primary);
}

.modern-filter-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
  border-color: var(--primary);
}

.filter-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.filter-badge {
  background: var(--primary);
  color: white;
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 4px;
}

/* Modern Dropdown Menu */
.modern-dropdown-menu {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 4px;
  margin-top: 4px;
  background: var(--bg-primary);
  min-width: 200px;
}

.modern-dropdown-item {
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 2px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-primary);
  text-decoration: none;
  font-size: 0.875rem;
}

.modern-dropdown-item:last-child {
  margin-bottom: 0;
}

.modern-dropdown-item:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  text-decoration: none;
}

.modern-dropdown-item.active {
  background: var(--primary);
  color: white;
}

.modern-dropdown-item.active:hover {
  background: var(--primary-dark);
  color: white;
  text-decoration: none;
}

.item-count {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.modern-dropdown-item.active .item-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.modern-dropdown-item:hover:not(.active) .item-count {
  background: var(--bg-quaternary);
  color: var(--text-primary);
}

/* Dark theme support */
[data-theme="dark"] .modern-dropdown-menu {
  background: var(--bg-primary);
  border-color: var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modern-filter-btn {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-filter-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--primary);
}