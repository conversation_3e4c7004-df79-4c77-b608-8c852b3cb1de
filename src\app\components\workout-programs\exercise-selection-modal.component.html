<!-- Modern Exercise Selection Modal -->
<div class="exercise-selection-modal">
  <!-- Enhanced <PERSON>dal Header -->
  <div class="enhanced-modal-header">
    <div class="header-content">
      <div class="header-icon">
        <fa-icon [icon]="faSearch"></fa-icon>
      </div>
      <div class="header-text">
        <h3 class="modal-title">Egzersiz <PERSON>çimi</h3>
        <p class="modal-subtitle">Antrenman programınız için uygun egzersizi seçin</p>
      </div>
    </div>
    <button type="button" class="enhanced-close-btn" (click)="onCancel()">
      <fa-icon [icon]="faTimes"></fa-icon>
    </button>
  </div>

  <!-- Enhanced Modal Body -->
  <div class="enhanced-modal-body">
    <!-- Enhanced Search and Filters Section -->
    <div class="enhanced-filters-section">
      <div class="filters-header">
        <h5 class="filters-title">
          <fa-icon [icon]="faFilter" class="me-2"></fa-icon>
          <PERSON><PERSON> ve Filtreler
        </h5>
        <button
          *ngIf="hasActiveFilters()"
          class="clear-all-btn"
          (click)="clearFilters()">
          <fa-icon [icon]="faTimes" class="me-1"></fa-icon>
          Tümünü Temizle
        </button>
      </div>

      <div class="filters-grid">
        <!-- Enhanced Search -->
        <div class="filter-item search-filter">
          <label class="filter-label">
            <fa-icon [icon]="faSearch" class="label-icon"></fa-icon>
            Egzersiz Ara
          </label>
          <div class="search-input-wrapper">
            <fa-icon [icon]="faSearch" class="search-icon"></fa-icon>
            <input
              type="text"
              class="enhanced-search-input"
              placeholder="Egzersiz adını yazın..."
              [value]="searchText"
              (input)="onSearch($event)">
            <button
              *ngIf="searchText"
              class="clear-search-btn"
              (click)="clearSearch()">
              <fa-icon [icon]="faTimes"></fa-icon>
            </button>
          </div>
        </div>

        <!-- Enhanced Exercise Type Filter -->
        <div class="filter-item">
          <label class="filter-label">
            <fa-icon [icon]="faDesktop" class="label-icon"></fa-icon>
            Egzersiz Türü
          </label>
          <div class="select-wrapper">
            <select
              class="enhanced-select"
              [(ngModel)]="selectedExerciseType"
              (change)="onExerciseTypeChange()">
              <option value="">Tüm Türler</option>
              <option value="System">💻 Sistem Hareketleri</option>
              <option value="Company">🏢 Salon Hareketleri</option>
            </select>
            <fa-icon [icon]="faChevronDown" class="select-arrow"></fa-icon>
          </div>
        </div>

        <!-- Enhanced Category Filter -->
        <div class="filter-item">
          <label class="filter-label">
            <fa-icon [icon]="faList" class="label-icon"></fa-icon>
            Kategori
          </label>
          <div class="select-wrapper">
            <select
              class="enhanced-select"
              [(ngModel)]="selectedCategoryId"
              (change)="onCategoryChange()">
              <option value="">Tüm Kategoriler</option>
              <option *ngFor="let category of categories" [value]="category.exerciseCategoryID">
                {{category.categoryName}}
              </option>
            </select>
            <fa-icon [icon]="faChevronDown" class="select-arrow"></fa-icon>
          </div>
        </div>

        <!-- Enhanced Difficulty Filter -->
        <div class="filter-item">
          <label class="filter-label">
            <fa-icon [icon]="faSignal" class="label-icon"></fa-icon>
            Zorluk Seviyesi
          </label>
          <div class="select-wrapper">
            <select
              class="enhanced-select"
              [(ngModel)]="selectedDifficultyLevel"
              (change)="onDifficultyChange()">
              <option value="">Tüm Seviyeler</option>
              <option value="1">🟢 Başlangıç</option>
              <option value="2">🟡 Orta</option>
              <option value="3">🔴 İleri</option>
            </select>
            <fa-icon [icon]="faChevronDown" class="select-arrow"></fa-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Results Info -->
    <div class="results-info-section">
      <div class="results-stats">
        <div class="stats-item">
          <fa-icon [icon]="faList" class="stats-icon"></fa-icon>
          <span class="stats-text">
            <span *ngIf="!isLoading">{{totalItems}} egzersiz</span>
            <span *ngIf="isLoading">Yükleniyor...</span>
          </span>
        </div>
        <div *ngIf="selectedExercise" class="stats-item selected-info">
          <fa-icon [icon]="faCheck" class="stats-icon text-success"></fa-icon>
          <span class="stats-text">
            "{{selectedExercise.exerciseName}}" seçili
          </span>
        </div>
      </div>
    </div>

    <!-- Enhanced Loading Spinner -->
    <div *ngIf="isLoading" class="enhanced-loading-section">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <p class="loading-text">Egzersizler yükleniyor...</p>
    </div>

    <!-- Enhanced Exercises List -->
    <div *ngIf="!isLoading" class="enhanced-exercises-list">
      <div
        *ngFor="let exercise of exercises; let i = index"
        class="enhanced-exercise-card"
        [class.selected]="isExerciseSelected(exercise)"
        [class.animate-in]="true"
        [style.animation-delay.ms]="i * 50"
        (click)="selectExercise(exercise)">

        <!-- Exercise Card Header -->
        <div class="exercise-card-header">
          <div class="exercise-main-info">
            <h6 class="exercise-name">{{exercise.exerciseName}}</h6>
            <div class="exercise-category-tag">
              <fa-icon [icon]="faTag" class="me-1"></fa-icon>
              {{exercise.categoryName}}
            </div>
          </div>
          <div class="exercise-badges-group">
            <span
              class="enhanced-badge type-badge"
              [ngClass]="getExerciseTypeBadgeClass(exercise.exerciseType)">
              <fa-icon [icon]="exercise.exerciseType === 'System' ? faDesktop : faBuilding" class="me-1"></fa-icon>
              {{exercise.exerciseType === 'System' ? 'Sistem' : 'Salon'}}
            </span>
            <span
              class="enhanced-badge difficulty-badge"
              [ngClass]="getDifficultyBadgeClass(exercise.difficultyLevel)">
              {{getDifficultyIcon(exercise.difficultyLevel)}} {{getDifficultyText(exercise.difficultyLevel)}}
            </span>
          </div>
        </div>

        <!-- Exercise Card Body -->
        <div class="exercise-card-body" *ngIf="exercise.description || exercise.muscleGroups || exercise.equipment">
          <div *ngIf="exercise.description" class="exercise-description">
            {{exercise.description}}
          </div>

          <div class="exercise-details-grid">
            <div *ngIf="exercise.muscleGroups" class="detail-item">
              <fa-icon [icon]="faUser" class="detail-icon"></fa-icon>
              <div class="detail-content">
                <span class="detail-label">Kas:</span>
                <span class="detail-value">{{exercise.muscleGroups}}</span>
              </div>
            </div>
            <div *ngIf="exercise.equipment" class="detail-item">
              <fa-icon [icon]="faCog" class="detail-icon"></fa-icon>
              <div class="detail-content">
                <span class="detail-label">Ekipman:</span>
                <span class="detail-value">{{exercise.equipment}}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Selection Indicator -->
        <div class="enhanced-selection-indicator" *ngIf="isExerciseSelected(exercise)">
          <div class="selection-checkmark">
            <fa-icon [icon]="faCheck"></fa-icon>
          </div>
          <div class="selection-overlay"></div>
        </div>
      </div>

      <!-- Enhanced Empty State -->
      <div *ngIf="exercises.length === 0" class="enhanced-empty-state">
        <div class="empty-state-icon">
          <fa-icon [icon]="faSearch"></fa-icon>
        </div>
        <h5 class="empty-state-title">Egzersiz Bulunamadı</h5>
        <p class="empty-state-description">
          <span *ngIf="hasActiveFilters()">
            Arama kriterlerinize uygun egzersiz bulunamadı.<br>
            Filtreleri değiştirerek tekrar deneyin.
          </span>
          <span *ngIf="!hasActiveFilters()">
            Henüz hiç egzersiz tanımlanmamış.<br>
            Lütfen sistem yöneticisi ile iletişime geçin.
          </span>
        </p>
        <button
          *ngIf="hasActiveFilters()"
          class="empty-state-action"
          (click)="clearFilters()">
          <fa-icon [icon]="faTimes" class="me-2"></fa-icon>
          Filtreleri Temizle
        </button>
      </div>
    </div>

    <!-- Enhanced Pagination -->
    <div *ngIf="totalPages > 1" class="enhanced-pagination-section">
      <div class="pagination-info">
        <span class="pagination-text">
          Sayfa {{currentPage}} / {{totalPages}}
          <span class="text-muted">({{totalItems}} egzersiz)</span>
        </span>
      </div>
      <nav class="pagination-nav" aria-label="Sayfa navigasyonu">
        <div class="pagination-controls">
          <button
            class="pagination-btn prev-btn"
            [disabled]="currentPage === 1"
            (click)="onPageChange(currentPage - 1)">
            <fa-icon [icon]="faChevronLeft"></fa-icon>
            <span class="btn-text">Önceki</span>
          </button>

          <div class="page-numbers">
            <button
              *ngFor="let page of getPaginationRange()"
              class="page-number-btn"
              [class.active]="page === currentPage"
              (click)="onPageChange(page)">
              {{page}}
            </button>
          </div>

          <button
            class="pagination-btn next-btn"
            [disabled]="currentPage === totalPages"
            (click)="onPageChange(currentPage + 1)">
            <span class="btn-text">Sonraki</span>
            <fa-icon [icon]="faChevronRight"></fa-icon>
          </button>
        </div>
      </nav>
    </div>
  </div>

  <!-- Enhanced Modal Footer -->
  <div class="enhanced-modal-footer">
    <div class="footer-info">
      <div *ngIf="selectedExercise" class="selected-exercise-info">
        <fa-icon [icon]="faCheck" class="text-success me-2"></fa-icon>
        <span class="selected-text">
          <strong>{{selectedExercise.exerciseName}}</strong> seçildi
        </span>
      </div>
      <div *ngIf="!selectedExercise" class="no-selection-info">
        <fa-icon [icon]="faInfoCircle" class="text-muted me-2"></fa-icon>
        <span class="info-text">Lütfen bir egzersiz seçin</span>
      </div>
    </div>

    <div class="footer-actions">
      <button
        type="button"
        class="enhanced-btn cancel-btn"
        (click)="onCancel()">
        <fa-icon [icon]="faTimes" class="btn-icon"></fa-icon>
        <span class="btn-text">İptal</span>
      </button>
      <button
        type="button"
        class="enhanced-btn confirm-btn"
        [class.disabled]="!selectedExercise"
        [disabled]="!selectedExercise"
        (click)="onConfirm()">
        <fa-icon [icon]="faCheck" class="btn-icon"></fa-icon>
        <span class="btn-text">Egzersizi Seç</span>
      </button>
    </div>
  </div>
</div>
