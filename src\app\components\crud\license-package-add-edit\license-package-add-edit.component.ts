import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackage } from '../../../models/licensePackage';
import { LicensePackageService } from '../../../services/license-package.service';

interface DialogData {
  isEdit: boolean;
  licensePackage?: LicensePackage;
}

@Component({
  selector: 'app-license-package-add-edit',
  templateUrl: './license-package-add-edit.component.html',
  styleUrls: ['./license-package-add-edit.component.css'],
  standalone:false
})
export class LicensePackageAddEditComponent implements OnInit {
  licensePackageForm: FormGroup;
  isSubmitting = false;
  dialogTitle: string;
  
  // Predefined roles for the dropdown
  roles: string[] = ['owner', 'admin'];

  constructor(
    private fb: FormBuilder,
    private licensePackageService: LicensePackageService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<LicensePackageAddEditComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.dialogTitle = data.isEdit ? 'Lisans Paketi Düzenle' : 'Yeni Lisans Paketi Ekle';
    
    this.licensePackageForm = this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      role: ['', Validators.required],
      durationDays: [30, [Validators.required, Validators.min(1)]],
      price: [0, [Validators.required, Validators.min(0)]],
      isActive: [true]
    });

    if (data.isEdit && data.licensePackage) {
      this.licensePackageForm.patchValue({
        name: data.licensePackage.name,
        description: data.licensePackage.description,
        role: data.licensePackage.role,
        durationDays: data.licensePackage.durationDays,
        price: data.licensePackage.price,
        isActive: data.licensePackage.isActive
      });
    }
  }

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.licensePackageForm.invalid) {
      this.toastr.error('Lütfen formu doğru şekilde doldurun', 'Hata');
      return;
    }

    this.isSubmitting = true;
    
    const formValue = this.licensePackageForm.value;
    const licensePackage: LicensePackage = {
      name: formValue.name,
      description: formValue.description,
      role: formValue.role,
      durationDays: formValue.durationDays,
      price: formValue.price,
      isActive: formValue.isActive,
      creationDate: new Date(),
      licensePackageID: formValue.licensePackageID
    };

    if (this.data.isEdit && this.data.licensePackage) {
      licensePackage.licensePackageID = this.data.licensePackage.licensePackageID;
      licensePackage.creationDate = this.data.licensePackage.creationDate;
      licensePackage.updatedDate = new Date();
      
      this.licensePackageService.update(licensePackage).subscribe({
        next: (response) => {
          this.toastr.success(response.message, 'Başarılı');
          this.dialogRef.close(true);
          this.isSubmitting = false;
        },
        error: (error) => {
          this.toastr.error('Lisans paketi güncellenirken bir hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    } else {
      this.licensePackageService.add(licensePackage).subscribe({
        next: (response) => {
          this.toastr.success(response.message, 'Başarılı');
          this.dialogRef.close(true);
          this.isSubmitting = false;
        },
        error: (error) => {
          this.toastr.error('Lisans paketi eklenirken bir hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
