/* <PERSON><PERSON> Admin Component Styles */

.card {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg, 0.75rem);
  box-shadow: var(--shadow-sm, 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075));
  transition: all 0.3s ease;
  color: var(--text-color);
}

.card:hover {
  box-shadow: var(--shadow-md, 0 0.5rem 1rem rgba(0, 0, 0, 0.15));
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--btn-primary-text, white);
  border-bottom: none;
  border-radius: var(--border-radius-lg, 0.75rem) var(--border-radius-lg, 0.75rem) 0 0;
}

.card-header h4,
.card-header h5,
.card-header h6 {
  color: var(--btn-primary-text, white);
  margin: 0;
}

.card-body {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Statistics Cards */
.bg-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark)) !important;
  color: var(--btn-primary-text, white) !important;
}

.bg-warning {
  background: linear-gradient(135deg, var(--warning), #e0a800) !important;
  color: var(--bg-primary, #000) !important;
}

.bg-info {
  background: linear-gradient(135deg, var(--info), #138496) !important;
  color: var(--btn-primary-text, white) !important;
}

.bg-success {
  background: linear-gradient(135deg, var(--success), #1e7e34) !important;
  color: var(--btn-primary-text, white) !important;
}

.bg-danger {
  background: linear-gradient(135deg, var(--danger), #c82333) !important;
  color: var(--btn-primary-text, white) !important;
}

/* Hit Ratio Colors */
.text-success {
  background: linear-gradient(135deg, var(--success), #1e7e34) !important;
  color: var(--btn-primary-text, white) !important;
}

.text-warning {
  background: linear-gradient(135deg, var(--warning), #e0a800) !important;
  color: var(--bg-primary, #000) !important;
}

.text-danger {
  background: linear-gradient(135deg, var(--danger), #c82333) !important;
  color: var(--btn-primary-text, white) !important;
}

/* Buttons */
.btn {
  border-radius: var(--border-radius-md, 0.5rem);
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm, 0 4px 8px rgba(0, 0, 0, 0.2));
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--btn-primary-text);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-danger {
  background-color: var(--danger);
  border-color: var(--danger);
  color: var(--btn-primary-text);
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-warning {
  background-color: var(--warning);
  border-color: var(--warning);
  color: var(--bg-primary);
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-info {
  background-color: var(--info);
  border-color: var(--info);
  color: var(--btn-primary-text);
}

.btn-info:hover {
  background-color: #138496;
  border-color: #117a8b;
}

.btn-outline-primary {
  background-color: transparent;
  border-color: var(--primary);
  color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--btn-primary-text);
}

.btn-outline-secondary {
  background-color: transparent;
  border-color: var(--border-color);
  color: var(--text-color);
}

.btn-outline-secondary:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-color);
}

.btn-outline-success {
  background-color: transparent;
  border-color: var(--success);
  color: var(--success);
}

.btn-outline-success:hover {
  background-color: var(--success);
  border-color: var(--success);
  color: var(--btn-primary-text);
}

/* Form Controls */
.form-control,
.form-select {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: var(--border-radius-md, 0.5rem);
  color: var(--input-text);
  transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
  background-color: var(--input-bg);
  border-color: var(--primary);
  color: var(--input-text);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb, 67, 97, 238), 0.25);
  outline: none;
}

.form-label {
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Input Group */
.input-group .btn {
  border-radius: 0 var(--border-radius-md, 0.5rem) var(--border-radius-md, 0.5rem) 0;
}

.input-group .form-control {
  border-radius: var(--border-radius-md, 0.5rem) 0 0 var(--border-radius-md, 0.5rem);
}

/* Cache Keys Section */
.cache-keys-container {
  max-height: 400px;
  overflow-y: auto;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md, 0.5rem);
}

.cache-keys-container::-webkit-scrollbar {
  width: 8px;
}

.cache-keys-container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

.cache-keys-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.cache-keys-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Code styling */
code {
  background-color: var(--bg-secondary);
  color: var(--primary);
  padding: 0.2rem 0.4rem;
  border-radius: var(--border-radius-sm, 0.25rem);
  font-size: 0.875em;
  font-family: 'Courier New', monospace;
}

/* Loading Spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
  color: var(--primary);
}

/* Badge */
.badge {
  font-size: 0.875em;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-pill, 50rem);
}

.badge.bg-secondary {
  background-color: var(--secondary) !important;
  color: var(--btn-primary-text) !important;
}

/* Empty State */
.text-muted {
  color: var(--text-muted) !important;
}

.text-center {
  text-align: center;
}

.text-break {
  word-break: break-all;
}

/* Border utilities */
.border-bottom {
  border-bottom: 1px solid var(--border-color) !important;
}

/* Padding utilities */
.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .input-group {
    flex-direction: column;
  }

  .input-group .btn {
    border-radius: var(--border-radius-md, 0.5rem);
    margin-top: 0.5rem;
  }

  .input-group .form-control {
    border-radius: var(--border-radius-md, 0.5rem);
  }

  .col-md-3,
  .col-md-4,
  .col-md-6,
  .col-md-8 {
    margin-bottom: 1rem;
  }
}

/* Additional theme support */
.container-fluid {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* Icon styling */
.fas, .fa {
  color: inherit;
}

/* Alert styling for better theme support */
.alert {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md, 0.5rem);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: 15px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.modern-stats-info {
  flex: 1;
}

.modern-stats-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.5rem;
  color: inherit;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modern-stats-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: inherit;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Okunabilir Renk Paleti */
.stats-card-blue {
  background: linear-gradient(135deg, #4c63d2 0%, #5a67d8 100%);
  color: white !important;
}

.stats-card-teal {
  background: linear-gradient(135deg, #319795 0%, #38b2ac 100%);
  color: white !important;
}

.stats-card-purple {
  background: linear-gradient(135deg, #805ad5 0%, #9f7aea 100%);
  color: white !important;
}

.stats-card-purple .modern-stats-icon {
  background: rgba(255, 255, 255, 0.2);
}

.stats-card-orange {
  background: linear-gradient(135deg, #dd6b20 0%, #ed8936 100%);
  color: white !important;
}

.stats-card-orange .modern-stats-icon {
  background: rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.zoom-in {
  animation: zoom-in 0.5s ease-out;
}

.slide-in-left {
  animation: slide-in-left 0.6s ease-out;
}

.slide-in-right {
  animation: slide-in-right 0.6s ease-out;
}

.fade-in {
  animation: fade-in 0.4s ease-out;
}

/* Tab Navigation */
.nav-tabs .nav-link {
  border: none;
  border-radius: 10px 10px 0 0;
  color: var(--text-muted);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
  background: transparent;
}

.nav-tabs .nav-link:hover {
  background: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  box-shadow: 0 4px 10px rgba(var(--primary-rgb), 0.3);
}

/* Loading States */
.content-blur {
  filter: blur(2px);
  pointer-events: none;
  transition: filter 0.3s ease;
}

/* Dark Mode Renk Düzenlemeleri */
[data-theme="dark"] .stats-card-blue {
  background: linear-gradient(135deg, #3c4fe0 0%, #4c63d2 100%);
}

[data-theme="dark"] .stats-card-teal {
  background: linear-gradient(135deg, #2d7d79 0%, #319795 100%);
}

[data-theme="dark"] .stats-card-purple {
  background: linear-gradient(135deg, #6b46c1 0%, #805ad5 100%);
}

[data-theme="dark"] .stats-card-orange {
  background: linear-gradient(135deg, #c05621 0%, #dd6b20 100%);
}

/* Ensure proper spacing */
.row {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.col-12,
.col-md-3,
.col-md-4,
.col-md-6,
.col-md-8,
.col-sm-6 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
