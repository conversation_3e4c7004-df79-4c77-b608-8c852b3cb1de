import { <PERSON>mponent, OnInit, OnD<PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { LicenseTransactionService } from '../../services/license-transaction.service';
import { LicenseTransaction } from '../../models/LicenseTransaction';
import { UserService } from '../../services/user-service.service';
import { LicensePackageService } from '../../services/license-package.service';
import { User } from '../../models/user';
import { LicensePackage } from '../../models/licensePackage';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subject } from 'rxjs';
import {
  map,
  startWith,
  takeUntil
} from 'rxjs/operators';
import { DialogService } from '../../services/dialog.service';
import { RateLimitService } from '../../services/rate-limit.service';
import { ChartUtilsService } from '../../services/chart-utils.service';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

declare var Chart: any;

@Component({
  selector: 'app-license-transactions',
  templateUrl: './license-transactions.component.html',
  styleUrls: ['./license-transactions.component.css'],
  standalone: false
})
export class LicenseTransactionsComponent implements OnInit, OnDestroy, AfterViewInit {
  transactions: LicenseTransaction[] = [];
  users: { [key: number]: User } = {};
  packages: { [key: number]: LicensePackage } = {};

  // Loading states
  isInitialLoading = true;
  isLoading = false;
  isExporting = false;

  // Filter form
  filterForm: FormGroup;
  displayedColumns: string[] = ['userName', 'packageName', 'amount', 'paymentMethod', 'transactionDate'];

  // Admin search
  adminSearchControl = new FormControl();
  filteredAdmins: Observable<User[]>;
  selectedAdmin: User | null = null;

  // Date filters
  startDate: string = '';
  endDate: string = '';

  // Totals
  totalAmount = 0;
  totalCash = 0;
  totalCreditCard = 0;
  totalTransfer = 0;

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  totalPages = 0;

  // Charts
  paymentChart: any;
  monthlySalesChart: any;

  // Filters
  currentMonthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  isFiltered = false;

  private destroy$ = new Subject<void>();

  constructor(
    private licenseTransactionService: LicenseTransactionService,
    private userService: UserService,
    private licensePackageService: LicensePackageService,
    private fb: FormBuilder,
    private toastr: ToastrService,
    private dialogService: DialogService,
    private rateLimitService: RateLimitService,
    private chartUtils: ChartUtilsService
  ) {
    this.filterForm = this.fb.group({
      startDate: [null],
      endDate: [null],
      userID: [null],
      paymentMethod: ['']
    });

    this.setupAdminAutocomplete();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngAfterViewInit(): void {
    // Charts will be initialized after data is loaded
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.paymentChart) {
      this.paymentChart.destroy();
    }
    if (this.monthlySalesChart) {
      this.monthlySalesChart.destroy();
    }
  }

  private loadInitialData(): void {
    this.isInitialLoading = true;

    // Load users first
    this.userService.getAll().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          response.data.forEach(user => {
            this.users[user.userID] = user;
          });
        }

        // Then load packages
        this.licensePackageService.getAll().subscribe({
          next: (response) => {
            if (response.success && response.data) {
              response.data.forEach(pkg => {
                this.packages[pkg.licensePackageID] = pkg;
              });
            }

            // Finally load transactions and totals
            this.loadTransactions(() => {
              this.loadTotals();
              this.loadMonthlyRevenue();
              this.isInitialLoading = false;

              // Initialize charts after a short delay
              setTimeout(() => {
                this.initializeCharts();
              }, 100);
            });
          },
          error: (error) => {
            console.error('Error loading packages:', error);
            this.isInitialLoading = false;
          }
        });
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.isInitialLoading = false;
      }
    });
  }

  private setupAdminAutocomplete(): void {
    this.filteredAdmins = this.adminSearchControl.valueChanges.pipe(
      startWith(''),
      map(value => this._filterAdmins(value || ''))
    );
  }

  private _filterAdmins(value: string | User): User[] {
    if (typeof value === 'object') {
      return Object.values(this.users);
    }

    const filterValue = value.toLowerCase();
    return Object.values(this.users).filter(user =>
      user.firstName.toLowerCase().includes(filterValue) ||
      user.lastName.toLowerCase().includes(filterValue) ||
      user.email.toLowerCase().includes(filterValue)
    );
  }

  displayAdmin(admin: User): string {
    return admin ? `${admin.firstName} ${admin.lastName}` : '';
  }

  searchAdmin(): void {
    const searchValue = this.adminSearchControl.value;
    if (typeof searchValue === 'object') {
      this.selectedAdmin = searchValue;
      this.onFilterChange();
    }
  }

  clearAdminFilter(): void {
    this.selectedAdmin = null;
    this.adminSearchControl.setValue('');
    this.onFilterChange();
  }

  clearStartDateFilter(): void {
    this.startDate = '';
    this.onFilterChange();
  }

  clearEndDateFilter(): void {
    this.endDate = '';
    this.onFilterChange();
  }

  hasActiveFilters(): boolean {
    return !!(this.selectedAdmin || this.startDate || this.endDate);
  }

  onFilterChange(): void {
    this.currentPage = 1;
    this.isFiltered = this.hasActiveFilters();
    this.loadTransactions(() => {
      this.loadTotals();
      this.updateCharts();
    });
  }

  private loadTransactions(callback?: () => void): void {
    this.isLoading = true;

    const filters = {
      userID: this.selectedAdmin?.userID || null,
      startDate: this.startDate || null,
      endDate: this.endDate || null,
      page: this.currentPage,
      pageSize: this.itemsPerPage
    };

    this.licenseTransactionService.getAll(filters).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.transactions = response.data;

          // Frontend'de de transaction date'e göre sırala (en yeni en üstte)
          this.transactions.sort((a, b) =>
            new Date(b.transactionDate).getTime() - new Date(a.transactionDate).getTime()
          );

          this.totalItems = response.data.length;
          this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        } else {
          this.transactions = [];
          this.totalItems = 0;
          this.totalPages = 0;
        }
        this.isLoading = false;
        if (callback) callback();
      },
      error: (error) => {
        console.error('Error loading transactions:', error);
        this.transactions = [];
        this.totalItems = 0;
        this.totalPages = 0;
        this.isLoading = false;
        if (callback) callback();
      }
    });
  }

  private loadTotals(): void {
    this.licenseTransactionService.getTotals().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.totalAmount = response.data.totalAmount || 0;
          this.totalCash = response.data.totalCash || 0;
          this.totalCreditCard = response.data.totalCreditCard || 0;
          this.totalTransfer = response.data.totalTransfer || 0;
        }
      },
      error: (error) => {
        console.error('Error loading totals:', error);
        // Fallback to calculating from current transactions
        this.totalAmount = this.transactions.reduce((sum, t) => sum + t.amount, 0);
        this.totalCash = this.transactions.filter(t => t.paymentMethod.includes('Nakit')).reduce((sum, t) => sum + t.amount, 0);
        this.totalCreditCard = this.transactions.filter(t => t.paymentMethod.includes('Kredi Kartı')).reduce((sum, t) => sum + t.amount, 0);
        this.totalTransfer = this.transactions.filter(t => t.paymentMethod.includes('Havale')).reduce((sum, t) => sum + t.amount, 0);
      }
    });
  }

  private loadMonthlyRevenue(): void {
    const currentYear = new Date().getFullYear();
    this.licenseTransactionService.getMonthlyRevenue(currentYear).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          // Monthly revenue data is loaded and will be used in charts
          console.log('Monthly revenue loaded:', response.data);
        }
      },
      error: (error) => {
        console.error('Error loading monthly revenue:', error);
      }
    });
  }

  private initializeCharts(): void {
    this.initializePaymentChart();
    this.initializeMonthlySalesChart();
  }

  private initializePaymentChart(): void {
    const ctx = document.getElementById('licensePaymentChart') as HTMLCanvasElement;
    if (!ctx) {
      // Retry after a short delay if element is not ready
      setTimeout(() => {
        this.initializePaymentChart();
      }, 200);
      return;
    }

    if (this.paymentChart) {
      this.paymentChart.destroy();
    }

    // Use ChartUtils service for consistent configuration
    const paymentData = {
      totalCash: this.totalCash,
      totalCreditCard: this.totalCreditCard,
      totalTransfer: this.totalTransfer,
      totalDebt: 0 // License transactions don't have debt
    };

    const chartConfig = this.chartUtils.getPaymentStatsConfig(paymentData);
    this.paymentChart = new Chart(ctx, chartConfig);
  }

  private initializeMonthlySalesChart(): void {
    const ctx = document.getElementById('licenseMonthlySalesChart') as HTMLCanvasElement;
    if (!ctx) {
      // Retry after a short delay if element is not ready
      setTimeout(() => {
        this.initializeMonthlySalesChart();
      }, 200);
      return;
    }

    if (this.monthlySalesChart) {
      this.monthlySalesChart.destroy();
    }

    // Load yearly trend data from backend
    this.loadYearlyTrendData();
  }

  loadYearlyTrendData() {
    // Mevcut yılın aylık gelir trendini al
    const currentYear = new Date().getFullYear();

    this.licenseTransactionService.getMonthlyRevenue(currentYear).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const monthlyData = {
            monthlyRevenue: response.data.monthlyRevenues
          };

          const monthlyChartConfig = this.chartUtils.getMonthlyRevenueConfig(monthlyData);
          const ctx = document.getElementById('licenseMonthlySalesChart') as HTMLCanvasElement;

          if (!ctx) {
            console.warn('License monthly sales chart element not found');
            return;
          }

          try {
            // Eğer grafik zaten oluşturulmuşsa, güncelle
            if (this.monthlySalesChart) {
              this.monthlySalesChart.data.datasets[0].data = monthlyData.monthlyRevenue;
              this.monthlySalesChart.update();
            } else {
              // Grafik henüz oluşturulmamışsa, yeni bir tane oluştur
              this.monthlySalesChart = new Chart(ctx, monthlyChartConfig);
            }
          } catch (error) {
            console.error('License monthly trend chart creation error:', error);
            this.showEmptyYearlyTrendChart();
          }
        } else {
          this.toastr.error('Aylık gelir trendi yüklenirken bir hata oluştu', 'Hata');
          // Hata durumunda boş bir grafik göster
          this.showEmptyYearlyTrendChart();
        }
      },
      error: (error) => {
        console.error('Aylık gelir trendi yüklenirken bir hata oluştu:', error);
        this.toastr.error('Aylık gelir trendi yüklenirken bir hata oluştu', 'Hata');
        // Hata durumunda boş bir grafik göster
        this.showEmptyYearlyTrendChart();
      }
    });
  }

  showEmptyYearlyTrendChart() {
    const ctx = document.getElementById('licenseMonthlySalesChart') as HTMLCanvasElement;

    if (!ctx) {
      console.warn('License monthly sales chart element not found for empty chart');
      return;
    }

    try {
      const emptyData = {
        monthlyRevenue: Array(12).fill(0)
      };

      const monthlyChartConfig = this.chartUtils.getMonthlyRevenueConfig(emptyData);

      // Eğer chart zaten varsa, önce destroy et
      if (this.monthlySalesChart) {
        this.monthlySalesChart.destroy();
      }

      this.monthlySalesChart = new Chart(ctx, monthlyChartConfig);
    } catch (error) {
      console.error('Empty license yearly trend chart creation error:', error);
    }
  }



  private updateCharts(): void {
    setTimeout(() => {
      if (this.paymentChart) {
        const paymentData = {
          totalCash: this.totalCash,
          totalCreditCard: this.totalCreditCard,
          totalTransfer: this.totalTransfer,
          totalDebt: 0
        };

        this.paymentChart.data.datasets[0].data = [
          paymentData.totalCash,
          paymentData.totalCreditCard,
          paymentData.totalTransfer
        ];
        this.paymentChart.update();
      } else {
        this.initializeCharts();
      }

      // Yearly trend chart'ı yeniden yükle
      this.loadYearlyTrendData();
    }, 100);
  }

  getUserName(userID: number): string {
    const user = this.users[userID];
    return user ? `${user.firstName} ${user.lastName}` : 'Bilinmeyen Kullanıcı';
  }

  getUserEmail(userID: number): string {
    const user = this.users[userID];
    return user ? user.email : '';
  }

  getPackageName(packageID: number): string {
    const pkg = this.packages[packageID];
    return pkg ? pkg.name : 'Bilinmeyen Paket';
  }

  calculateTotal(): number {
    return this.transactions.reduce((total, transaction) => total + transaction.amount, 0);
  }

  resetFilters(): void {
    this.selectedAdmin = null;
    this.adminSearchControl.setValue('');
    this.startDate = '';
    this.endDate = '';
    this.currentPage = 1;
    this.isFiltered = false;
    this.loadTransactions(() => {
      this.loadTotals();
      this.updateCharts();
    });
  }

  getTotalValuesTitle(): string {
    if (this.selectedAdmin) {
      return `Toplam Değerler (${this.selectedAdmin.firstName} ${this.selectedAdmin.lastName})`;
    }
    if (this.isFiltered) {
      return 'Toplam Değerler (Filtrelenmiş)';
    }
    return `Toplam Değerler (${this.currentMonthStart.toLocaleString(
      'default',
      { month: 'long' }
    )} ${this.currentMonthStart.getFullYear()})`;
  }

  deleteTransaction(transaction: LicenseTransaction): void {
    this.dialogService.confirmDelete(
      'Lisans İşlemi',
      `${this.getUserName(transaction.userID)} - ${transaction.amount}₺`
    ).subscribe(result => {
      if (result) {
        this.isLoading = true;

        this.licenseTransactionService.delete(transaction.licenseTransactionID).subscribe({
          next: (response) => {
            this.toastr.success('Lisans işlemi başarıyla silindi');
            this.loadTransactions(() => {
              this.loadTotals();
              this.updateCharts();
            });
          },
          error: (error) => {
            this.toastr.error('Lisans işlemi silinirken bir hata oluştu');
            this.isLoading = false;
          },
        });
      }
    });
  }

  exportToExcel(): void {
    if (!this.rateLimitService.canMakeRequest('excel-export')) {
      this.toastr.warning('Çok sık Excel export yapıyorsunuz. Lütfen bekleyin.');
      return;
    }

    this.isExporting = true;

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Lisans İşlemleri');

    // Add headers with summary columns
    worksheet.columns = [
      { header: 'Tarih', key: 'date', width: 20 },
      { header: 'Admin/Salon', key: 'admin', width: 25 },
      { header: 'Email', key: 'email', width: 30 },
      { header: 'Paket', key: 'package', width: 20 },
      { header: 'Ödeme Tipi', key: 'paymentMethod', width: 15 },
      { header: 'Tutar', key: 'amount', width: 15, style: { numFmt: '#,##0.00 ₺' } },
      { header: '', key: 'empty1', width: 5 }, // Boş sütun
      { header: 'ÖZET', key: 'summary', width: 20 }, // Özet başlığı
      { header: 'TUTAR', key: 'summaryAmount', width: 15, style: { numFmt: '#,##0.00 ₺' } } // Özet tutar
    ];

    // Calculate totals from current transactions
    const exportTotals = this.calculateExportTotals();

    // Add data
    let rowIndex = 2; // Başlık satırından sonra
    this.transactions.forEach(transaction => {
      worksheet.addRow({
        date: new Date(transaction.transactionDate).toLocaleDateString('tr-TR'),
        admin: this.getUserName(transaction.userID),
        email: this.getUserEmail(transaction.userID),
        package: this.getPackageName(transaction.licensePackageID),
        paymentMethod: transaction.paymentMethod,
        amount: transaction.amount,
        empty1: '',
        summary: '',
        summaryAmount: ''
      });
      rowIndex++;
    });

    // Add summary table on the right
    this.addLicenseSummaryTable(worksheet, exportTotals);

    // Style the worksheet
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Generate and save file
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const fileName = `lisans-islemleri-${new Date().toISOString().split('T')[0]}.xlsx`;
      saveAs(blob, fileName);
      this.isExporting = false;
      this.toastr.success('Excel dosyası başarıyla indirildi');
    }).catch(error => {
      console.error('Excel export error:', error);
      this.isExporting = false;
      this.toastr.error('Excel dosyası oluşturulurken bir hata oluştu');
    });
  }

  // License transactions için toplamları hesapla
  private calculateExportTotals() {
    const cashTotal = this.transactions
      .filter(t => t.paymentMethod.includes('Nakit'))
      .reduce((sum, t) => sum + t.amount, 0);

    const creditCardTotal = this.transactions
      .filter(t => t.paymentMethod.includes('Kredi Kartı'))
      .reduce((sum, t) => sum + t.amount, 0);

    const transferTotal = this.transactions
      .filter(t => t.paymentMethod.includes('Havale') || t.paymentMethod.includes('EFT'))
      .reduce((sum, t) => sum + t.amount, 0);

    return {
      cash: cashTotal,
      creditCard: creditCardTotal,
      transfer: transferTotal,
      total: cashTotal + creditCardTotal + transferTotal
    };
  }

  // Excel'e lisans özet tablosu ekle
  private addLicenseSummaryTable(worksheet: any, totals: any) {
    // Özet tablosu başlığını formatla
    const headerRow = worksheet.getRow(1);
    headerRow.getCell(8).value = 'ÖZET';
    headerRow.getCell(9).value = 'TUTAR';
    headerRow.getCell(8).font = { bold: true, size: 12 };
    headerRow.getCell(9).font = { bold: true, size: 12 };
    headerRow.getCell(8).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };
    headerRow.getCell(9).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };

    // Özet verilerini ekle
    let currentRow = 2; // İlk satırdan başla

    // Nakit
    worksheet.getCell(`H${currentRow}`).value = 'Nakit';
    worksheet.getCell(`I${currentRow}`).value = totals.cash;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    currentRow++;

    // Kredi Kartı
    worksheet.getCell(`H${currentRow}`).value = 'Kredi Kartı';
    worksheet.getCell(`I${currentRow}`).value = totals.creditCard;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    currentRow++;

    // Havale-EFT
    worksheet.getCell(`H${currentRow}`).value = 'Havale - EFT';
    worksheet.getCell(`I${currentRow}`).value = totals.transfer;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    currentRow++;

    // Boş satır
    currentRow++;

    // Toplam
    worksheet.getCell(`H${currentRow}`).value = 'TOPLAM';
    worksheet.getCell(`I${currentRow}`).value = totals.total;
    worksheet.getCell(`I${currentRow}`).numFmt = '#,##0.00 ₺';
    worksheet.getCell(`H${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } }; // Yeşil ve kalın
    worksheet.getCell(`I${currentRow}`).font = { bold: true, size: 12, color: { argb: 'FF008000' } };
    worksheet.getCell(`H${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };
    worksheet.getCell(`I${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0FFF0' } };

    // Özet tablosu etrafına border ekle
    for (let row = 1; row <= currentRow; row++) {
      for (let col = 8; col <= 9; col++) {
        const cell = worksheet.getCell(row, col);
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      }
    }
  }

  getAvatarColor(name: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadTransactions();
    }
  }

  getPaginationRange(): number[] {
    const range = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);

    for (let i = start; i <= end; i++) {
      range.push(i);
    }
    return range;
  }
}
