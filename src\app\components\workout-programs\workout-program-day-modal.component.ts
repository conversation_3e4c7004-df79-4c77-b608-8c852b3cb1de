import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { faPlus, faMinus, faEdit, faSave, faTimes, faSearch } from '@fortawesome/free-solid-svg-icons';

import {
  WorkoutProgramDay,
  WorkoutProgramExercise
} from '../../models/workout-program.models';
import { ExerciseSelectionModalComponent } from './exercise-selection-modal.component';

export interface WorkoutProgramDayModalData {
  day?: WorkoutProgramDay;
  dayNumber: number;
  mode: 'add' | 'edit';
}

@Component({
  selector: 'app-workout-program-day-modal',
  templateUrl: './workout-program-day-modal.component.html',
  styleUrls: ['./workout-program-day-modal.component.css'],
  standalone: false
})
export class WorkoutProgramDayModalComponent implements OnInit {
  // Icons
  faPlus = faPlus;
  faMinus = faMinus;
  faEdit = faEdit;
  faSave = faSave;
  faTimes = faTimes;
  faSearch = faSearch;

  // Form
  dayForm!: FormGroup;
  isSubmitting = false;

  // Data

  mode: 'add' | 'edit';
  dayNumber: number;

  constructor(
    private fb: FormBuilder,
    private toastrService: ToastrService,
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<WorkoutProgramDayModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: WorkoutProgramDayModalData
  ) {
    this.mode = data.mode;
    this.dayNumber = data.dayNumber;
  }

  ngOnInit(): void {
    this.initializeForm();
    if (this.data.day && this.mode === 'edit') {
      this.loadDayData();
    }
  }

  initializeForm(): void {
    this.dayForm = this.fb.group({
      exercises: this.fb.array([])
    });
  }

  get exercises(): FormArray {
    return this.dayForm.get('exercises') as FormArray;
  }

  loadDayData(): void {
    if (this.data.day) {
      // Egzersizleri yükle
      if (this.data.day.exercises && this.data.day.exercises.length > 0) {
        this.data.day.exercises.forEach(exercise => {
          this.addExerciseFromData(exercise);
        });
      }
    }
  }

  createExerciseFormGroup(exercise?: WorkoutProgramExercise): FormGroup {
    return this.fb.group({
      exerciseType: [exercise?.exerciseType || 'System', [Validators.required]],
      exerciseID: [exercise?.exerciseID || null, [Validators.required]],
      exerciseName: [exercise?.exerciseName || '', [Validators.required]],
      orderIndex: [exercise?.orderIndex || this.exercises.length + 1, [Validators.required]],
      sets: [exercise?.sets || 4, [Validators.required, Validators.min(1), Validators.max(10)]],
      reps: [exercise?.reps || '12', [Validators.required]],
      notes: [exercise?.notes || '']
    });
  }

  addExercise(): void {
    const exerciseGroup = this.createExerciseFormGroup();
    this.exercises.push(exerciseGroup);

    // İlk egzersiz değilse otomatik kaydır
    if (this.exercises.length > 1) {
      this.scrollToNewExercise();
    }
  }

  addExerciseFromData(exercise: WorkoutProgramExercise): void {
    const exerciseGroup = this.createExerciseFormGroup(exercise);
    this.exercises.push(exerciseGroup);
  }

  private scrollToNewExercise(): void {
    // DOM güncellemesini bekle
    setTimeout(() => {
      const lastExerciseIndex = this.exercises.length - 1;
      const lastCard = document.getElementById(`exercise-card-${lastExerciseIndex}`);

      if (lastCard) {
        lastCard.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });

        // Hafif bir highlight efekti için
        lastCard.style.transform = 'scale(1.02)';
        setTimeout(() => {
          lastCard.style.transform = 'scale(1)';
        }, 300);
      }
    }, 150);
  }

  removeExercise(index: number): void {
    this.exercises.removeAt(index);
    this.updateExerciseOrder();
  }

  updateExerciseOrder(): void {
    this.exercises.controls.forEach((exerciseControl, index) => {
      exerciseControl.get('orderIndex')?.setValue(index + 1);
    });
  }





  openExerciseSelectionModal(exerciseIndex: number): void {
    const exerciseControl = this.exercises.at(exerciseIndex);
    const currentExercise = exerciseControl.value;

    const dialogRef = this.dialog.open(ExerciseSelectionModalComponent, {
      width: '1200px',
      maxWidth: '95vw',
      height: '85vh',
      maxHeight: '85vh',
      data: {
        selectedExercise: currentExercise.exerciseID ? {
          exerciseType: currentExercise.exerciseType,
          exerciseID: currentExercise.exerciseID,
          exerciseName: currentExercise.exerciseName
        } : undefined
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Seçilen egzersizi forma uygula
        exerciseControl.patchValue({
          exerciseType: result.exerciseType,
          exerciseID: result.exerciseID,
          exerciseName: result.exerciseName
        });
      }
    });
  }

  moveExerciseUp(index: number): void {
    if (index > 0) {
      const exerciseArray = this.exercises;
      const currentExercise = exerciseArray.at(index);
      const previousExercise = exerciseArray.at(index - 1);
      
      exerciseArray.setControl(index, previousExercise);
      exerciseArray.setControl(index - 1, currentExercise);
      
      this.updateExerciseOrder();
    }
  }

  moveExerciseDown(index: number): void {
    if (index < this.exercises.length - 1) {
      const exerciseArray = this.exercises;
      const currentExercise = exerciseArray.at(index);
      const nextExercise = exerciseArray.at(index + 1);
      
      exerciseArray.setControl(index, nextExercise);
      exerciseArray.setControl(index + 1, currentExercise);
      
      this.updateExerciseOrder();
    }
  }

  onSubmit(): void {
    if (this.dayForm.valid) {
      this.isSubmitting = true;
      
      const dayData = {
        exercises: this.dayForm.value.exercises || []
      };

      this.dialogRef.close(dayData);
    } else {
      this.markFormGroupTouched(this.dayForm);
      this.toastrService.warning('Lütfen tüm gerekli alanları doldurun', 'Uyarı');
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          }
        });
      }
    });
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.dayForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isExerciseFieldInvalid(exerciseIndex: number, fieldName: string): boolean {
    const exerciseControl = this.exercises.at(exerciseIndex);
    const field = exerciseControl.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.dayForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['min']) return `En az ${field.errors['min'].min} olmalıdır`;
      if (field.errors['max']) return `En fazla ${field.errors['max'].max} olmalıdır`;
    }
    return '';
  }

  getExerciseFieldError(exerciseIndex: number, fieldName: string): string {
    const exerciseControl = this.exercises.at(exerciseIndex);
    const field = exerciseControl.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['min']) return `En az ${field.errors['min'].min} olmalıdır`;
      if (field.errors['max']) return `En fazla ${field.errors['max'].max} olmalıdır`;
    }
    return '';
  }

  getModalTitle(): string {
    return this.mode === 'add' ? 'Yeni Gün Ekle' : 'Egzersizleri Düzenle';
  }

  // Summary calculation methods
  getTotalSets(): number {
    return this.exercises.controls.reduce((total, exerciseControl) => {
      const sets = exerciseControl.get('sets')?.value || 0;
      return total + sets;
    }, 0);
  }



  // Exercise management helpers
  hasExercises(): boolean {
    return this.exercises.length > 0;
  }

  isExerciseComplete(index: number): boolean {
    const exerciseControl = this.exercises.at(index);
    const exerciseName = exerciseControl.get('exerciseName')?.value;
    const sets = exerciseControl.get('sets')?.value;
    const reps = exerciseControl.get('reps')?.value;

    return !!(exerciseName && sets && reps);
  }

  getExerciseCompletionPercentage(): number {
    if (this.exercises.length === 0) return 0;

    const completeExercises = this.exercises.controls.filter((_, index) =>
      this.isExerciseComplete(index)
    ).length;

    return Math.round((completeExercises / this.exercises.length) * 100);
  }

  // Set manipulation methods
  increaseSets(exerciseIndex: number): void {
    const exerciseControl = this.exercises.at(exerciseIndex);
    const currentSets = exerciseControl.get('sets')?.value || 1;
    if (currentSets < 10) {
      exerciseControl.get('sets')?.setValue(currentSets + 1);
    }
  }

  decreaseSets(exerciseIndex: number): void {
    const exerciseControl = this.exercises.at(exerciseIndex);
    const currentSets = exerciseControl.get('sets')?.value || 1;
    if (currentSets > 1) {
      exerciseControl.get('sets')?.setValue(currentSets - 1);
    }
  }

  // Scroll methods
  scrollToTop(): void {
    const modalBody = document.querySelector('.modern-modal-body');
    if (modalBody) {
      modalBody.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  }
}
