import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MembershipDetail } from '../../models/membershipDetail';

export interface MembershipSelectionDialogData {
  memberName: string;
  memberId: number;
  activeMemberships: MembershipDetail[];
}

@Component({
  selector: 'app-membership-selection-dialog',
  standalone: false,
  templateUrl: './membership-selection-dialog.component.html',
  styleUrls: ['./membership-selection-dialog.component.css']
})
export class MembershipSelectionDialogComponent {
  selectedMembershipId: number | null = null;

  constructor(
    public dialogRef: MatDialogRef<MembershipSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MembershipSelectionDialogData
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onSelect(): void {
    if (this.selectedMembershipId) {
      this.dialogRef.close(this.selectedMembershipId);
    }
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }

  getAvatarColor(name: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];

    if (!name) return colors[0];

    const hash = name.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    return colors[Math.abs(hash) % colors.length];
  }
}
