import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-freeze-membership-dialog',
  template: `
    <div class="modern-dialog zoom-in freeze-dialog">
      <div class="modern-card-header">
        <h2>Üyelik Dondurma</h2>
        <button class="modern-btn-icon" (click)="onNoClick()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <form [formGroup]="freezeForm" (ngSubmit)="onSubmit()">
        <div class="modern-card-body">
          <div class="dialog-icon">
            <i class="fas fa-snowflake"></i>
          </div>
          
          <div class="member-info">
            <div class="modern-avatar" [style.background-color]="getAvatarColor(data.memberName)">
              {{ getInitials(data.memberName) }}
            </div>
            <div class="member-name">{{ data.memberName }}</div>
          </div>
          
          <p class="dialog-message">{{ data.memberName }} isimli üyenin üyeliğini dondurmak üzeresiniz.</p>
          
          <div class="modern-form-group">
            <label class="modern-form-label">Dondurma Süresi (Gün)</label>
            <div class="input-group">
              <div class="input-group-text">
                <i class="fas fa-calendar-day"></i>
              </div>
              <input
                type="number"
                class="modern-form-control"
                formControlName="freezeDays"
                min="1"
                placeholder="Gün sayısı"
              >
            </div>
            <div *ngIf="freezeForm.get('freezeDays')?.invalid && freezeForm.get('freezeDays')?.touched" class="error-message">
              <div *ngIf="freezeForm.get('freezeDays')?.hasError('required')">
                Dondurma süresi gereklidir
              </div>
              <div *ngIf="freezeForm.get('freezeDays')?.hasError('min')">
                En az 1 gün olmalıdır
              </div>
            </div>
          </div>
          
          <div class="freeze-info">
           
            <div class="freeze-info-item">
              <i class="fas fa-ban"></i>
              <span>Dondurma süresince üye giriş yapamaz.</span>
            </div>
          </div>
        </div>
        
        <div class="modern-card-footer">
          <button 
            type="button" 
            class="modern-btn modern-btn-outline-secondary" 
            (click)="onNoClick()">
            <i class="fas fa-times modern-btn-icon"></i> İptal
          </button>
          <button 
            type="submit" 
            class="modern-btn modern-btn-info" 
            [disabled]="!freezeForm.valid">
            <i class="fas fa-snowflake modern-btn-icon"></i> Dondur
          </button>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .modern-dialog {
      min-width: 320px;
      max-width: 100%;
      overflow: hidden;
      border-radius: var(--border-radius-lg);
      box-shadow: var(--shadow-md);
      background-color: var(--bg-primary);
      color: var(--text-primary);
    }
    
    .modern-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-md) var(--spacing-lg);
      border-bottom: 1px solid var(--border-color);
      background-color: var(--info-light);
      color: var(--text-primary);
    }
    
    .modern-card-header h2 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .modern-card-body {
      padding: var(--spacing-lg);
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    
    .dialog-icon {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: var(--spacing-md);
      font-size: 1.75rem;
      background-color: var(--info-light);
      color: var(--info);
    }
    
    .member-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: var(--spacing-md);
    }
    
    .modern-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      margin-bottom: var(--spacing-xs);
    }
    
    .member-name {
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .dialog-message {
      font-size: 1rem;
      margin: 0 0 var(--spacing-md);
      line-height: 1.5;
      max-width: 400px;
      color: var(--text-primary);
    }
    
    .modern-card-footer {
      padding: var(--spacing-md) var(--spacing-lg);
      border-top: 1px solid var(--border-color);
      background-color: var(--bg-secondary);
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-sm);
    }
    
    .modern-btn-icon {
      background: none;
      border: none;
      cursor: pointer;
      color: var(--text-secondary);
      font-size: 1rem;
      padding: 0.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: color 0.3s ease;
    }
    
    .modern-btn-icon:hover {
      color: var(--text-primary);
    }
    
    .modern-form-group {
      width: 100%;
      margin-bottom: var(--spacing-md);
    }
    
    .modern-form-label {
      display: block;
      margin-bottom: var(--spacing-xs);
      font-weight: 500;
      text-align: left;
    }
    
    .input-group {
      display: flex;
    }
    
    .input-group-text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background-color: var(--info-light);
      color: var(--info);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    }
    
    .modern-form-control {
      flex: 1;
      padding: 0.5rem 0.75rem;
      border: 1px solid var(--border-color);
      border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
      background-color: var(--bg-primary);
      color: var(--text-primary);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    .modern-form-control:focus {
      border-color: var(--info);
      outline: none;
      box-shadow: 0 0 0 0.2rem var(--info-light);
    }
    
    .error-message {
      color: var(--danger);
      font-size: 0.875rem;
      margin-top: 0.25rem;
      text-align: left;
    }
    
    .freeze-info {
      width: 100%;
      margin-top: var(--spacing-md);
      padding: var(--spacing-sm);
      background-color: var(--bg-secondary);
      border-radius: var(--border-radius-md);
      text-align: left;
    }
    
    .freeze-info-item {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-xs);
      font-size: 0.875rem;
      color: var(--text-primary);
    }
    
    .freeze-info-item:last-child {
      margin-bottom: 0;
    }
    
    .freeze-info-item i {
      margin-right: var(--spacing-xs);
      color: var(--info);
    }
    
    /* Dark mode specific styles */
    [data-theme="dark"] .freeze-dialog {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
    }
    
    [data-theme="dark"] .modern-card-header {
      background-color: rgba(100, 181, 246, 0.2);
      color: var(--text-primary);
    }
    
    [data-theme="dark"] .dialog-message,
    [data-theme="dark"] .member-name,
    [data-theme="dark"] .modern-form-label {
      color: var(--text-primary);
    }
    
    [data-theme="dark"] .freeze-info {
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
    }
    
    [data-theme="dark"] .freeze-info-item {
      color: var(--text-primary);
    }
    
    [data-theme="dark"] .modern-card-footer {
      background-color: var(--bg-tertiary);
    }
    
    @media screen and (max-width: 480px) {
      .modern-dialog {
        min-width: 280px;
      }
      
      .modern-card-header {
        padding: var(--spacing-sm) var(--spacing-md);
      }
      
      .modern-card-body {
        padding: var(--spacing-md);
      }
      
      .modern-card-footer {
        padding: var(--spacing-sm) var(--spacing-md);
      }
      
      .dialog-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
      }
    }
  `],
  standalone: false
})
export class FreezeMembershipDialogComponent {
  freezeForm: FormGroup;

  constructor(
    public dialogRef: MatDialogRef<FreezeMembershipDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder
  ) {
    this.freezeForm = this.fb.group({
      freezeDays: ['', [Validators.required, Validators.min(1)]]
    });
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {
    if (this.freezeForm.valid) {
      this.dialogRef.close(this.freezeForm.value.freezeDays);
    }
  }
  
  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }
  
  getAvatarColor(name: string): string {
    if (!name) return '#4cc9f0';
    
    // Generate a consistent color based on the name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    // Convert to hex color
    const colors = [
      '#4cc9f0', // info
      '#4361ee', // primary
      '#3a0ca3', // primary-dark
      '#7209b7', // purple
      '#f72585', // pink
      '#4895ef', // blue
      '#560bad', // indigo
      '#b5179e', // magenta
    ];
    
    return colors[Math.abs(hash) % colors.length];
  }
}
