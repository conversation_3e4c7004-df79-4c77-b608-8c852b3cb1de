<!-- Modal Container -->
<form [formGroup]="dayForm" (ngSubmit)="onSubmit()" class="modal-form-container">
  <!-- Modern Modal Header -->
  <div class="modern-modal-header">
    <div class="d-flex align-items-center">
      <div class="modal-icon me-3">
        <fa-icon [icon]="faEdit" class="text-primary"></fa-icon>
      </div>
      <div>
        <h4 class="modal-title mb-0">{{getModalTitle()}}</h4>
        <small class="text-muted">{{dayNumber}}. Gün için egzersizleri düzenleyin</small>
      </div>
    </div>
    <button type="button" class="modern-btn-close" (click)="onCancel()">
      <fa-icon [icon]="faTimes"></fa-icon>
    </button>
  </div>

  <!-- Modal Body -->
  <div class="modern-modal-body">


    <!-- Add Exercise Button - Sticky -->
    <div class="add-exercise-section-sticky mb-4">
      <button
        type="button"
        class="add-exercise-btn"
        (click)="addExercise()">
        <div class="add-exercise-content">
          <div class="add-exercise-icon">
            <fa-icon [icon]="faPlus"></fa-icon>
          </div>
          <div class="add-exercise-text">
            <div class="add-exercise-title">Yeni Egzersiz Ekle</div>
            <div class="add-exercise-subtitle">Bu güne egzersiz eklemek için tıklayın</div>
          </div>
        </div>
      </button>
    </div>

    <!-- Exercises List -->
    <div *ngIf="hasExercises()" class="exercises-container">
      <div class="exercises-header mb-3">
        <h6 class="mb-0">Egzersiz Listesi</h6>
        <div class="completion-indicator">
          <div class="completion-bar">
            <div
              class="completion-progress"
              [style.width.%]="getExerciseCompletionPercentage()">
            </div>
          </div>
          <small class="text-muted">{{getExerciseCompletionPercentage()}}% tamamlandı</small>
        </div>
      </div>

      <div formArrayName="exercises" class="exercises-list">
        <div
          *ngFor="let exerciseControl of exercises.controls; let i = index"
          class="modern-exercise-card"
          [class.complete]="isExerciseComplete(i)"
          [class.incomplete]="!isExerciseComplete(i)"
          [formGroupName]="i"
          [id]="'exercise-card-' + i">

          <!-- Exercise Card Header -->
          <div class="exercise-card-header">
            <div class="exercise-info">
              <div class="exercise-number">
                <span class="number">{{i + 1}}</span>
                <fa-icon
                  [icon]="isExerciseComplete(i) ? faEdit : faPlus"
                  [class.text-success]="isExerciseComplete(i)"
                  [class.text-warning]="!isExerciseComplete(i)">
                </fa-icon>
              </div>
              <div class="exercise-title">
                <div class="exercise-name">
                  {{exerciseControl.get('exerciseName')?.value || 'Egzersiz seçilmedi'}}
                </div>
                <div class="exercise-details" *ngIf="isExerciseComplete(i)">
                  {{exerciseControl.get('sets')?.value}} set × {{exerciseControl.get('reps')?.value}} tekrar
                </div>
              </div>
            </div>
            <div class="exercise-actions">
              <button
                type="button"
                class="action-btn move-btn"
                (click)="moveExerciseUp(i)"
                [disabled]="i === 0"
                title="Yukarı Taşı">
                <fa-icon [icon]="faPlus" style="transform: rotate(-90deg);"></fa-icon>
              </button>
              <button
                type="button"
                class="action-btn move-btn"
                (click)="moveExerciseDown(i)"
                [disabled]="i === exercises.length - 1"
                title="Aşağı Taşı">
                <fa-icon [icon]="faPlus" style="transform: rotate(90deg);"></fa-icon>
              </button>
              <button
                type="button"
                class="action-btn delete-btn"
                (click)="removeExercise(i)"
                title="Sil">
                <fa-icon [icon]="faMinus"></fa-icon>
              </button>
            </div>
          </div>

          <!-- Exercise Card Body -->
          <div class="exercise-card-body">
            <div class="row g-3">
              <!-- Exercise Selection -->
              <div class="col-12">
                <div class="exercise-selection-field">
                  <label class="field-label">
                    <fa-icon [icon]="faSearch" class="me-2"></fa-icon>
                    Egzersiz Seçimi <span class="text-danger">*</span>
                  </label>
                  <div
                    class="exercise-selector"
                    [class.selected]="exerciseControl.get('exerciseName')?.value"
                    [class.is-invalid]="isExerciseFieldInvalid(i, 'exerciseName')"
                    (click)="openExerciseSelectionModal(i)">
                    <div class="selector-content">
                      <div class="selector-text">
                        <span *ngIf="!exerciseControl.get('exerciseName')?.value" class="placeholder">
                          Egzersiz seçmek için tıklayın
                        </span>
                        <span *ngIf="exerciseControl.get('exerciseName')?.value" class="selected-exercise">
                          {{exerciseControl.get('exerciseName')?.value}}
                        </span>
                      </div>
                      <div class="selector-icon">
                        <fa-icon [icon]="faSearch"></fa-icon>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="isExerciseFieldInvalid(i, 'exerciseName')" class="field-error">
                    {{getExerciseFieldError(i, 'exerciseName')}}
                  </div>
                  <!-- Hidden input for form validation -->
                  <input type="hidden" formControlName="exerciseName">
                </div>
              </div>

              <!-- Sets and Reps -->
              <div class="col-md-6">
                <div class="sets-reps-group">
                  <div class="row g-2">
                    <div class="col-6">
                      <label class="field-label">Set <span class="text-danger">*</span></label>
                      <div class="number-input-wrapper">
                        <button
                          type="button"
                          class="number-btn decrease"
                          (click)="decreaseSets(i)"
                          [disabled]="(exerciseControl.get('sets')?.value || 1) <= 1">
                          <fa-icon [icon]="faMinus"></fa-icon>
                        </button>
                        <input
                          type="number"
                          class="number-input"
                          [class.is-invalid]="isExerciseFieldInvalid(i, 'sets')"
                          formControlName="sets"
                          min="1" max="10"
                          readonly>
                        <button
                          type="button"
                          class="number-btn increase"
                          (click)="increaseSets(i)"
                          [disabled]="(exerciseControl.get('sets')?.value || 1) >= 10">
                          <fa-icon [icon]="faPlus"></fa-icon>
                        </button>
                      </div>
                      <div *ngIf="isExerciseFieldInvalid(i, 'sets')" class="field-error">
                        {{getExerciseFieldError(i, 'sets')}}
                      </div>
                    </div>
                    <div class="col-6">
                      <label class="field-label">Tekrar <span class="text-danger">*</span></label>
                      <input
                        type="text"
                        class="modern-form-control"
                        [class.is-invalid]="isExerciseFieldInvalid(i, 'reps')"
                        formControlName="reps"
                        placeholder="12 veya 8-12">
                      <div *ngIf="isExerciseFieldInvalid(i, 'reps')" class="field-error">
                        {{getExerciseFieldError(i, 'reps')}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Notes -->
              <div class="col-md-6">
                <div class="notes-field">
                  <label class="field-label">
                    <fa-icon [icon]="faEdit" class="me-2"></fa-icon>
                    Notlar
                  </label>
                  <textarea
                    class="modern-form-control"
                    formControlName="notes"
                    rows="3"
                    placeholder="Egzersiz ile ilgili özel notlar..."></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!hasExercises()" class="empty-state">
      <div class="empty-state-content">
        <div class="empty-state-icon">
          <fa-icon [icon]="faPlus"></fa-icon>
        </div>
        <div class="empty-state-text">
          <h6>Henüz egzersiz eklenmemiş</h6>
          <p>Bu güne egzersiz eklemek için yukarıdaki "Yeni Egzersiz Ekle" butonuna tıklayın</p>
        </div>
      </div>
    </div>

    <!-- Scroll to Top Button -->
    <div *ngIf="hasExercises() && exercises.length > 3" class="scroll-to-top-container">
      <button
        type="button"
        class="scroll-to-top-btn"
        (click)="scrollToTop()"
        title="Başa dön">
        <fa-icon [icon]="faPlus" style="transform: rotate(-90deg);"></fa-icon>
      </button>
    </div>
  </div>

  <!-- Modern Modal Footer -->
  <div class="modern-modal-footer">
    <div class="footer-content">
      <div class="footer-info">
        <small class="text-muted">
          <fa-icon [icon]="faEdit" class="me-1"></fa-icon>
          {{exercises.length}} egzersiz, {{getTotalSets()}} set
        </small>
      </div>
      <div class="footer-actions">
        <button
          type="button"
          class="modern-btn modern-btn-outline-secondary"
          (click)="onCancel()">
          <fa-icon [icon]="faTimes" class="me-2"></fa-icon>
          İptal
        </button>
        <button
          type="submit"
          class="modern-btn modern-btn-primary"
          [disabled]="isSubmitting || dayForm.invalid">
          <fa-icon [icon]="faSave" class="me-2"></fa-icon>
          <span *ngIf="!isSubmitting">Değişiklikleri Kaydet</span>
          <span *ngIf="isSubmitting">Kaydediliyor...</span>
        </button>
      </div>
    </div>
  </div>
</form>
