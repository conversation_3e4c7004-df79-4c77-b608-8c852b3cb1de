import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { BaseApiService } from '../../services/baseApiService';
import { RateLimitService } from '../../services/rate-limit.service';
import { UserService } from '../../services/user-service.service';
import { MemberService } from '../../services/member.service';
import { Subject, interval, Subscription } from 'rxjs';
import { takeUntil, take } from 'rxjs/operators';

interface TestResult {
  requestNumber: number;
  timestamp: string;
  status: 'success' | 'rate-limited' | 'error';
  statusCode?: number;
  message?: string;
  responseTime?: number;
}

@Component({
  selector: 'app-rate-limit-test',
  templateUrl: './rate-limit-test.component.html',
  styleUrls: ['./rate-limit-test.component.css'],
  standalone: false
})
export class RateLimitTestComponent extends BaseApiService implements OnInit, OnDestroy {

  // Test durumu
  isTestRunning = false;
  testResults: TestResult[] = [];
  currentRequestCount = 0;
  totalRequests = 120; // 100'den fazla test için
  requestInterval = 50; // milliseconds

  // İstatistikler
  successCount = 0;
  rateLimitCount = 0;
  errorCount = 0;
  averageResponseTime = 0;

  // Test seçenekleri
  selectedEndpoint = '/api/user/getall';
  testEndpoints = [
    { value: '/api/user/getall', label: 'User GetAll (1dk/30 limit)', limit: '30/dakika' },
    { value: '/api/member/getall', label: 'Member GetAll (1dk/30 limit)', limit: '30/dakika' },
    { value: '/api/user/profile', label: 'User Profile (1dk/15 limit)', limit: '15/dakika' },
    { value: '/api/member/scannumber', label: 'Member Scan (10s/1 limit)', limit: '1/10saniye', method: 'POST' },
    { value: '/api/auth/change-password', label: 'Change Password (5dk/5 limit)', limit: '5/5dakika', method: 'POST' },
    { value: '/api/member/gettodayentries', label: 'Today Entries (30s/2 limit)', limit: '2/30saniye' },
    { value: '/api/member/gettotalregisteredmembers', label: 'Total Members (1dk/10 limit)', limit: '10/dakika' },
    { value: '/api/payment/getpaymenthistorypaginated', label: 'Payment History (30s/5 limit)', limit: '5/30saniye' },
    { value: '/api/products/getall', label: 'Products GetAll (1dk/20 limit)', limit: '20/dakika' },
    { value: '/api/transactions/getall', label: 'Transactions GetAll (30s/5 limit)', limit: '5/30saniye' },
    { value: '/api/transactions/add', label: 'Add Transaction (5s/1 limit)', limit: '1/5saniye', method: 'POST' },
    { value: '/api/products/add', label: 'Add Product (30s/3 limit)', limit: '3/30saniye', method: 'POST' },
    { value: '/api/member/add', label: 'Add Member (30s/2 limit)', limit: '2/30saniye', method: 'POST' }
  ];

  // Reactive streams
  private destroy$ = new Subject<void>();
  private testSubscription?: Subscription;

  constructor(
    private httpClient: HttpClient,
    private toastrService: ToastrService,
    private rateLimitService: RateLimitService,
    private userService: UserService,
    private memberService: MemberService
  ) {
    super();
  }

  ngOnInit(): void {
    this.toastrService.info('Rate Limit Test sayfası yüklendi. Test başlatmak için butona tıklayın.', 'Bilgi');
  }

  ngOnDestroy(): void {
    this.stopTest();
    this.destroy$.next();
    this.destroy$.complete();
  }

  startTest(): void {
    if (this.isTestRunning) {
      this.toastrService.warning('Test zaten çalışıyor!', 'Uyarı');
      return;
    }

    // Test verilerini sıfırla
    this.resetTestData();
    this.isTestRunning = true;

    this.toastrService.info(`${this.selectedEndpoint} endpoint'i için ${this.totalRequests} istek gönderiliyor...`, 'Test Başlatıldı');

    // Test başlat
    this.runTest();
  }

  stopTest(): void {
    if (this.testSubscription) {
      this.testSubscription.unsubscribe();
      this.testSubscription = undefined;
    }
    this.isTestRunning = false;

    if (this.currentRequestCount > 0) {
      this.toastrService.info(`Test durduruldu. ${this.currentRequestCount} istek gönderildi.`, 'Test Durduruldu');
      this.calculateStatistics();
    }
  }

  private resetTestData(): void {
    this.testResults = [];
    this.currentRequestCount = 0;
    this.successCount = 0;
    this.rateLimitCount = 0;
    this.errorCount = 0;
    this.averageResponseTime = 0;
  }

  private runTest(): void {
    // Interval ile istekleri gönder
    this.testSubscription = interval(this.requestInterval)
      .pipe(
        take(this.totalRequests),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (index) => {
          this.sendRequest(index + 1);
        },
        complete: () => {
          this.isTestRunning = false;
          this.calculateStatistics();
          this.toastrService.success('Test tamamlandı!', 'Başarılı');
        }
      });
  }

  private async sendRequest(requestNumber: number): Promise<void> {
    const startTime = Date.now();
    const timestamp = new Date().toLocaleTimeString();

    try {
      let response: any;
      const selectedEndpointData = this.testEndpoints.find(e => e.value === this.selectedEndpoint);

      if (selectedEndpointData?.method === 'POST') {
        // POST istekleri için özel body'ler
        response = await this.sendPostRequest();
      } else {
        // GET istekleri
        response = await this.httpClient.get(`${this.apiUrl}${this.selectedEndpoint.replace('/api/', '')}`).toPromise();
      }

      const responseTime = Date.now() - startTime;

      this.addTestResult({
        requestNumber,
        timestamp,
        status: 'success',
        statusCode: 200,
        message: 'Başarılı',
        responseTime
      });

      this.successCount++;

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const statusCode = error.status || 0;

      let status: 'rate-limited' | 'error' = 'error';
      let message = 'Bilinmeyen hata';

      if (statusCode === 429) {
        status = 'rate-limited';
        message = 'Rate limit aşıldı (429)';
        this.rateLimitCount++;
      } else {
        message = `HTTP ${statusCode}: ${error.message || 'Hata'}`;
        this.errorCount++;
      }

      this.addTestResult({
        requestNumber,
        timestamp,
        status,
        statusCode,
        message,
        responseTime
      });
    }

    this.currentRequestCount = requestNumber;
  }

  private async sendPostRequest(): Promise<any> {
    switch (this.selectedEndpoint) {
      case '/api/member/scannumber':
        return this.httpClient.post(`${this.apiUrl}member/scannumber`, {
          scanNumber: '123456789'
        }).toPromise();

      case '/api/auth/change-password':
        return this.httpClient.post(`${this.apiUrl}auth/change-password`, {
          currentPassword: 'test123',
          newPassword: 'test456'
        }).toPromise();

      case '/api/transactions/add':
        return this.httpClient.post(`${this.apiUrl}transactions/add`, {
          memberID: 1,
          productID: 1,
          quantity: 1,
          unitPrice: 10,
          amount: 10,
          transactionType: 'Test'
        }).toPromise();

      case '/api/products/add':
        return this.httpClient.post(`${this.apiUrl}products/add`, {
          productName: 'Test Product',
          price: 10,
          stock: 100,
          description: 'Rate limit test product'
        }).toPromise();

      case '/api/member/add':
        return this.httpClient.post(`${this.apiUrl}member/add`, {
          name: 'Test Member',
          surname: 'Rate Limit',
          phoneNumber: '5551234567',
          email: '<EMAIL>',
          membershipStartDate: new Date().toISOString(),
          membershipEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }).toPromise();

      default:
        throw new Error('Desteklenmeyen POST endpoint');
    }
  }

  private addTestResult(result: TestResult): void {
    this.testResults.unshift(result); // En yeni sonuçları üstte göster

    // Sadece son 50 sonucu göster (performans için)
    if (this.testResults.length > 50) {
      this.testResults = this.testResults.slice(0, 50);
    }
  }

  private calculateStatistics(): void {
    if (this.testResults.length === 0) return;

    const totalResponseTime = this.testResults
      .filter(r => r.responseTime)
      .reduce((sum, r) => sum + (r.responseTime || 0), 0);

    const responseTimeCount = this.testResults.filter(r => r.responseTime).length;
    this.averageResponseTime = responseTimeCount > 0 ? Math.round(totalResponseTime / responseTimeCount) : 0;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'success': return 'status-success';
      case 'rate-limited': return 'status-rate-limited';
      case 'error': return 'status-error';
      default: return '';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'success': return '✅';
      case 'rate-limited': return '🚫';
      case 'error': return '❌';
      default: return '❓';
    }
  }

  clearResults(): void {
    this.resetTestData();
    this.toastrService.info('Test sonuçları temizlendi.', 'Bilgi');
  }

  exportResults(): void {
    if (this.testResults.length === 0) {
      this.toastrService.warning('Dışa aktarılacak sonuç yok.', 'Uyarı');
      return;
    }

    // Rate limit kontrolü
    this.rateLimitService.getRemainingFileDownloads().subscribe({
      next: (rateLimitResponse) => {
        if (rateLimitResponse.success && rateLimitResponse.remainingDownloads <= 0) {
          this.toastrService.error('Dosya indirme limitini aştınız. 10 dakikada en fazla 5 dosya indirebilirsiniz.', 'Limit Aşıldı');
          return;
        }

        this.performCSVExport();
      },
      error: (error) => {
        console.error('Rate limit kontrolü başarısız:', error);
        this.performCSVExport(); // Rate limit kontrolü başarısız olursa yine de devam et
      }
    });
  }

  private performCSVExport(): void {
    const csvContent = this.generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `rate-limit-test-${new Date().toISOString().slice(0, 10)}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Başarılı indirme kaydı
      this.rateLimitService.recordFileDownload().subscribe({
        next: (recordResponse) => {
          this.toastrService.success('Test sonuçları CSV olarak indirildi.', 'Başarılı');
        },
        error: (recordError) => {
          console.error('File download record error:', recordError);
          this.toastrService.success('Test sonuçları CSV olarak indirildi.', 'Başarılı');
        }
      });
    }
  }

  private generateCSV(): string {
    const headers = ['İstek No', 'Zaman', 'Durum', 'Status Code', 'Mesaj', 'Yanıt Süresi (ms)'];
    const rows = this.testResults.map(result => [
      result.requestNumber,
      result.timestamp,
      result.status,
      result.statusCode || '',
      result.message || '',
      result.responseTime || ''
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  }

  // Test endpoint'i değiştiğinde çağrılır
  onEndpointChange(): void {
    if (this.isTestRunning) {
      this.stopTest();
      this.toastrService.info('Test durduruldu. Yeni endpoint seçildi.', 'Bilgi');
    }
    this.clearResults();
  }
}
