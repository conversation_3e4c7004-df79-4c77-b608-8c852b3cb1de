/* Member Workout Assign Mo<PERSON> Styles */

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.modal-icon {
  color: var(--primary);
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-speed) ease;
}

.modal-close-btn:hover:not(:disabled) {
  color: var(--danger);
  background-color: var(--danger-light);
}

.modal-close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Body */
.modal-body {
  padding: var(--spacing-lg);
  max-height: 70vh;
  overflow-y: auto;
}

/* Form Styles */
.form-icon {
  margin-right: var(--spacing-xs);
  color: var(--primary);
  width: 16px;
}



.required::after {
  content: ' *';
  color: var(--danger);
}

.is-invalid {
  border-color: var(--danger) !important;
  box-shadow: 0 0 0 0.2rem var(--danger-light) !important;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--danger);
}

.form-text {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--text-muted);
}



/* Checkbox Styles */
.form-check-container {
  margin-top: var(--spacing-xs);
}

.form-check-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-primary);
  position: relative;
  padding-left: 2rem;
}

.form-check-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  width: 20px;
  background-color: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-speed) ease;
}

.form-check-input:checked ~ .checkmark {
  background-color: var(--primary);
  border-color: var(--primary);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-check-input:checked ~ .checkmark:after {
  display: block;
}

/* Selected Info Preview */
.selected-info-preview {
  margin-top: var(--spacing-lg);
}

.preview-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
}

.preview-card h6 {
  margin: 0 0 var(--spacing-sm) 0;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.preview-item {
  margin-bottom: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.preview-item strong {
  color: var(--text-primary);
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.modal-footer .modern-btn {
  min-width: 100px;
}

/* Loading State */
.modal-footer .modern-btn .fa-spinner {
  margin-right: var(--spacing-xs);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-header {
    padding: var(--spacing-md);
  }

  .modal-body {
    padding: var(--spacing-md);
    max-height: 60vh;
  }

  .modal-footer {
    padding: var(--spacing-md);
    flex-direction: column;
  }

  .modal-footer .modern-btn {
    width: 100%;
  }



  .modal-title {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: var(--spacing-sm);
  }

  .modal-body {
    padding: var(--spacing-sm);
  }

  .modal-footer {
    padding: var(--spacing-sm);
  }

  .modal-title {
    font-size: 1rem;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .modal-header {
  background-color: var(--bg-secondary);
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modal-title {
  color: var(--text-primary);
}

[data-theme="dark"] .modal-close-btn {
  color: var(--text-secondary);
}

[data-theme="dark"] .modal-close-btn:hover:not(:disabled) {
  color: var(--danger);
  background-color: var(--danger-light);
}

[data-theme="dark"] .form-check-label {
  color: var(--text-primary);
}

[data-theme="dark"] .checkmark {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .preview-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .preview-card h6 {
  color: var(--text-primary);
}

[data-theme="dark"] .preview-item {
  color: var(--text-secondary);
}

[data-theme="dark"] .preview-item strong {
  color: var(--text-primary);
}

[data-theme="dark"] .modal-footer {
  background-color: var(--bg-secondary);
  border-top-color: var(--border-color);
}


