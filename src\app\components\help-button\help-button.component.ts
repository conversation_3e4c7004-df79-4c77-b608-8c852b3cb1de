// src/app/components/help-button/help-button.component.ts
import { Component, Input } from '@angular/core';
import { HelpGuideService } from '../../services/help-guide.service';

@Component({
  selector: 'app-help-button',
  standalone: false,
  templateUrl: './help-button.component.html',
  styleUrls: ['./help-button.component.css']
})
export class HelpButtonComponent {
  @Input() guideId!: string;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'inline' = 'top-right';
  @Input() tooltip: string = 'Bu panel hakkında yardım al';

  constructor(private helpGuideService: HelpGuideService) {}

  openHelp(): void {
    if (!this.guideId) {
      console.warn('Help button: guideId is required');
      return;
    }

    this.helpGuideService.openHelpDialog(this.guideId).subscribe();
  }

  getSizeClass(): string {
    return `help-btn-${this.size}`;
  }

  getPositionClass(): string {
    return `help-btn-${this.position}`;
  }
}
