// Workout Program Models
export interface WorkoutProgramTemplate {
  workoutProgramTemplateID: number;
  companyID: number;
  programName: string;
  description?: string;
  experienceLevel?: string;
  targetGoal?: string;
  isActive?: boolean;
  creationDate?: Date;
  dayCount: number;
  days?: WorkoutProgramDay[];
}

export interface WorkoutProgramTemplateList {
  workoutProgramTemplateID: number;
  programName: string;
  description?: string;
  experienceLevel?: string;
  targetGoal?: string;
  isActive?: boolean;
  creationDate?: Date;
  dayCount: number;
  exerciseCount: number;
}

export interface WorkoutProgramTemplateAdd {
  programName: string;
  description?: string;
  experienceLevel?: string;
  targetGoal?: string;
  days: WorkoutProgramDayAdd[];
}

export interface WorkoutProgramTemplateUpdate {
  workoutProgramTemplateID: number;
  programName: string;
  description?: string;
  experienceLevel?: string;
  targetGoal?: string;
  isActive: boolean;
  days: WorkoutProgramDayUpdate[];
}

export interface WorkoutProgramDay {
  workoutProgramDayID: number;
  workoutProgramTemplateID: number;
  dayNumber: number;
  dayName: string;
  isRestDay: boolean;
  creationDate?: Date;
  exercises?: WorkoutProgramExercise[];
}

export interface WorkoutProgramDayAdd {
  dayNumber: number;
  dayName: string;
  isRestDay: boolean;
  exercises: WorkoutProgramExerciseAdd[];
}

export interface WorkoutProgramDayUpdate {
  workoutProgramDayID?: number; // Null ise yeni gün
  dayNumber: number;
  dayName: string;
  isRestDay: boolean;
  exercises: WorkoutProgramExerciseUpdate[];
}

export interface WorkoutProgramExercise {
  workoutProgramExerciseID: number;
  workoutProgramDayID: number;
  exerciseType: string; // "System" veya "Company"
  exerciseID: number;
  exerciseName: string;
  exerciseDescription?: string;
  categoryName?: string;
  orderIndex: number;
  sets: number;
  reps: string;
  restTime?: number;
  notes?: string;
  creationDate?: Date;
}

export interface WorkoutProgramExerciseAdd {
  exerciseType: string;
  exerciseID: number;
  orderIndex: number;
  sets: number;
  reps: string;
  notes?: string;
}

export interface WorkoutProgramExerciseUpdate {
  workoutProgramExerciseID?: number; // Null ise yeni egzersiz
  exerciseType: string;
  exerciseID: number;
  orderIndex: number;
  sets: number;
  reps: string;
  notes?: string;
}

// Popüler gün adları
export const POPULAR_DAY_NAMES = [
  'Göğüs-Triceps',
  'Sırt-Biceps',
  'Bacak-Omuz',
  'Push Day',
  'Pull Day',
  'Leg Day',
  'Upper Body',
  'Lower Body',
  'Full Body',
  'Cardio',
  'Dinlenme Günü'
];

// Deneyim seviyeleri
export const EXPERIENCE_LEVELS = [
  { value: 'Başlangıç', label: 'Başlangıç' },
  { value: 'Orta', label: 'Orta' },
  { value: 'İleri', label: 'İleri' }
];

// Hedef türleri
export const TARGET_GOALS = [
  { value: 'Kilo Alma', label: 'Kilo Alma' },
  { value: 'Kilo Verme', label: 'Kilo Verme' },
  { value: 'Kas Yapma', label: 'Kas Yapma' },
  { value: 'Kondisyon', label: 'Kondisyon' },
  { value: 'Güç', label: 'Güç' }
];
