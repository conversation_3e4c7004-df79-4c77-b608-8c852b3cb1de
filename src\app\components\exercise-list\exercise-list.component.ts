import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { faEdit, faTrashAlt, faInfoCircle, faSyncAlt, faPlus, faSearch, faFilter, faDumbbell } from '@fortawesome/free-solid-svg-icons';

import { ExerciseCategoryService, ExerciseCategory } from '../../services/exercise-category.service';
import { SystemExerciseService, SystemExercise, SystemExerciseFilter } from '../../services/system-exercise.service';
import { CompanyExerciseService, CompanyExercise, CombinedExercise, CompanyExerciseFilter } from '../../services/company-exercise.service';
import { AuthService } from '../../services/auth.service';
import { DialogService } from '../../services/dialog.service';
import { ExerciseAddModalComponent } from '../exercise-add-modal/exercise-add-modal.component';

@Component({
  selector: 'app-exercise-list',
  templateUrl: './exercise-list.component.html',
  styleUrls: ['./exercise-list.component.css'],
  standalone: false
})
export class ExerciseListComponent implements OnInit, OnDestroy {
  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faInfoCircle = faInfoCircle;
  faSyncAlt = faSyncAlt;
  faPlus = faPlus;
  faSearch = faSearch;
  faFilter = faFilter;
  faDumbbell = faDumbbell;

  // Data
  categories: ExerciseCategory[] = [];
  exercises: CombinedExercise[] = [];
  
  // Pagination
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  pageSize = 20;

  // Filters
  selectedCategoryId: number | string | null = null;
  selectedDifficultyLevel: number | string | null = null;
  selectedEquipment: string = '';
  searchText = '';
  
  // UI State
  isLoading = false;
  viewMode: 'combined' | 'system' | 'company' = 'combined';
  
  // User permissions
  isOwner = false;
  isAdmin = false;

  private searchSubject = new Subject<string>();
  private equipmentSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private exerciseCategoryService: ExerciseCategoryService,
    private systemExerciseService: SystemExerciseService,
    private companyExerciseService: CompanyExerciseService,
    private authService: AuthService,
    private toastrService: ToastrService,
    public dialog: MatDialog,
    private dialogService: DialogService
  ) {
    this.searchSubject.pipe(
      debounceTime(750),
      takeUntil(this.destroy$)
    ).subscribe(searchValue => {
      this.searchText = searchValue;
      this.currentPage = 1;
      this.loadExercises();
    });

    this.equipmentSubject.pipe(
      debounceTime(750),
      takeUntil(this.destroy$)
    ).subscribe(equipmentValue => {
      this.selectedEquipment = equipmentValue;
      this.currentPage = 1;
      this.loadExercises();
    });
  }

  ngOnInit(): void {
    this.checkUserPermissions();
    this.loadCategories();
    this.loadExercises();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  checkUserPermissions(): void {
    this.isOwner = this.authService.hasRole('owner');
    this.isAdmin = this.authService.hasRole('admin');
  }

  loadCategories(): void {
    this.exerciseCategoryService.getActiveCategories().subscribe({
      next: (response) => {
        if (response.success) {
          // Kategorileri istenen sıraya göre sırala: Göğüs, Sırt, Kol, Omuz, Bacak, Karın, Cardio, Fonksiyonel
          const categoryOrder = ['Göğüs', 'Sırt', 'Kol', 'Omuz', 'Bacak', 'Karın', 'Cardio', 'Functional'];
          this.categories = response.data.sort((a, b) => {
            const indexA = categoryOrder.indexOf(a.categoryName);
            const indexB = categoryOrder.indexOf(b.categoryName);

            // Eğer kategori listede yoksa sona koy
            if (indexA === -1) return 1;
            if (indexB === -1) return -1;

            return indexA - indexB;
          });
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.toastrService.error('Kategoriler yüklenirken hata oluştu', 'Hata');
      }
    });
  }

  loadExercises(): void {
    this.isLoading = true;

    if (this.viewMode === 'combined') {
      this.loadCombinedExercises();
    } else if (this.viewMode === 'system') {
      this.loadSystemExercises();
    } else {
      this.loadCompanyExercises();
    }
  }

  loadCombinedExercises(): void {
    const filter: SystemExerciseFilter = {
      exerciseCategoryID: (this.selectedCategoryId && this.selectedCategoryId !== '') ? Number(this.selectedCategoryId) : undefined,
      searchTerm: this.searchText || undefined,
      difficultyLevel: (this.selectedDifficultyLevel && this.selectedDifficultyLevel !== '') ? Number(this.selectedDifficultyLevel) : undefined,
      equipment: this.selectedEquipment || undefined,
      page: this.currentPage,
      pageSize: this.pageSize
    };

    this.companyExerciseService.getCombinedFiltered(filter).subscribe({
      next: (response) => {
        if (response.success) {
          this.exercises = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading combined exercises:', error);
        this.toastrService.error('Egzersizler yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  loadSystemExercises(): void {
    const filter: SystemExerciseFilter = {
      exerciseCategoryID: (this.selectedCategoryId && this.selectedCategoryId !== '') ? Number(this.selectedCategoryId) : undefined,
      searchTerm: this.searchText || undefined,
      difficultyLevel: (this.selectedDifficultyLevel && this.selectedDifficultyLevel !== '') ? Number(this.selectedDifficultyLevel) : undefined,
      equipment: this.selectedEquipment || undefined,
      page: this.currentPage,
      pageSize: this.pageSize
    };

    this.systemExerciseService.getFiltered(filter).subscribe({
      next: (response) => {
        if (response.success) {
          // SystemExercise'i CombinedExercise formatına çevir
          this.exercises = response.data.data.map(exercise => ({
            exerciseID: exercise.systemExerciseID,
            exerciseType: 'System',
            exerciseCategoryID: exercise.exerciseCategoryID,
            categoryName: exercise.categoryName,
            exerciseName: exercise.exerciseName,
            description: exercise.description,
            instructions: exercise.instructions,
            muscleGroups: exercise.muscleGroups,
            equipment: exercise.equipment,
            difficultyLevel: exercise.difficultyLevel,
            difficultyLevelText: exercise.difficultyLevelText,
            isActive: exercise.isActive,
            creationDate: exercise.creationDate
          }));
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading system exercises:', error);
        this.toastrService.error('Sistem egzersizleri yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  loadCompanyExercises(): void {
    const filter: CompanyExerciseFilter = {
      exerciseCategoryID: (this.selectedCategoryId && this.selectedCategoryId !== '') ? Number(this.selectedCategoryId) : undefined,
      searchTerm: this.searchText || undefined,
      difficultyLevel: (this.selectedDifficultyLevel && this.selectedDifficultyLevel !== '') ? Number(this.selectedDifficultyLevel) : undefined,
      equipment: this.selectedEquipment || undefined,
      page: this.currentPage,
      pageSize: this.pageSize
    };

    this.companyExerciseService.getFiltered(filter).subscribe({
      next: (response) => {
        if (response.success) {
          // CompanyExercise'i CombinedExercise formatına çevir
          this.exercises = response.data.data.map(exercise => ({
            exerciseID: exercise.companyExerciseID,
            exerciseType: 'Company',
            exerciseCategoryID: exercise.exerciseCategoryID,
            categoryName: exercise.categoryName,
            exerciseName: exercise.exerciseName,
            description: exercise.description,
            instructions: exercise.instructions,
            muscleGroups: exercise.muscleGroups,
            equipment: exercise.equipment,
            difficultyLevel: exercise.difficultyLevel,
            difficultyLevelText: exercise.difficultyLevelText,
            isActive: exercise.isActive,
            creationDate: exercise.creationDate
          }));
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading company exercises:', error);
        this.toastrService.error('Salon egzersizleri yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearch(event: any): void {
    const searchValue = event.target.value;
    this.searchSubject.next(searchValue);
  }

  onCategoryChange(): void {
    this.currentPage = 1;
    this.loadExercises();
  }

  onDifficultyChange(): void {
    this.currentPage = 1;
    this.loadExercises();
  }

  onEquipmentChange(event: any): void {
    const equipmentValue = event.target.value;
    this.equipmentSubject.next(equipmentValue);
  }

  onViewModeChange(): void {
    this.currentPage = 1;
    this.loadExercises();
  }

  hasActiveFilters(): boolean {
    return !!(this.searchText ||
              (this.selectedCategoryId && this.selectedCategoryId !== '') ||
              (this.selectedDifficultyLevel && this.selectedDifficultyLevel !== '') ||
              this.selectedEquipment);
  }

  clearFilters(): void {
    this.selectedCategoryId = null;
    this.selectedDifficultyLevel = null;
    this.selectedEquipment = '';
    this.searchText = '';
    this.currentPage = 1;
    this.loadExercises();
  }

  openAddExerciseModal(): void {
    const dialogRef = this.dialog.open(ExerciseAddModalComponent, {
      width: '800px',
      maxHeight: '90vh',
      data: { categories: this.categories, mode: 'add' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadExercises();
      }
    });
  }

  openEditExerciseModal(exercise: CombinedExercise): void {
    if (exercise.exerciseType !== 'Company') {
      this.toastrService.warning('Sadece salon egzersizleri düzenlenebilir', 'Uyarı');
      return;
    }

    const dialogRef = this.dialog.open(ExerciseAddModalComponent, {
      width: '800px',
      maxHeight: '90vh',
      data: {
        categories: this.categories,
        mode: 'edit',
        exercise: exercise
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadExercises();
      }
    });
  }

  deleteExercise(exercise: CombinedExercise): void {
    if (exercise.exerciseType !== 'Company') {
      this.toastrService.warning('Sadece salon egzersizleri silinebilir', 'Uyarı');
      return;
    }

    this.dialogService.confirmExerciseDelete(exercise.exerciseName, exercise).subscribe(result => {
      if (result) {
        this.companyExerciseService.delete(exercise.exerciseID).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Egzersiz başarıyla silindi', 'Başarılı');
              this.loadExercises();
            } else {
              this.toastrService.error(response.message || 'Egzersiz silinirken hata oluştu', 'Hata');
            }
          },
          error: (error) => {
            console.error('Error deleting exercise:', error);
            this.toastrService.error('Egzersiz silinirken hata oluştu', 'Hata');
          }
        });
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadExercises();
    }
  }

  getPaginationRange(): number[] {
    const range = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }

  getDifficultyBadgeClass(level?: number): string {
    switch (level) {
      case 1: return 'badge-success';
      case 2: return 'badge-warning';
      case 3: return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  getExerciseTypeBadgeClass(type: string): string {
    return type === 'System' ? 'badge-primary' : 'badge-info';
  }
}
