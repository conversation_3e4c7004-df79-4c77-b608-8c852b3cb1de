import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';

// DTO Models
export interface MemberWorkoutProgramAdd {
  memberID: number;
  workoutProgramTemplateID: number;
  startDate: Date;
  endDate?: Date;
  notes?: string;
}

export interface MemberWorkoutProgramUpdate {
  memberWorkoutProgramID: number;
  workoutProgramTemplateID: number;
  startDate: Date;
  endDate?: Date;
  notes?: string;
  isActive: boolean;
}

export interface MemberWorkoutProgramList {
  memberWorkoutProgramID: number;
  memberID: number;
  memberName: string;
  programName: string;
  experienceLevel?: string;
  targetGoal?: string;
  startDate: Date;
  endDate?: Date;
  isActive: boolean;
  dayCount: number;
  exerciseCount: number;
}

export interface MemberWorkoutProgramDetail {
  memberWorkoutProgramID: number;
  memberID: number;
  memberName: string;
  memberPhone?: string;
  workoutProgramTemplateID: number;
  programName: string;
  programDescription?: string;
  experienceLevel?: string;
  targetGoal?: string;
  companyID: number;
  startDate: Date;
  endDate?: Date;
  notes?: string;
  isActive: boolean;
  creationDate?: Date;
  dayCount: number;
  exerciseCount: number;
}

export interface MemberWorkoutProgramHistory {
  memberWorkoutProgramID: number;
  programName: string;
  startDate: Date;
  endDate?: Date;
  isActive: boolean;
  notes?: string;
  creationDate?: Date;
}

export interface MemberActiveWorkoutProgram {
  memberWorkoutProgramID: number;
  workoutProgramTemplateID: number;
  programName: string;
  programDescription?: string;
  experienceLevel?: string;
  targetGoal?: string;
  startDate: Date;
  endDate?: Date;
  notes?: string;
  dayCount: number;
  exerciseCount: number;
}

@Injectable({
  providedIn: 'root'
})
export class MemberWorkoutProgramService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Üyeye program atar
   */
  assignProgram(assignment: MemberWorkoutProgramAdd): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}memberworkoutprogram/assign`,
      assignment
    );
  }

  /**
   * Program atamasını günceller
   */
  updateAssignment(assignment: MemberWorkoutProgramUpdate): Observable<ResponseModel> {
    return this.httpClient.put<ResponseModel>(
      `${this.apiUrl}memberworkoutprogram/update`,
      assignment
    );
  }

  /**
   * Program atamasını siler
   */
  deleteAssignment(assignmentId: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}memberworkoutprogram/delete?id=${assignmentId}`
    );
  }

  /**
   * Şirket bazlı tüm program atamalarını getirir
   */
  getCompanyAssignments(): Observable<ListResponseModel<MemberWorkoutProgramList>> {
    return this.httpClient.get<ListResponseModel<MemberWorkoutProgramList>>(
      `${this.apiUrl}memberworkoutprogram/getcompanyassignments`
    );
  }

  /**
   * Belirli üyenin aktif programlarını getirir
   */
  getMemberActivePrograms(memberId: number): Observable<ListResponseModel<MemberWorkoutProgramDetail>> {
    return this.httpClient.get<ListResponseModel<MemberWorkoutProgramDetail>>(
      `${this.apiUrl}memberworkoutprogram/getmemberactiveprograms?memberId=${memberId}`
    );
  }

  /**
   * Belirli üyenin program geçmişini getirir
   */
  getMemberProgramHistory(memberId: number): Observable<ListResponseModel<MemberWorkoutProgramHistory>> {
    return this.httpClient.get<ListResponseModel<MemberWorkoutProgramHistory>>(
      `${this.apiUrl}memberworkoutprogram/getmemberprogramhistory?memberId=${memberId}`
    );
  }

  /**
   * User ID'ye göre aktif programları getirir (mobil API için)
   */
  getActiveWorkoutProgramsByUserId(userId: number): Observable<ListResponseModel<MemberActiveWorkoutProgram>> {
    return this.httpClient.get<ListResponseModel<MemberActiveWorkoutProgram>>(
      `${this.apiUrl}memberworkoutprogram/getactiveprogramsbyuser?userId=${userId}`
    );
  }

  /**
   * Program atama detayını getirir
   */
  getAssignmentDetail(assignmentId: number): Observable<SingleResponseModel<MemberWorkoutProgramDetail>> {
    return this.httpClient.get<SingleResponseModel<MemberWorkoutProgramDetail>>(
      `${this.apiUrl}memberworkoutprogram/getassignmentdetail?id=${assignmentId}`
    );
  }

  /**
   * Belirli programa atanan üye sayısını getirir
   */
  getAssignedMemberCount(workoutProgramTemplateId: number): Observable<SingleResponseModel<number>> {
    return this.httpClient.get<SingleResponseModel<number>>(
      `${this.apiUrl}memberworkoutprogram/getassignedmembercount?workoutProgramTemplateId=${workoutProgramTemplateId}`
    );
  }

  /**
   * Şirket bazlı aktif atama sayısını getirir
   */
  getActiveAssignmentCount(): Observable<SingleResponseModel<number>> {
    return this.httpClient.get<SingleResponseModel<number>>(
      `${this.apiUrl}memberworkoutprogram/getactiveassignmentcount`
    );
  }
}
