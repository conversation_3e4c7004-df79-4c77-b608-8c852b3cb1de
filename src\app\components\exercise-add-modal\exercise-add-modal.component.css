.modal-header {
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  color: var(--text-color);
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: var(--primary-color);
}

.btn-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background-color: var(--hover-bg);
  color: var(--text-color);
}

.btn-close:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modal-body {
  background: var(--card-bg);
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Form Styles */
.form-label {
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 6px;
}

.form-label.required::after {
  content: ' *';
  color: #dc3545;
}

.form-control,
.form-select {
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
  background-color: var(--input-bg);
  color: var(--text-color);
}

.form-control::placeholder {
  color: var(--text-muted);
  opacity: 0.8;
}

.form-select option {
  background-color: var(--input-bg);
  color: var(--text-color);
}

/* Validation Styles */
.is-invalid {
  border-color: #dc3545;
}

.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 4px;
  font-size: 0.875rem;
  color: #dc3545;
}

.form-text {
  margin-top: 4px;
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background-color: var(--primary-color);
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--secondary-hover);
  transform: translateY(-1px);
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

/* Responsive */
@media (max-width: 768px) {
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .modal-body {
    max-height: 60vh;
  }

  .modal-footer {
    flex-direction: column-reverse;
  }

  .modal-footer .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .form-control:focus,
  .form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.4);
  }
  
  .is-invalid:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.4);
  }
}

/* Custom scrollbar for modal body */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--bg-color);
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
