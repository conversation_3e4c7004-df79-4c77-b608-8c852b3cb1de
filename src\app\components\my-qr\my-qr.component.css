/* QR Code Component Styles - Using project's design system */

.gym-container {
  max-width: 100%;
  margin: auto;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Arial', sans-serif;
  transition: all var(--transition-speed) var(--transition-timing);
}

.gym-panel {
  width: 100%;
  max-width: 380px;
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-speed) var(--transition-timing);
  animation: zoomIn 0.5s var(--transition-timing);
  position: relative;
  overflow: hidden;
}

.gym-panel::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: var(--primary);
  opacity: 0.1;
  border-radius: 50%;
  z-index: 0;
}

.gym-panel::after {
  content: '';
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: var(--primary-dark);
  opacity: 0.1;
  border-radius: 50%;
  z-index: 0;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.gym-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
  margin: 0 auto;
}

.message-area {
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: 14px;
  text-align: center;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.3s var(--transition-timing);
}

.message-area.error {
  background-color: var(--danger-light);
  color: var(--danger);
}

.error-help {
  margin-top: 10px;
  font-size: 12px;
  color: var(--text-secondary);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.result-area {
  text-align: center;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.5s var(--transition-timing);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-secondary);
  box-shadow: var(--shadow-sm);
}

.result-area h2 {
  margin: 0 0 var(--spacing-md);
  font-size: 22px;
  color: var(--text-primary);
  font-weight: 600;
}

.membership-info {
  margin-bottom: var(--spacing-md);
}

.remaining-days {
  color: var(--success);
  font-weight: bold;
  margin: 8px 0;
  padding: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--success-light);
}

.future-membership {
  color: var(--warning);
  font-weight: bold;
  margin: 8px 0;
  padding: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--warning-light);
}

.expired-membership, .expired-membership-special {
  color: var(--danger);
  font-weight: bold;
  margin: 8px 0;
  padding: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--danger-light);
}

.frozen-membership {
  color: var(--primary);
  font-weight: bold;
  margin: 8px 0;
  padding: 8px;
  border-radius: var(--border-radius-md);
  background-color: var(--primary-light);
}

.no-qr-message {
  color: var(--danger);
  font-weight: bold;
  margin: 15px 0;
  padding: 12px;
  border-radius: var(--border-radius-md);
  background-color: var(--danger-light);
  text-align: center;
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: var(--spacing-md) 0;
  position: relative;
  width: 100%;
  overflow: hidden;
}

.qr-validity-timer {
  width: 100%;
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.timer-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--text-secondary);
}

.timer-countdown {
  font-weight: bold;
  color: var(--success);
}

.timer-warning {
  color: var(--danger);
  animation: blink 1s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.progress-bar-container {
  width: 100%;
  height: 6px;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-pill);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--success) 0%, var(--primary) 100%);
  border-radius: var(--border-radius-pill);
  transition: width 1s linear;
}

.progress-warning {
  background: linear-gradient(90deg, var(--danger) 0%, var(--warning) 100%);
}

.qr-code-container qrcode {
  border: 2px solid var(--primary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  background-color: white;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-speed) var(--transition-timing);
  max-width: 100%;
  margin-bottom: var(--spacing-md);
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code-container qrcode canvas {
  max-width: 100%;
  height: auto !important;
  display: block;
  image-rendering: -webkit-optimize-contrast; /* Improves QR code clarity on some browsers */
  image-rendering: crisp-edges; /* Modern browsers */
}

/* QR kod hover animasyonu kaldırıldı - kullanıcı isteği üzerine */

/* QR Kodunu İndir butonu kaldırıldı (QR kodları 5 dakika geçerli olduğu için)
.qr-actions {
  display: flex;
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.download-button {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, var(--success) 0%, #219150 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius-pill);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.download-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.download-button:active {
  transform: translateY(1px);
}
*/

.qr-info-text {
  width: 100%;
  text-align: center;
  font-size: 13px;
  color: var(--text-secondary);
  padding: 8px;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px dashed var(--border-color);
}

/* Special member styles - Yeni Tasarım */
.special-member {
  background: linear-gradient(135deg, #ff0080, #ff5db1, #ff0080);
  background-size: 200% 200%;
  animation: gradientBG 15s ease infinite;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 0 30px rgba(255, 0, 128, 0.6);
  border: 3px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

/* Arka plan animasyonu */
@keyframes gradientBG {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Arka plan süslemeleri */
.special-member::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  z-index: 0;
}

.special-member::after {
  content: '';
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  z-index: 0;
}

.special-expired-text {
  background-color: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.6);
  font-weight: bold;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  padding: 15px;
  margin: 15px 0;
  font-size: 18px;
  letter-spacing: 1px;
  animation: glow 2s infinite alternate;
  border-radius: 12px;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5), 0 0 10px rgba(255, 0, 128, 0.3);
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.5);
  }
  to {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.7), 0 0 20px rgba(255, 0, 128, 0.5);
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.7);
  }
}

.special-header {
  text-align: center;
  margin-bottom: var(--spacing-md);
  position: relative;
  z-index: 1;
}

.special-header h2 {
  color: #ffffff !important;
  font-size: 32px !important;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  margin: 10px 0 !important;
  font-weight: 700;
  letter-spacing: 1px;
  background: rgba(255, 255, 255, 0.2);
  padding: 15px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.special-header h2::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  20% { left: 100%; }
  100% { left: 100%; }
}

.hearts {
  font-size: 30px;
  margin: 10px 0;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.hearts span {
  animation: heartbeat 1.5s infinite;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.7));
  display: inline-block;
  transform-origin: center;
}

.hearts span:nth-child(1) {
  animation-delay: 0s;
}

.hearts span:nth-child(2) {
  animation-delay: 0.5s;
}

.hearts span:nth-child(3) {
  animation-delay: 1s;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  15% {
    transform: scale(1.3);
  }
  30% {
    transform: scale(1);
  }
  45% {
    transform: scale(1.3);
  }
  60% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}

.special-info {
  color: #ffffff !important;
  font-size: 18px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

/* Üyelik bilgisi için özel stil */
.special-member .remaining-days {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.4);
  font-weight: bold;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  padding: 12px;
  margin: 10px 0;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.special-member .remaining-days:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}

.special-member .remaining-days strong {
  color: #ffffff;
  font-size: 20px;
  margin: 0 5px;
}

.special-member .qr-code-container {
  position: relative;
  z-index: 1;
  margin: 20px 0;
}

.special-member .qr-code-container qrcode {
  border: 5px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  padding: 20px;
  transform: scale(1.05);
  transition: all 0.3s ease;
  background-color: white;
  position: relative;
}

.special-member .qr-code-container qrcode::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #ff0080, #ff5db1, #ff0080);
  background-size: 200% 200%;
  animation: gradientBG 5s ease infinite;
  z-index: -1;
  border-radius: 23px;
}

/* Özel üye QR kod hover animasyonu kaldırıldı - kullanıcı isteği üzerine */

/* Özel üye için QR kod geçerlilik süresi göstergesi */
.special-member .qr-validity-timer {
  margin: 15px 0;
}

.special-member .timer-label {
  color: #ffffff;
  font-weight: 500;
  font-size: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin-bottom: 8px;
}

.special-member .timer-countdown {
  color: #ffffff;
  font-weight: 700;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 3px 8px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.special-member .timer-warning {
  color: #ffffff;
  background-color: rgba(255, 100, 100, 0.4);
  animation: blinkWarning 1s infinite;
}

@keyframes blinkWarning {
  0% { background-color: rgba(255, 100, 100, 0.4); }
  50% { background-color: rgba(255, 50, 50, 0.6); }
  100% { background-color: rgba(255, 100, 100, 0.4); }
}

.special-member .progress-bar-container {
  height: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.special-member .progress-bar {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  height: 100%;
  border-radius: 10px;
}

.special-member .progress-warning {
  background: linear-gradient(90deg, rgba(255, 100, 100, 0.8), rgba(255, 50, 50, 0.6));
}

/* Özel üye için bilgi metni */
.special-member .qr-info-text {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.4);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  font-size: 14px;
  padding: 10px;
  border-radius: 10px;
  margin-top: 15px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

/* QR Kodunu İndir butonu kaldırıldı (QR kodları 5 dakika geçerli olduğu için)
.special-member .download-button {
  background: linear-gradient(135deg, #00c9ff, #92fe9d);
  font-weight: bold;
  color: #333;
  font-size: 18px;
  padding: 16px 24px;
  border: 2px solid #ffffff;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
  letter-spacing: 1px;
}

.special-member .download-button:hover {
  background: linear-gradient(135deg, #92fe9d, #00c9ff);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
*/

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Styles */
@media (max-width: 767px) {
  .gym-container {
    padding: var(--spacing-md);
  }

  .gym-panel {
    max-width: 100%;
    padding: var(--spacing-lg);
  }

  .gym-icon {
    width: 60px;
    height: 60px;
    font-size: 28px;
  }

  .result-area h2 {
    font-size: 20px;
  }

  .qr-code-container qrcode {
    max-width: 100%;
    height: auto;
  }
}

@media (max-width: 480px) {
  .gym-panel {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin: 10px;
    width: calc(100% - 20px);
  }

  .gym-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .result-area {
    padding: var(--spacing-sm);
  }

  .result-area h2 {
    font-size: 18px;
  }

  .remaining-days,
  .future-membership,
  .expired-membership,
  .expired-membership-special,
  .frozen-membership {
    font-size: 14px;
    padding: 6px;
    margin: 6px 0;
  }

  /* QR Kodunu İndir butonu kaldırıldı
  .download-button {
    font-size: 14px;
    padding: 12px 16px;
  }
  */

  .special-header h2 {
    font-size: 20px !important;
    padding: 10px !important;
  }

  .hearts {
    font-size: 20px;
    margin: 8px 0;
    gap: 10px;
  }

  .special-info {
    font-size: 16px;
  }

  .special-expired-text {
    padding: 10px;
    margin: 10px 0;
    font-size: 16px;
  }

  .special-member .qr-code-container qrcode {
    padding: 12px;
    border-width: 3px;
  }

  .special-member .timer-label {
    font-size: 13px;
  }

  .special-member .timer-countdown {
    padding: 2px 6px;
  }

  .special-member .progress-bar-container {
    height: 6px;
  }

  .special-member .qr-info-text {
    font-size: 12px;
    padding: 8px;
  }

  .special-member .remaining-days {
    padding: 10px;
    font-size: 14px;
  }

  .special-member .remaining-days strong {
    font-size: 16px;
  }

  /* QR Kodunu İndir butonu kaldırıldı
  .special-member .download-button {
    font-size: 16px;
    padding: 12px 18px;
  }
  */
}

@media (max-height: 700px) {
  .gym-panel {
    max-height: 90vh;
    overflow-y: auto;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .gym-panel {
    padding: 12px;
    margin: 5px;
    width: calc(100% - 10px);
  }

  .gym-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .result-area h2 {
    font-size: 16px;
  }

  .special-header h2 {
    font-size: 18px !important;
    padding: 8px !important;
  }

  .hearts {
    font-size: 18px;
    margin: 6px 0;
    gap: 8px;
  }

  .special-member .timer-label {
    font-size: 12px;
  }

  .special-member .timer-countdown {
    padding: 2px 4px;
    font-size: 12px;
  }

  .special-member .progress-bar-container {
    height: 5px;
  }

  .special-member .qr-info-text {
    font-size: 11px;
    padding: 6px;
  }

  .special-member .remaining-days {
    padding: 8px;
    font-size: 12px;
  }

  .special-member .remaining-days strong {
    font-size: 14px;
  }

  .remaining-days,
  .future-membership,
  .expired-membership,
  .expired-membership-special,
  .frozen-membership,
  .special-expired-text {
    font-size: 12px;
    padding: 5px;
    margin: 5px 0;
  }

  /* QR Kodunu İndir butonu kaldırıldı
  .download-button,
  .special-member .download-button {
    font-size: 12px;
    padding: 10px 14px;
  }
  */

  .special-member .qr-code-container qrcode {
    padding: 8px;
    border-width: 2px;
  }
}

/* Landscape mode for mobile devices */
@media (max-width: 767px) and (orientation: landscape) {
  .gym-container {
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: auto;
  }

  .gym-panel {
    max-width: 90%;
    padding: 15px;
    margin: 0;
  }

  .result-area {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }

  .special-header,
  .result-area h2 {
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
  }

  .membership-info {
    width: 48%;
    margin-right: 2%;
  }

  .qr-code-container {
    width: 48%;
    margin: 0;
  }

  /* QR Kodunu İndir butonu kaldırıldı
  .download-button {
    width: 100%;
    margin-top: 10px;
  }
  */

  .special-member {
    padding: 15px;
  }

  .special-header h2 {
    font-size: 20px !important;
    padding: 8px !important;
  }
}

/* Dark Mode Support */
[data-theme="dark"] .qr-code-container qrcode {
  background-color: white; /* QR code needs to stay white for readability */
}