<div class="selection-dialog-container">
  <!-- Header -->
  <div class="dialog-header">
    <div class="header-content">
      <div class="header-icon">
        <i class="fas fa-snowflake"></i>
      </div>
      <h2 class="dialog-title">Üyelik Seçimi</h2>
    </div>
    <button class="close-btn" (click)="onCancel()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Content -->
  <div class="dialog-content">
    <!-- Member Info -->
    <div class="member-section">
      <div class="member-avatar" [style.background-color]="getAvatarColor(data.memberName)">
        {{ getInitials(data.memberName) }}
      </div>
      <div class="member-details">
        <h3 class="member-name">{{ data.memberName }}</h3>
        <p class="member-subtitle">{{ data.activeMemberships.length }} aktif üyelik</p>
      </div>
    </div>

    <!-- Info Message -->
    <div class="info-section">
      <div class="info-box">
        <i class="fas fa-info-circle"></i>
        <span>Dondurmak istediğiniz üyeliği seçin.</span>
      </div>
    </div>

    <!-- Selection Text -->
    <div class="selection-section">
      <h4>Hangi üyeliği dondurmak istiyorsunuz?</h4>
    </div>

    <!-- Membership List -->
    <div class="memberships-container">
      <div
        *ngFor="let membership of data.activeMemberships"
        class="membership-card"
        [class.selected]="selectedMembershipId === membership.membershipID"
        (click)="selectedMembershipId = membership.membershipID">

        <div class="membership-radio">
          <input
            type="radio"
            [value]="membership.membershipID"
            [(ngModel)]="selectedMembershipId"
            [id]="'membership-' + membership.membershipID">
        </div>

        <div class="membership-content">
          <div class="membership-header">
            <h5 class="package-name">{{ membership.packageName }}</h5>
            <span class="branch-name">{{ membership.branch }}</span>
          </div>

          <div class="membership-meta">
            <div class="remaining-time"
                 [ngClass]="{
                   'status-good': membership.remainingDays > 10,
                   'status-warning': membership.remainingDays <= 10 && membership.remainingDays > 3,
                   'status-danger': membership.remainingDays <= 3
                 }">
              <i class="fas fa-clock"></i>
              {{ membership.remainingDays }} gün kaldı
            </div>

            <div class="date-range">
              <i class="fas fa-calendar"></i>
              {{ membership.startDate | date:'dd.MM.yyyy' }} - {{ membership.endDate | date:'dd.MM.yyyy' }}
            </div>
          </div>

          <div class="membership-status" *ngIf="membership.isFrozen">
            <span class="frozen-indicator">
              <i class="fas fa-snowflake"></i>
              Dondurulmuş
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="dialog-footer">
    <button
      type="button"
      class="btn btn-cancel"
      (click)="onCancel()">
      <i class="fas fa-times me-2"></i>
      İptal
    </button>
    <button
      type="button"
      class="btn btn-select"
      [disabled]="!selectedMembershipId"
      (click)="onSelect()">
      <i class="fas fa-snowflake me-2"></i>
      Seçileni Dondur
    </button>
  </div>
</div>
