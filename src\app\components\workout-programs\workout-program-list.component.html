<div class="container-fluid" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Antrenman programları yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <!-- Header -->
  <div class="page-header">
    <div class="page-title-container">
      <div class="d-flex align-items-center gap-2">
        <h1 class="page-title">
          <fa-icon [icon]="faDumbbell" class="page-icon"></fa-icon>
          Antrenman Programları
        </h1>

        <!-- Help Button -->
        <app-help-button
          guideId="workout-programs"
          position="inline"
          size="small"
          tooltip="Bu panel hakkında yardım al">
        </app-help-button>
      </div>
      <p class="page-subtitle">Antrenman programı oluşturma ve yönetim sistemi</p>
    </div>
    <div class="page-actions">
      <button
        *ngIf="isOwner || isAdmin"
        class="modern-btn modern-btn-primary"
        (click)="openAddProgramModal()">
        <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon>
        Program Oluştur
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div class="modern-card mb-4">
    <div class="modern-card-body">
      <div class="row g-3">
        <!-- Search -->
        <div class="col-md-4">
          <div class="modern-form-group">
            <label class="modern-form-label">
              <fa-icon [icon]="faSearch" class="me-1"></fa-icon>
              Program Ara
            </label>
            <input 
              type="text" 
              class="modern-form-control"
              placeholder="Program adı veya açıklama..."
              [value]="searchText"
              (input)="onSearch($event)">
          </div>
        </div>

        <!-- Experience Level Filter -->
        <div class="col-md-4">
          <div class="modern-form-group">
            <label class="modern-form-label">Deneyim Seviyesi</label>
            <select 
              class="modern-form-control"
              [(ngModel)]="selectedExperienceLevel"
              (change)="onExperienceLevelChange()">
              <option value="">Tümü</option>
              <option *ngFor="let level of experienceLevels" [value]="level.value">
                {{level.label}}
              </option>
            </select>
          </div>
        </div>

        <!-- Target Goal Filter -->
        <div class="col-md-4">
          <div class="modern-form-group">
            <label class="modern-form-label">Hedef</label>
            <select 
              class="modern-form-control"
              [(ngModel)]="selectedTargetGoal"
              (change)="onTargetGoalChange()">
              <option value="">Tümü</option>
              <option *ngFor="let goal of targetGoals" [value]="goal.value">
                {{goal.label}}
              </option>
            </select>
          </div>
        </div>

        <!-- Clear Filters -->
        <div class="col-md-2 d-flex align-items-end">
          <button 
            *ngIf="hasActiveFilters()"
            class="modern-btn modern-btn-secondary w-100"
            (click)="clearFilters()">
            <fa-icon [icon]="faFilter" class="modern-btn-icon"></fa-icon>
            Temizle
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Results Info -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <div class="text-muted">
      <span>Toplam {{totalItems}} program bulundu</span>
    </div>
  </div>

  <!-- Programs Table -->
  <div class="modern-card">
    <div class="table-responsive">
      <table class="table modern-table mb-0">
        <thead>
          <tr>
            <th>Program Adı</th>
            <th>Açıklama</th>
            <th>Deneyim</th>
            <th>Hedef</th>
            <th>Oluşturma</th>
            <th>İşlemler</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let program of workoutPrograms">
            <td>
              <div class="fw-bold">{{program.programName}}</div>
            </td>
            <td>
              <div class="text-truncate" style="max-width: 200px;" [title]="program.description">
                {{program.description || '-'}}
              </div>
            </td>
            <td>
              <span 
                class="modern-badge"
                [ngClass]="getExperienceLevelBadgeClass(program.experienceLevel)">
                {{program.experienceLevel || '-'}}
              </span>
            </td>
            <td>
              <span 
                class="modern-badge"
                [ngClass]="getTargetGoalBadgeClass(program.targetGoal)">
                {{program.targetGoal || '-'}}
              </span>
            </td>
            <td>
              <small class="text-muted">{{formatDate(program.creationDate)}}</small>
            </td>

            <td>
              <div class="d-flex gap-1">
                <button 
                  class="modern-btn modern-btn-sm modern-btn-outline-primary"
                  (click)="viewProgramDetail(program)"
                  title="Detay Görüntüle">
                  <fa-icon [icon]="faEye"></fa-icon>
                </button>
                <button 
                  *ngIf="isOwner || isAdmin"
                  class="modern-btn modern-btn-sm modern-btn-outline-primary"
                  (click)="openEditProgramModal(program)"
                  title="Düzenle">
                  <fa-icon [icon]="faEdit"></fa-icon>
                </button>
                <button 
                  *ngIf="isOwner || isAdmin"
                  class="modern-btn modern-btn-sm modern-btn-outline-danger"
                  (click)="deleteProgram(program)"
                  title="Sil">
                  <fa-icon [icon]="faTrashAlt"></fa-icon>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Empty State -->
      <div *ngIf="workoutPrograms.length === 0" class="text-center py-5">
        <fa-icon [icon]="faDumbbell" class="text-muted mb-3" style="font-size: 3rem;"></fa-icon>
        <h5 class="text-muted">Henüz antrenman programı bulunmuyor</h5>
        <p class="text-muted">
          <span *ngIf="hasActiveFilters()">
            Arama kriterlerinize uygun program bulunamadı. Filtreleri temizleyerek tekrar deneyin.
          </span>
          <span *ngIf="!hasActiveFilters() && (isOwner || isAdmin)">
            İlk antrenman programınızı oluşturmak için "Yeni Program" butonuna tıklayın.
          </span>
          <span *ngIf="!hasActiveFilters() && !(isOwner || isAdmin)">
            Henüz hiç antrenman programı oluşturulmamış.
          </span>
        </p>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="totalPages > 1" class="modern-card-footer">
      <nav aria-label="Sayfa navigasyonu">
        <ul class="pagination justify-content-center mb-0">
          <li class="page-item" [class.disabled]="currentPage === 1">
            <button class="page-link" (click)="onPageChange(currentPage - 1)">Önceki</button>
          </li>
          <li 
            *ngFor="let page of getPaginationRange()" 
            class="page-item" 
            [class.active]="page === currentPage">
            <button class="page-link" (click)="onPageChange(page)">{{page}}</button>
          </li>
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <button class="page-link" (click)="onPageChange(currentPage + 1)">Sonraki</button>
          </li>
        </ul>
      </nav>
    </div>

  </div>
</div>
