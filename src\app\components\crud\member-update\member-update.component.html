
<div class="modern-modal-content fade-in">
  <h1 class="modern-modal-title">
    <i class="fas fa-user-edit me-3 ms-3"></i>
    <PERSON>ye Bilgilerini Güncelle
  </h1>
  
  <div class="modern-modal-body">
    <!-- Loading Spinner -->
    <div class="loading-overlay" *ngIf="isSubmitting">
      <div class="spinner-container">
        <app-loading-spinner></app-loading-spinner>
      </div>
    </div>
    
    <form [formGroup]="updateForm" class="update-form" (ngSubmit)="updateMember()">
      <!-- Form Progress Indicator -->
      <div class="progress mb-4" style="height: 6px;">
        <div 
          class="progress-bar bg-primary" 
          [style.width]="getFormProgress() + '%'"
          role="progressbar" 
          [attr.aria-valuenow]="getFormProgress()" 
          aria-valuemin="0" 
          aria-valuemax="100">
        </div>
      </div>
      
      <!-- Personal Information Section -->
      <div class="form-section mb-4">
        <h6 class="section-title">
          <i class="fas fa-user me-2"></i>
          Kişisel Bilgiler
        </h6>
        
        <div class="row">
          <div class="col-md-4 mb-3">
            <div class="modern-form-group">
              <label for="name" class="modern-form-label">Ad Soyad*</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
                <input
                  type="text"
                  id="name"
                  formControlName="name"
                  class="modern-form-control"
                  placeholder="Ad Soyad"
                  required
                />
              </div>
              <small class="text-danger" *ngIf="updateForm.get('name')?.invalid && updateForm.get('name')?.touched">
                Ad Soyad alanı zorunludur
              </small>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-form-group">
              <label for="phoneNumber" class="modern-form-label">Telefon Numarası*</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                <input
                  type="tel"
                  id="phoneNumber"
                  formControlName="phoneNumber"
                  class="modern-form-control"
                  placeholder="Telefon Numarası"
                  maxlength="11"
                  required
                />
              </div>
              <small class="text-danger" *ngIf="updateForm.get('phoneNumber')?.invalid && updateForm.get('phoneNumber')?.touched">
                Geçerli bir telefon numarası giriniz
              </small>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-form-group">
              <label for="gender" class="modern-form-label">Cinsiyet*</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                <select
                  id="gender"
                  formControlName="gender"
                  class="modern-form-control"
                  required
                >
                  <option value="">Seçiniz</option>
                  <option [value]="1">Erkek</option>
                  <option [value]="2">Kadın</option>
                </select>
              </div>
              <small class="text-danger" *ngIf="updateForm.get('gender')?.invalid && updateForm.get('gender')?.touched">
                Cinsiyet seçimi zorunludur
              </small>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Additional Information Section -->
      <div class="form-section">
        <h6 class="section-title">
          <i class="fas fa-info-circle me-2"></i>
          Ek Bilgiler
        </h6>
        
        <div class="row">
          <div class="col-md-4 mb-3">
            <div class="modern-form-group">
              <label for="birthDate" class="modern-form-label">Doğum Tarihi</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                <input
                  type="date"
                  id="birthDate"
                  formControlName="birthDate"
                  class="modern-form-control"
                  min="1924-12-31"
                  max="2024-12-31"
                />
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-form-group">
              <label for="adress" class="modern-form-label">Adres</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                <input
                  type="text"
                  id="adress"
                  formControlName="adress"
                  class="modern-form-control"
                  placeholder="Adres"
                />
              </div>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="modern-form-group">
              <label for="email" class="modern-form-label">E-posta</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                <input
                  type="email"
                  id="email"
                  formControlName="email"
                  class="modern-form-control"
                  placeholder="E-posta"
                />
              </div>
              <small class="text-danger" *ngIf="updateForm.get('email')?.invalid && updateForm.get('email')?.touched">
                Geçerli bir e-posta adresi giriniz
              </small>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modern-modal-footer">
        <button 
          type="button" 
          class="modern-btn modern-btn-secondary" 
          (click)="onNoClick()"
        >
          <i class="fas fa-times me-2"></i>
          İptal
        </button>
        <button 
          type="submit" 
          class="modern-btn modern-btn-primary" 
          [disabled]="!updateForm.valid || isSubmitting"
          #updateButton
        >
          <i class="fas fa-save me-2"></i>
          {{ isSubmitting ? 'Güncelleniyor...' : 'Güncelle' }}
        </button>
      </div>
    </form>
  </div>
</div>
