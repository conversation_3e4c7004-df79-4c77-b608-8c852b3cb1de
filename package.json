{"name": "gymproject", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:gymproject": "node dist/gymproject/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.1.1", "@angular/cdk": "^19.1.0", "@angular/common": "^19.1.1", "@angular/compiler": "^19.1.1", "@angular/core": "^19.1.1", "@angular/forms": "^19.1.1", "@angular/material": "^19.1.0", "@angular/platform-browser": "^19.1.1", "@angular/platform-browser-dynamic": "^19.1.1", "@angular/platform-server": "^19.1.1", "@angular/router": "^19.1.1", "@angular/ssr": "^19.1.2", "@auth0/angular-jwt": "^5.2.0", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@types/exceljs": "^0.5.3", "@types/file-saver": "^2.0.7", "@types/xlsx": "^0.0.35", "angularx-qrcode": "^19.0.0", "bootstrap": "^5.3.3", "chart.js": "^4.4.7", "exceljs": "^4.4.0", "express": "^4.18.2", "file-saver": "^2.0.5", "jquery": "^3.7.1", "ng2-charts": "^8.0.0", "ngx-pagination": "^6.0.3", "ngx-toastr": "^19.0.0", "primeng": "^19.0.5", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.2", "@angular/cli": "^19.1.2", "@angular/compiler-cli": "^19.1.1", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/jquery": "^3.5.30", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.3"}}