<div class="delete-dialog-container">
  <!-- Header -->
  <div class="dialog-header">
    <div class="header-content">
      <div class="header-icon">
        <i class="fas fa-trash-alt"></i>
      </div>
      <h2 class="dialog-title">Üyelik Silme</h2>
    </div>
    <button class="close-btn" (click)="onCancel()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Content -->
  <div class="dialog-content">
    <!-- Member Info -->
    <div class="member-section">
      <div class="member-avatar" [style.background-color]="getAvatarColor(data.memberName)">
        {{ getInitials(data.memberName) }}
      </div>
      <div class="member-details">
        <h3 class="member-name">{{ data.memberName }}</h3>
        <p class="member-subtitle">{{ data.activeMemberships.length }} aktif üyelik</p>
      </div>
    </div>

    <!-- Warning Message -->
    <div class="warning-section">
      <div class="warning-box">
        <i class="fas fa-exclamation-triangle"></i>
        <span>Seçilen üyelik kalıcı olarak silinecek ve bu işlem geri alınamaz.</span>
      </div>
    </div>

    <!-- Selection Text -->
    <div class="selection-section">
      <h4>Hangi üyeliği silmek istiyorsunuz?</h4>
    </div>

    <!-- Membership List -->
    <div class="memberships-container">
      <div
        *ngFor="let membership of data.activeMemberships"
        class="membership-card"
        [class.selected]="selectedMembershipId === membership.membershipID"
        (click)="selectedMembershipId = membership.membershipID">

        <div class="membership-radio">
          <input
            type="radio"
            [value]="membership.membershipID"
            [(ngModel)]="selectedMembershipId"
            [id]="'membership-' + membership.membershipID">
        </div>

        <div class="membership-content">
          <div class="membership-header">
            <h5 class="package-name">{{ membership.packageName }}</h5>
            <span class="branch-name">{{ membership.branch }}</span>
          </div>

          <div class="membership-meta">
            <div class="remaining-time"
                 [ngClass]="{
                   'status-good': membership.remainingDays > 10,
                   'status-warning': membership.remainingDays <= 10 && membership.remainingDays > 3,
                   'status-danger': membership.remainingDays <= 3
                 }">
              <i class="fas fa-clock"></i>
              {{ membership.remainingDays }} gün kaldı
            </div>

            <div class="date-range">
              <i class="fas fa-calendar"></i>
              {{ membership.startDate | date:'dd.MM.yyyy' }} - {{ membership.endDate | date:'dd.MM.yyyy' }}
            </div>
          </div>

          <div class="membership-status" *ngIf="membership.isFrozen">
            <span class="frozen-indicator">
              <i class="fas fa-snowflake"></i>
              Dondurulmuş
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="dialog-footer">
    <button
      type="button"
      class="btn btn-cancel"
      (click)="onCancel()">
      <i class="fas fa-times me-2"></i>
      İptal
    </button>
    <button
      type="button"
      class="btn btn-delete"
      [disabled]="!selectedMembershipId"
      (click)="onDelete()">
      <i class="fas fa-trash-alt me-2"></i>
      Seçileni Sil
    </button>
  </div>
</div>
