/* src/app/components/help-button/help-button.component.css */

/* Base Help Button Styles */
.help-button {
  background: var(--primary-color);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
  z-index: 10;
  font-size: inherit;
}

.help-button:hover {
  background: var(--secondary-color);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.4);
}

.help-button:active {
  transform: scale(0.95);
}

.help-button i {
  transition: transform 0.3s ease;
}

.help-button:hover i {
  transform: rotate(15deg);
}

/* Size Variants */
.help-btn-small {
  width: 28px;
  height: 28px;
  font-size: 0.75rem;
}

.help-btn-medium {
  width: 36px;
  height: 36px;
  font-size: 0.875rem;
}

.help-btn-large {
  width: 44px;
  height: 44px;
  font-size: 1rem;
}

/* Position Variants */
.help-btn-top-right {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.help-btn-top-left {
  position: absolute;
  top: 1rem;
  left: 1rem;
}

.help-btn-bottom-right {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
}

.help-btn-bottom-left {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
}

.help-btn-inline {
  position: relative;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

/* Dark Mode Support */
[data-theme="dark"] .help-button {
  background: var(--primary-color);
  box-shadow: 0 2px 8px rgba(67, 97, 238, 0.4);
}

[data-theme="dark"] .help-button:hover {
  background: var(--secondary-color);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .help-btn-top-right,
  .help-btn-top-left {
    top: 0.75rem;
  }
  
  .help-btn-top-right,
  .help-btn-bottom-right {
    right: 0.75rem;
  }
  
  .help-btn-top-left,
  .help-btn-bottom-left {
    left: 0.75rem;
  }
  
  .help-btn-bottom-right,
  .help-btn-bottom-left {
    bottom: 0.75rem;
  }
  
  /* Smaller sizes on mobile */
  .help-btn-medium {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
  
  .help-btn-large {
    width: 38px;
    height: 38px;
    font-size: 0.9rem;
  }
}

/* Animation for attention */
@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(67, 97, 238, 0.5);
  }
  100% {
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
  }
}

/* Optional pulse animation for first-time users */
.help-button.pulse {
  animation: pulse 2s infinite;
}

/* Accessibility */
.help-button:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

.help-button:focus:not(:focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .help-button {
    border: 2px solid white;
  }
  
  .help-button:hover {
    border-color: var(--accent-color);
  }
}
