/* Expired Licenses Component - Modern Design */

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.empty-state-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.empty-state-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1rem;
}

/* Modern Spinner */
.modern-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Badge Styles */
.modern-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 50rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.modern-badge-warning {
  background-color: var(--warning-light);
  color: var(--warning);
  border-color: rgba(var(--warning-rgb), 0.3);
}

.total-members-badge {
  display: flex;
  align-items: center;
}

/* Modern Card Styles */
.modern-card {
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
  overflow: hidden;
  background-color: var(--card-bg-color);
}

.modern-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modern-card .card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-card .card-body {
  padding: 1.5rem;
}

/* Company Info Styling */
.company-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.company-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.company-id {
  font-size: 0.75rem;
  opacity: 0.7;
}

/* Search Input Styling */
.search-input {
  transition: all 0.3s ease;
}

.search-help-text {
  display: block;
  margin-top: 0.5rem;
  color: var(--text-muted);
  font-size: 0.75rem;
  font-style: italic;
}

/* Modern Input Styles */
.modern-label {
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.modern-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.modern-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.modern-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modern-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Modern Table Styles */
.modern-table-container {
  overflow-x: auto;
  border-radius: var(--border-radius-md);
}

.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 1.5rem;
}

.modern-table th {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-muted);
}

.modern-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
  background-color: var(--bg-secondary);
}

/* Modern Badge Styles */
.modern-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--border-radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.modern-badge-info {
  background-color: var(--info-light);
  color: var(--info-dark);
  border: 1px solid var(--info);
}

.modern-badge-secondary {
  background-color: var(--secondary-light);
  color: var(--secondary-dark);
  border: 1px solid var(--secondary);
}

.modern-badge-danger {
  background-color: var(--danger-light);
  color: var(--danger-dark);
  border: 1px solid var(--danger);
}

.modern-badge-warning {
  background-color: var(--warning-light);
  color: var(--warning-dark);
  border: 1px solid var(--warning);
}

.modern-badge-success {
  background-color: var(--success-light);
  color: var(--success-dark);
  border: 1px solid var(--success);
}

/* Modern Pagination */
.modern-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.modern-pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modern-pagination-btn:hover:not(.disabled) {
  border-color: var(--primary);
  background-color: var(--primary-light);
  color: var(--primary);
}

.modern-pagination-btn.active {
  border-color: var(--primary);
  background-color: var(--primary);
  color: white;
}

.modern-pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.modern-pagination-info {
  text-align: center;
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .modern-card-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  .modern-card-header .modern-btn {
    width: 100%;
  }

  /* Filter form responsive */
  .row.g-3.align-items-end {
    align-items: stretch !important;
  }

  .row.g-3.align-items-end .col-md-4 {
    margin-top: 1rem;
  }

  .modern-table-container {
    font-size: 0.875rem;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.75rem 0.5rem;
  }

  .modern-btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .modern-pagination {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .modern-pagination-btn {
    width: 35px;
    height: 35px;
    font-size: 0.875rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state-icon {
    font-size: 3rem;
  }

  .empty-state-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .modern-card-body {
    padding: 1rem;
  }

  .modern-input,
  .modern-select {
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
  }

  .modern-table-container {
    font-size: 0.8125rem;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.5rem 0.375rem;
  }

  .modern-btn-sm {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
  }

  .modern-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }
}


