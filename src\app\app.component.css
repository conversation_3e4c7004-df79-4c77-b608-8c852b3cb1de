/* Initializing Overlay */
.app-initializing {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.initializing-spinner {
    position: relative;
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-circle {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    animation: spin 1.5s linear infinite;
}

.dumbbell {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: lift 2s ease-in-out infinite;
}

.weight {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);
}

.inner-weight {
    width: 50%;
    height: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.weight.left {
    animation: pulse-left 2s ease-in-out infinite;
}

.weight.right {
    animation: pulse-right 2s ease-in-out infinite;
}

.handle {
    height: 8px;
    width: 50px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(67, 97, 238, 0.5);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes lift {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

@keyframes pulse-left {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes pulse-right {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Main App Container */
.app-container {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
    opacity: 1;
    visibility: visible;
}

.app-container.initializing {
    opacity: 0;
    visibility: hidden;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
}

.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    transition: all 0.3s ease;
}

/* Mobile Header */
.mobile-header {
    display: none;
    padding: 15px;
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px var(--shadow-color);
    z-index: 100;
}

.mobile-title {
    font-size: 18px;
    font-weight: 600;
}

.sidebar-toggle, .theme-toggle {
    background: none;
    border: none;
    color: var(--sidebar-text);
    font-size: 18px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover, .theme-toggle:hover {
    background-color: var(--sidebar-hover);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .mobile-header {
        display: flex;
    }
    
    .app-container.sidebar-collapsed .main-content {
        margin-left: 0;
    }
}

/* Initializing state in body */
body.initializing {
    overflow: hidden;
}
