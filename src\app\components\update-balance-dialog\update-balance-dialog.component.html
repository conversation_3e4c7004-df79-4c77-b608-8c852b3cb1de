<div class="modern-dialog fade-in">
  <div class="modern-dialog-header">
    <h2 class="modern-dialog-title"><PERSON><PERSON><PERSON> Güncelleme</h2>
    <button class="modern-btn modern-btn-sm" (click)="closeDialog()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="modern-dialog-content">
    <!-- Yükleme Göstergesi -->
    <div class="loading-container" *ngIf="isLoading">
      <app-loading-spinner></app-loading-spinner>
    </div>

    <!-- Üye Bilgileri -->
    <div class="member-info mb-4" *ngIf="!isLoading && member">
      <div class="d-flex align-items-center">
        <div class="modern-avatar" [style.background-color]="'var(--primary)'">
          {{ member.name.charAt(0).toUpperCase() }}
        </div>
        <div class="ms-3">
          <h4 class="mb-1">{{ member.name }}</h4>
          <div class="d-flex align-items-center">
            <span class="modern-badge" [ngClass]="member.balance >= 0 ? 'modern-badge-success' : 'modern-badge-danger'">
              Mevcut Bakiye: {{ member.balance | currency:'TRY':'symbol-narrow':'1.2-2' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Bakiye Güncelleme Formu -->
    <form [formGroup]="balanceForm" (ngSubmit)="updateBalance()" *ngIf="!isLoading">
      <div class="modern-form-group">
        <label for="amount" class="modern-form-label">İşlem Tipi</label>
        <div class="d-flex gap-3 mb-3">
          <div class="form-check">
            <input class="form-check-input" type="radio" formControlName="operationType" id="add" value="add" checked>
            <label class="form-check-label" for="add">
              <i class="fas fa-plus-circle text-success me-1"></i> Bakiye Ekle
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" formControlName="operationType" id="subtract" value="subtract">
            <label class="form-check-label" for="subtract">
              <i class="fas fa-minus-circle text-danger me-1"></i> Bakiye Çıkar
            </label>
          </div>
        </div>
      </div>

      <div class="modern-form-group">
        <label for="amount" class="modern-form-label">Tutar</label>
        <div class="d-flex align-items-center">
          <div class="input-group-text me-2">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <input type="number" class="modern-form-control" id="amount" formControlName="amount" placeholder="0.00" min="0" style="width: 95%;">
        </div>
        <small class="text-danger" *ngIf="balanceForm.get('amount')?.invalid && balanceForm.get('amount')?.touched">
          Geçerli bir tutar giriniz
        </small>
      </div>

      <!-- Removed the note section -->

      <div class="d-flex justify-content-between mt-4">
        <button type="button" class="modern-btn modern-btn-secondary" (click)="closeDialog()">
          <i class="fas fa-times modern-btn-icon"></i> İptal
        </button>
        <button type="submit" class="modern-btn" [ngClass]="balanceForm.get('operationType')?.value === 'add' ? 'modern-btn-success' : 'modern-btn-danger'" [disabled]="!balanceForm.valid">
          <i class="fas" [ngClass]="balanceForm.get('operationType')?.value === 'add' ? 'fa-plus-circle' : 'fa-minus-circle'"></i>
          {{ balanceForm.get('operationType')?.value === 'add' ? 'Bakiye Ekle' : 'Bakiye Çıkar' }}
        </button>
      </div>
    </form>
  </div>
</div>

<style>
  .modern-dialog {
    max-width: 500px;
    width: 100%;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
  }

  .modern-dialog-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-secondary);
  }

  .modern-dialog-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .modern-dialog-content {
    padding: var(--spacing-lg);
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  .member-info {
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
  }

  .member-info:hover {
    background-color: var(--bg-tertiary);
    box-shadow: var(--shadow-sm);
  }
</style>
