/* Workout Program List Specific Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: var(--border-radius-lg);
  color: white;
}

.page-title-container {
  flex: 1;
}

.page-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-icon {
  font-size: 1.5rem;
}

.page-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .page-actions {
    width: 100%;
    justify-content: stretch;
  }

  .page-actions .modern-btn {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: 1.5rem;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* Existing styles below */

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table th {
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 2px solid var(--border-color);
  padding: 1rem 0.75rem;
}

.table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.pagination .page-link {
  color: var(--primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
}

.pagination .page-link:hover {
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
  transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
}

.pagination .page-item.disabled .page-link {
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  cursor: not-allowed;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Badge Styles */
.modern-badge-secondary {
  background-color: var(--secondary-light);
  color: var(--secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .modern-btn-sm {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
  
  .d-flex.gap-1 {
    flex-direction: column;
    gap: 0.25rem !important;
  }
  
  .text-truncate {
    max-width: 120px !important;
  }
}

/* Dark mode specific adjustments */
[data-theme="dark"] .pagination .page-link {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .pagination .page-link:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}
