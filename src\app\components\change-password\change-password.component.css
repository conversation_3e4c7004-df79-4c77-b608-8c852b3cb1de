.main-content {
  padding: 30px 0;
  min-height: calc(100vh - 70px);
}

.card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: var(--card-bg-color);
  margin-bottom: 30px;
  overflow: hidden;
}

.card-header {
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 25px;
}

.card-title {
  margin: 0;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-title i {
  color: var(--primary-color);
}

.card-body {
  padding: 25px;
}

.alert {
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.alert i {
  margin-top: 3px;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
  color: var(--text-color);
}

.alert-info {
  background-color: rgba(13, 202, 240, 0.1);
  border-left: 4px solid #0dcaf0;
  color: var(--text-color);
}

.modern-form {
  margin-top: 20px;
}

.form-row {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.modern-form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.input-group {
  position: relative;
  display: flex;
  align-items: stretch;
  width: 100%;
}

.input-group-text {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-right: none;
  color: var(--text-muted);
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  padding: 0.5rem 1rem;
}

.modern-form-control {
  flex: 1;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border: 1px solid var(--input-border);
  background-color: var(--input-bg);
  color: var(--input-text);
  border-radius: 0;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.modern-form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
  outline: none;
}

.modern-form-control.is-invalid {
  border-color: #dc3545;
}

.password-toggle {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid var(--input-border);
  background-color: var(--input-bg);
  color: var(--text-muted);
  padding: 0.5rem 1rem;
  cursor: pointer;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.password-requirements {
  background-color: var(--card-background-secondary, #2d2d2d);
  border: none;
  border-radius: 8px;
}

.password-requirements .card-title {
  font-size: 1rem;
  margin-bottom: 10px;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  color: var(--text-secondary, #a0a0a0);
}

.requirements-list li.fulfilled {
  color: var(--text-color, #e0e0e0);
}

.btn-primary {
  background-color: var(--primary-color, #4361ee);
  border-color: var(--primary-color, #4361ee);
  color: white;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--secondary-color, #3f37c9);
  border-color: var(--secondary-color, #3f37c9);
  transform: translateY(-2px);
}

.btn-primary:disabled {
  background-color: var(--text-muted, #6c757d);
  border-color: var(--text-muted, #6c757d);
  cursor: not-allowed;
  opacity: 0.7;
}

.support {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--text-muted, #6c757d);
  font-size: 0.9rem;
}

.support i {
  color: var(--primary-color, #4361ee);
}

.support a {
  color: var(--primary-color, #4361ee);
  text-decoration: none;
  transition: color 0.3s;
}

.support a:hover {
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .card-body {
    padding: 20px 15px;
  }

  .card-title {
    font-size: 1.3rem;
  }

  .btn-lg {
    padding: 8px 16px;
    font-size: 0.95rem;
  }
}