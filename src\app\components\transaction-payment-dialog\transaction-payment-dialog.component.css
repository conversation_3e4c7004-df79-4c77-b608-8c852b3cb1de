.modern-dialog {
  min-width: 350px;
  max-width: 100%;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  animation: zoomIn 0.3s var(--transition-timing);
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.modern-card-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modern-card-body {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.dialog-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.75rem;
  background-color: var(--success-light);
  color: var(--success);
}

.dialog-message {
  font-size: 1rem;
  margin: 0 0 var(--spacing-md);
  line-height: 1.5;
  max-width: 400px;
  color: var(--text-primary);
}

.modern-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.modern-btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 1rem;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.modern-btn-icon:hover {
  color: var(--text-primary);
}

.modern-form-group {
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  text-align: left;
  color: var(--text-primary);
}

.modern-form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease, color 0.3s ease;
}

.modern-form-control:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

@media screen and (max-width: 480px) {
  .modern-dialog {
    min-width: 280px;
  }

  .modern-card-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .modern-card-footer {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .dialog-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }
}

@keyframes zoomIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

:host-context([data-theme="dark"]) .modern-dialog {

}

:host-context([data-theme="dark"]) .modern-card-header {
  background-color: var(--bg-secondary);
  border-bottom-color: var(--border-color);
}

:host-context([data-theme="dark"]) .modern-card-header h2 {
  color: var(--text-primary);
}

:host-context([data-theme="dark"]) .dialog-icon {
  background-color: var(--success-light);
  color: var(--success);

}

:host-context([data-theme="dark"]) .dialog-message {
  color: var(--text-primary);
}

:host-context([data-theme="dark"]) .modern-card-footer {
  background-color: var(--bg-secondary);
  border-top-color: var(--border-color);
}

:host-context([data-theme="dark"]) .modern-btn-icon {
  color: var(--text-secondary);
}

:host-context([data-theme="dark"]) .modern-btn-icon:hover {
  color: var(--text-primary);
}

:host-context([data-theme="dark"]) .modern-form-label {
  color: var(--text-primary);
}

:host-context([data-theme="dark"]) .modern-form-control {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

:host-context([data-theme="dark"]) .modern-form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

:host-context([data-theme="dark"]) .modern-form-control option {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}


:host-context([data-theme="dark"]) .modern-btn-success {
    background-color: var(--success);
    color: var(--white);
}
:host-context([data-theme="dark"]) .modern-btn-success:hover {
    background-color: #388e3c;
}

:host-context([data-theme="dark"]) .modern-btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--text-secondary);
}
:host-context([data-theme="dark"]) .modern-btn-outline-secondary:hover {
    background-color: var(--text-secondary);
    color: var(--bg-primary);
}