<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Dondurulmuş üyelikler yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">

    <!-- Page Header with Help Button -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <div class="d-flex align-items-center gap-2">
              <h5 class="mb-0">
                <i class="fas fa-snowflake me-2"></i>
                Dondurulmuş Üyelikler
              </h5>

              <!-- Help Button -->
              <app-help-button
                guideId="frozen-memberships"
                position="inline"
                size="small"
                tooltip="Bu panel hakkında yardım al">
              </app-help-button>
            </div>
            <p class="text-muted mb-0 mt-1">
              Dondurulmuş üyeliklerin yönetimi ve takibi
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Toggle Buttons -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="toggle-buttons-container">
          <button
            class="modern-btn toggle-btn"
            [class.active]="showFrozenMemberships"
            (click)="setActiveView(true)">
            <i class="fas fa-snowflake me-2"></i>
            Aktif Dondurulmuş Üyelikler
            <span class="badge-count" *ngIf="frozenMemberships.length > 0">{{ frozenMemberships.length }}</span>
          </button>
          <button
            class="modern-btn toggle-btn"
            [class.active]="!showFrozenMemberships"
            (click)="setActiveView(false)">
            <i class="fas fa-history me-2"></i>
            Dondurma Geçmişi
            <span class="badge-count" *ngIf="freezeHistories.length > 0">{{ freezeHistories.length }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Aktif Dondurulmuş Üyelikler -->
    <div class="row" *ngIf="showFrozenMemberships">

      <!-- Üyelik Kartları (Mobil ve Tablet) -->
      <div class="col-12 d-lg-none">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-snowflake me-2"></i>
              Dondurulmuş Üyelikler
            </h5>
          </div>
          <div class="modern-card-body">
            <!-- Arama Kutusu -->
            <div class="row mb-3">
              <div class="col-12">
                <div class="search-input-container">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    class="form-control search-input"
                    placeholder="Üye adı veya telefon ile ara..."
                    [(ngModel)]="searchText"
                    (input)="onSearchChange($event)">
                </div>
              </div>
            </div>

            <!-- Üyelik Kartları -->
            <div class="membership-cards-container">
          <div
            *ngFor="let membership of getFilteredMemberships(); trackBy: trackByMembershipId"
            class="membership-card"
            [ngClass]="getMembershipCardClass(membership)">

            <div class="membership-card-header">
              <div class="member-info">
                <div class="member-avatar" [style.background-color]="getAvatarColor(membership.memberName)">
                  {{ membership.memberName.charAt(0).toUpperCase() }}
                </div>
                <div class="member-details">
                  <h6 class="member-name">{{ membership.memberName }}</h6>
                  <p class="member-phone">{{ membership.phoneNumber }}</p>
                </div>
              </div>
              <div class="status-badge" [ngClass]="getStatusBadgeClass(membership)">
                <i class="fas fa-clock me-1"></i>
                {{ getRemainingDays(membership.freezeEndDate) }} gün
              </div>
            </div>

            <div class="membership-card-body">
              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-dumbbell text-muted me-2"></i>
                  <span class="info-label">Branş:</span>
                  <span class="info-value">{{ membership.branch }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-calendar-plus text-muted me-2"></i>
                  <span class="info-label">Başlangıç:</span>
                  <span class="info-value">{{ membership.freezeStartDate | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-calendar-minus text-muted me-2"></i>
                  <span class="info-label">Bitiş:</span>
                  <span class="info-value">{{ membership.freezeEndDate | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>
            </div>

            <div class="membership-card-footer">
              <button
                class="modern-btn modern-btn-danger modern-btn-sm"
                (click)="cancelFreeze(membership)"
                [disabled]="isProcessing"
                title="Dondurma işlemini tamamen iptal et">
                <i class="fas fa-undo me-1"></i>
                İptal Et
              </button>
              <button
                class="modern-btn modern-btn-warning modern-btn-sm"
                (click)="reactivateFromToday(membership)"
                [disabled]="isProcessing"
                title="Bugünden itibaren üyeliği aktif et">
                <i class="fas fa-play me-1"></i>
                Aktif Et
              </button>
            </div>
          </div>

            <!-- Veri yoksa gösterilecek mesaj -->
            <div *ngIf="getFilteredMemberships().length === 0" class="empty-state">
              <div class="empty-state-icon">
                <i class="fas fa-snowflake"></i>
              </div>
              <h5 class="empty-state-title">{{ searchText ? 'Arama sonucu bulunamadı' : 'Dondurulmuş üyelik bulunmuyor' }}</h5>
              <p class="empty-state-text">
                {{ searchText ? 'Farklı arama terimleri deneyebilirsiniz.' : 'Henüz dondurulmuş üyelik bulunmamaktadır.' }}
              </p>
            </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Masaüstü Tablo Görünümü -->
      <div class="col-12 d-none d-lg-block">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-snowflake me-2"></i>
              Dondurulmuş Üyelikler
            </h5>
          </div>
          <div class="modern-card-body">
            <!-- Arama Kutusu -->
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="search-input-container">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    class="form-control search-input"
                    placeholder="Üye adı veya telefon ile ara..."
                    [(ngModel)]="searchText"
                    (input)="onSearchChange($event)">
                </div>
              </div>
            </div>
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      <i class="fas fa-user me-2"></i>
                      Üye Bilgileri
                    </th>
                    <th>
                      <i class="fas fa-dumbbell me-2"></i>
                      Branş
                    </th>
                    <th>
                      <i class="fas fa-calendar-alt me-2"></i>
                      Dondurma Tarihleri
                    </th>
                    <th class="text-center">
                      <i class="fas fa-hourglass-half me-2"></i>
                      Durum
                    </th>
                    <th class="text-center">
                      <i class="fas fa-cogs me-2"></i>
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let membership of getFilteredMemberships(); trackBy: trackByMembershipId"
                      class="table-row"
                      [ngClass]="getTableRowClass(membership)">
                    <td>
                      <div class="member-info-table">
                        <div class="member-avatar" [style.background-color]="getAvatarColor(membership.memberName)">
                          {{ membership.memberName.charAt(0).toUpperCase() }}
                        </div>
                        <div class="member-details">
                          <div class="member-name">{{ membership.memberName }}</div>
                          <div class="member-phone">{{ membership.phoneNumber }}</div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span class="modern-badge modern-badge-info">{{ membership.branch }}</span>
                    </td>
                    <td>
                      <div class="date-info">
                        <div class="date-item">
                          <i class="fas fa-play text-success me-1"></i>
                          <span class="date-label">Başlangıç:</span>
                          <span class="date-value">{{ membership.freezeStartDate | date:'dd/MM/yyyy' }}</span>
                        </div>
                        <div class="date-item">
                          <i class="fas fa-stop text-danger me-1"></i>
                          <span class="date-label">Bitiş:</span>
                          <span class="date-value">{{ membership.freezeEndDate | date:'dd/MM/yyyy' }}</span>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <div class="status-info">
                        <div class="status-badge-large" [ngClass]="getStatusBadgeClass(membership)">
                          <i class="fas fa-clock me-1"></i>
                          {{ getRemainingDays(membership.freezeEndDate) }} gün
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <div class="action-buttons-table">
                        <button
                          class="modern-btn modern-btn-danger modern-btn-sm"
                          (click)="cancelFreeze(membership)"
                          [disabled]="isProcessing"
                          title="Dondurma işlemini tamamen iptal et">
                          <i class="fas fa-undo me-1"></i>
                          İptal Et
                        </button>
                        <button
                          class="modern-btn modern-btn-warning modern-btn-sm ms-2"
                          (click)="reactivateFromToday(membership)"
                          [disabled]="isProcessing"
                          title="Bugünden itibaren üyeliği aktif et">
                          <i class="fas fa-play me-1"></i>
                          Aktif Et
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- Veri yoksa gösterilecek mesaj -->
              <div *ngIf="getFilteredMemberships().length === 0" class="empty-state-table">
                <div class="empty-state-icon">
                  <i class="fas fa-snowflake"></i>
                </div>
                <h5 class="empty-state-title">{{ searchText ? 'Arama sonucu bulunamadı' : 'Dondurulmuş üyelik bulunmuyor' }}</h5>
                <p class="empty-state-text">
                  {{ searchText ? 'Farklı arama terimleri deneyebilirsiniz.' : 'Henüz dondurulmuş üyelik bulunmamaktadır.' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dondurma İşlem Geçmişi -->
    <div class="row" *ngIf="!showFrozenMemberships">

      <!-- Geçmiş Kartları (Mobil ve Tablet) -->
      <div class="col-12 d-lg-none">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-history me-2"></i>
              Dondurma Geçmişi
            </h5>
          </div>
          <div class="modern-card-body">
            <!-- Arama Kutusu -->
            <div class="row mb-3">
              <div class="col-12">
                <div class="search-input-container">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    class="form-control search-input"
                    placeholder="Üye adı veya telefon ile ara..."
                    [(ngModel)]="searchText"
                    (input)="onSearchChange($event)">
                </div>
              </div>
            </div>

            <!-- Geçmiş Kartları -->
            <div class="history-cards-container">
          <div
            *ngFor="let history of getFilteredHistories(); trackBy: trackByHistoryId"
            class="history-card">

            <div class="history-card-header">
              <div class="member-info">
                <div class="member-avatar" [style.background-color]="getAvatarColor(history.memberName)">
                  {{ history.memberName.charAt(0).toUpperCase() }}
                </div>
                <div class="member-details">
                  <h6 class="member-name">{{ history.memberName }}</h6>
                  <p class="member-phone">{{ history.phoneNumber }}</p>
                </div>
              </div>
              <div class="history-type-badge" [ngClass]="getHistoryTypeBadgeClass(history)">
                {{ history.cancellationType || 'Tamamlandı' }}
              </div>
            </div>

            <div class="history-card-body">
              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-dumbbell text-muted me-2"></i>
                  <span class="info-label">Branş:</span>
                  <span class="info-value">{{ history.branch }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-calendar-plus text-muted me-2"></i>
                  <span class="info-label">Başlangıç:</span>
                  <span class="info-value">{{ formatDate(history.startDate) }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-calendar-minus text-muted me-2"></i>
                  <span class="info-label">Planlanan Bitiş:</span>
                  <span class="info-value">{{ formatDate(history.plannedEndDate) }}</span>
                </div>
              </div>

              <div class="info-row" *ngIf="history.actualEndDate">
                <div class="info-item">
                  <i class="fas fa-calendar-check text-muted me-2"></i>
                  <span class="info-label">Sonlandırıldı:</span>
                  <span class="info-value">{{ formatDate(history.actualEndDate) }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-clock text-muted me-2"></i>
                  <span class="info-label">Toplam Süre:</span>
                  <span class="info-value">{{ history.freezeDays }} gün</span>
                </div>
              </div>

              <div class="info-row" *ngIf="history.usedDays !== null">
                <div class="info-item">
                  <i class="fas fa-hourglass-half text-muted me-2"></i>
                  <span class="info-label">Kullanılan:</span>
                  <span class="info-value">{{ history.usedDays }} gün</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <i class="fas fa-calendar-day text-muted me-2"></i>
                  <span class="info-label">İşlem Tarihi:</span>
                  <span class="info-value">{{ formatDate(history.creationDate) }}</span>
                </div>
              </div>
            </div>
          </div>

            <!-- Veri yoksa gösterilecek mesaj -->
            <div *ngIf="getFilteredHistories().length === 0" class="empty-state">
              <div class="empty-state-icon">
                <i class="fas fa-history"></i>
              </div>
              <h5 class="empty-state-title">{{ searchText ? 'Arama sonucu bulunamadı' : 'Dondurma geçmişi bulunmuyor' }}</h5>
              <p class="empty-state-text">
                {{ searchText ? 'Farklı arama terimleri deneyebilirsiniz.' : 'Henüz dondurma geçmişi bulunmamaktadır.' }}
              </p>
            </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Masaüstü Geçmiş Tablosu -->
      <div class="col-12 d-none d-lg-block">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-history me-2"></i>
              Dondurma Geçmişi
            </h5>
          </div>
          <div class="modern-card-body">
            <!-- Arama Kutusu -->
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="search-input-container">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    class="form-control search-input"
                    placeholder="Üye adı veya telefon ile ara..."
                    [(ngModel)]="searchText"
                    (input)="onSearchChange($event)">
                </div>
              </div>
            </div>
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      <i class="fas fa-user me-2"></i>
                      Üye Bilgileri
                    </th>
                    <th>
                      <i class="fas fa-dumbbell me-2"></i>
                      Branş
                    </th>
                    <th>
                      <i class="fas fa-calendar-alt me-2"></i>
                      Dondurma Tarihleri
                    </th>
                    <th class="text-center">
                      <i class="fas fa-clock me-2"></i>
                      Süre Bilgileri
                    </th>
                    <th class="text-center">
                      <i class="fas fa-tag me-2"></i>
                      İşlem Türü
                    </th>
                    <th class="text-center">
                      <i class="fas fa-calendar-day me-2"></i>
                      İşlem Tarihi
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let history of getFilteredHistories(); trackBy: trackByHistoryId" class="table-row">
                    <td>
                      <div class="member-info-table">
                        <div class="member-avatar" [style.background-color]="getAvatarColor(history.memberName)">
                          {{ history.memberName.charAt(0).toUpperCase() }}
                        </div>
                        <div class="member-details">
                          <div class="member-name">{{ history.memberName }}</div>
                          <div class="member-phone">{{ history.phoneNumber }}</div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span class="modern-badge modern-badge-info">{{ history.branch }}</span>
                    </td>
                    <td>
                      <div class="date-info">
                        <div class="date-item">
                          <i class="fas fa-play text-success me-1"></i>
                          <span class="date-label">Başlangıç:</span>
                          <span class="date-value">{{ formatDate(history.startDate) }}</span>
                        </div>
                        <div class="date-item">
                          <i class="fas fa-stop text-warning me-1"></i>
                          <span class="date-label">Planlanan:</span>
                          <span class="date-value">{{ formatDate(history.plannedEndDate) }}</span>
                        </div>
                        <div class="date-item" *ngIf="history.actualEndDate">
                          <i class="fas fa-check text-success me-1"></i>
                          <span class="date-label">Sonlandırıldı:</span>
                          <span class="date-value">{{ formatDate(history.actualEndDate) }}</span>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <div class="duration-info">
                        <div class="duration-item">
                          <span class="modern-badge modern-badge-primary">{{ history.freezeDays }} gün</span>
                          <small class="text-muted d-block">Toplam</small>
                        </div>
                        <div class="duration-item" *ngIf="history.usedDays !== null">
                          <span class="modern-badge modern-badge-success">{{ history.usedDays }} gün</span>
                          <small class="text-muted d-block">Kullanılan</small>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <span class="modern-badge" [ngClass]="getHistoryTypeBadgeClass(history)">
                        {{ history.cancellationType || 'Tamamlandı' }}
                      </span>
                    </td>
                    <td class="text-center">
                      <span class="text-muted">{{ formatDate(history.creationDate) }}</span>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- Veri yoksa gösterilecek mesaj -->
              <div *ngIf="getFilteredHistories().length === 0" class="empty-state-table">
                <div class="empty-state-icon">
                  <i class="fas fa-history"></i>
                </div>
                <h5 class="empty-state-title">{{ searchText ? 'Arama sonucu bulunamadı' : 'Dondurma geçmişi bulunmuyor' }}</h5>
                <p class="empty-state-text">
                  {{ searchText ? 'Farklı arama terimleri deneyebilirsiniz.' : 'Henüz dondurma geçmişi bulunmamaktadır.' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
