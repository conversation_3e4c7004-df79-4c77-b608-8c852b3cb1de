/* Modern Modal Content Styles */
.modern-modal-content {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  animation: fadeIn 0.3s ease;
}

.modern-modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-modal-body {
  position: relative;
  padding: 1.5rem;
}

.modern-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Form Styles */
.update-form {
  display: flex;
  flex-direction: column;
}

.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: rgba(0, 0, 0, 0.03);
  box-shadow: var(--shadow-sm);
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--secondary-dark);
  margin-bottom: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Input Group Styles */
.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--primary-light);
  color: var(--primary);
  border: none;
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.modern-form-control {
  flex: 1;
  height: 40px;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-left: none;
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  transition: all 0.3s ease;
}

.modern-form-control:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--secondary-dark);
}

/* Progress Bar Animation */
.progress {
  height: 6px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: var(--border-radius-pill);
  overflow: hidden;
}

.progress-bar {
  background-color: var(--primary);
  transition: width 0.5s ease;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(3px);
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .modern-modal-content {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }
  
  .form-section {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .form-section:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
  
  .modern-form-control {
    background-color: var(--bg-tertiary);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
  }
  
  .loading-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }
}
  