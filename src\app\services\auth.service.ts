// services/auth.service.ts
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, of } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';
import { TokenResponse } from '../models/refreshToken';
import { UserModel } from '../models/userModel';
import { JwtHelperService } from '@auth0/angular-jwt';
import { BaseApiService } from './baseApiService';
import { LoginModel } from '../models/loginModel';
import { UserDevice } from '../models/userDevice';
import { Router } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class AuthService extends BaseApiService {
  private refreshTokenTimeout: any;
  private jwtHelper: JwtHelperService = new JwtHelperService();
  private currentUserSubject: BehaviorSubject<UserModel | null>;
  public currentUser: Observable<UserModel | null>;
  private refreshTokenInProgress = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  private deviceInfo: string = '{}';
  private isBrowser: boolean;

  constructor(
    private httpClient: HttpClient,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    super();
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.currentUserSubject = new BehaviorSubject<UserModel | null>(null);
    this.currentUser = this.currentUserSubject.asObservable();

    if (this.isBrowser) {
      this.deviceInfo = this.getDeviceInfo();
      this.initializeFromStorage();
    }
  }

  public getDeviceInfo(): string {
    if (!this.isBrowser) return 'Server';

    // Eğer deviceInfo zaten oluşturulmuşsa, aynısını döndür
    if (this.deviceInfo && this.deviceInfo !== '{}') {
      return this.deviceInfo;
    }

    try {
      const deviceName = this.generateDeviceName();
      this.deviceInfo = deviceName;
      return this.deviceInfo;
    } catch (error) {
      console.error('Error getting device info:', error);
      return 'Bilinmeyen Cihaz';
    }
  }

  private generateDeviceName(): string {
    const deviceType = this.getDeviceType();
    const browser = this.getBrowserName();
    const timestamp = new Date().toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });

    return `${deviceType} - ${browser} (${timestamp})`;
  }

  private getDeviceType(): string {
    const userAgent = navigator.userAgent.toLowerCase();

    if (/iphone/.test(userAgent)) return 'iPhone';
    if (/ipad/.test(userAgent)) return 'iPad';
    if (/android.*mobile/.test(userAgent)) return 'Android Telefon';
    if (/android/.test(userAgent)) return 'Android Tablet';
    if (/windows/.test(userAgent)) return 'Windows PC';
    if (/mac/.test(userAgent)) return 'Mac';
    if (/linux/.test(userAgent)) return 'Linux PC';

    return 'Web Tarayıcı';
  }

  private getBrowserName(): string {
    const userAgent = navigator.userAgent.toLowerCase();

    if (userAgent.includes('edg/')) return 'Edge';
    if (userAgent.includes('chrome/') && !userAgent.includes('edg/')) return 'Chrome';
    if (userAgent.includes('firefox/')) return 'Firefox';
    if (userAgent.includes('safari/') && !userAgent.includes('chrome/')) return 'Safari';
    if (userAgent.includes('opera/') || userAgent.includes('opr/')) return 'Opera';

    return 'Bilinmeyen';
  }

  private initializeFromStorage() {
    if (!this.isBrowser) return;

    const accessToken = localStorage.getItem('token');
    if (accessToken) {
      try {
        const decodedToken = this.jwtHelper.decodeToken(accessToken);
        this.currentUserSubject.next(new UserModel(decodedToken));
        this.startRefreshTokenTimer();
      } catch (error) {
        console.error('Token decode error:', error);
        this.logout();
      }
    }
  }

  login(loginModel: LoginModel): Observable<TokenResponse> {
    const loginRequest = {
      loginDto: loginModel,
      deviceInfo: this.deviceInfo
    };

    return this.httpClient
      .post<TokenResponse>(this.apiUrl + 'auth/login', loginRequest)
      .pipe(
        map((response) => {
          if (response.success && response.data) {
            const decodedToken = this.jwtHelper.decodeToken(response.data.token);
            const userModel = new UserModel(decodedToken);
            this.currentUserSubject.next(userModel);
            this.setSession(response.data);
            this.startRefreshTokenTimer();

            // Şifre değiştirme zorunluluğu kontrolü
            if (response.requirePasswordChange) {
              localStorage.setItem('requirePasswordChange', 'true');
            } else {
              localStorage.removeItem('requirePasswordChange');

              // Şifre değiştirme zorunluluğu yoksa, kullanıcının rolüne göre yönlendirme yap
              if (this.hasRole('owner')) {
                // Owner rolüne sahip kullanıcıları license-dashboard sayfasına yönlendir
                response.redirectUrl = '/license-dashboard';
              } else if (this.hasRole('admin')) {
                // Admin rolüne sahip kullanıcıları todayentries sayfasına yönlendir
                response.redirectUrl = '/todayentries';
              } else if (this.hasRole('member')) {
                // Member rolüne sahip kullanıcıları my-qr sayfasına yönlendir
                response.redirectUrl = '/my-qr';
              }
            }
          }
          return response;
        }),
        catchError((error: HttpErrorResponse) => { // Add HttpErrorResponse type for better checking
          let errorMessage = 'Bir hata oluştu. Lütfen tekrar deneyin.'; // Default error message

          if (error.status === 429) {
            errorMessage = 'Çok fazla giriş denemesi yaptınız. Lütfen bir süre bekleyip tekrar deneyin.';
          } else if (error.status === 0) {
            // Network error (server down, CORS, etc.)
            errorMessage = 'Sunucuya bağlanılamadı. İnternet bağlantınızı kontrol edin veya daha sonra tekrar deneyin.';
          } else if (error.error && typeof error.error === 'object' && error.error.message) {
            // Backend error with a specific message
            errorMessage = error.error.message;
          } else if (typeof error.message === 'string') {
            // Other types of errors with a message property
            errorMessage = error.message;
          }

          // Always throw an object compatible with the component's error handling
          return throwError(() => ({
            success: false,
            message: errorMessage
          }));
        })

      );
  }

  register(registerModel: any): Observable<any> {
    const registerRequest = {
      registerDto: registerModel,
      deviceInfo: this.deviceInfo
    };

    return this.httpClient
      .post<any>(this.apiUrl + 'auth/register', registerRequest)
      .pipe(
        map((response) => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message);
        }),
        catchError((error: HttpErrorResponse) => {
          let errorMessage = 'Kayıt işlemi başarısız';

          if (error.status === 429) {
            errorMessage = 'Çok fazla kayıt denemesi yaptınız. Lütfen bir süre bekleyip tekrar deneyin.';
          } else if (error.error && typeof error.error === 'object' && error.error.message) {
            errorMessage = error.error.message;
          }

          return throwError(() => ({
            success: false,
            message: errorMessage
          }));
        })
      );
  }

  registerMember(registerModel: any): Observable<any> {
    const registerRequest = {
      registerDto: registerModel,
      deviceInfo: this.deviceInfo
    };

    return this.httpClient
      .post<any>(this.apiUrl + 'auth/register-member', registerRequest)
      .pipe(
        map((response) => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message);
        }),
        catchError((error: HttpErrorResponse) => {
          let errorMessage = 'Üye kaydı başarısız';

          if (error.status === 429) {
            errorMessage = 'Çok fazla kayıt denemesi yaptınız. Lütfen bir süre bekleyip tekrar deneyin.';
          } else if (error.error && typeof error.error === 'object' && error.error.message) {
            errorMessage = error.error.message;
          }

          return throwError(() => ({
            success: false,
            message: errorMessage
          }));
        })
      );
  }

  changePassword(currentPassword: string, newPassword: string): Observable<any> {
    return this.httpClient
      .post<any>(this.apiUrl + 'auth/change-password', {
        currentPassword: currentPassword,
        newPassword: newPassword
      })
      .pipe(
        map((response) => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message);
        }),
        catchError((error) => {
          return throwError(() => ({
            success: false,
            message: error.error?.message || 'Şifre değiştirme işlemi başarısız'
          }));
        })
      );
  }

  checkPasswordChangeRequired(): Observable<any> {
    return this.httpClient
      .get<any>(this.apiUrl + 'auth/check-password-change-required')
      .pipe(
        map((response) => {
          if (response.success) {
            return response;
          }
          throw new Error(response.message);
        }),
        catchError((error) => {
          return throwError(() => ({
            success: false,
            message: error.error?.message || 'Şifre değiştirme kontrolü başarısız'
          }));
        })
      );
  }

  refreshToken(): Observable<TokenResponse> {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      return throwError(() => 'No refresh token found');
    }

    if (this.refreshTokenInProgress) {
      return new Observable(observer => {
        this.refreshTokenSubject.subscribe({
          next: token => {
            if (token) {
              observer.next(token);
              observer.complete();
            }
          },
          error: error => observer.error(error)
        });
      });
    }

    this.refreshTokenInProgress = true;

    return this.httpClient
      .post<TokenResponse>(
        this.apiUrl + 'auth/refresh-token',
        {
          refreshToken: refreshToken,
          deviceInfo: this.deviceInfo
        }
      )
      .pipe(
        map((response) => {
          this.refreshTokenInProgress = false;
          if (response.success && response.data) {
            // Store old permissions before updating session
            const oldPermissions = this.currentUserValue?.role;

            // Update session with new token
            this.setSession(response.data);

            // Get new permissions from updated token
            const newPermissions = this.currentUserValue?.role;

            // Check if permissions have changed
            if (oldPermissions !== newPermissions) {
              // Handle permission changes
              this.handlePermissionChange(newPermissions);
            }

            this.refreshTokenSubject.next(response);
          } else {
            this.handleFailedRefresh(refreshToken);
          }
          return response;
        }),
        catchError((error) => {
          this.refreshTokenInProgress = false;
          this.refreshTokenSubject.error(error);
          this.handleFailedRefresh(refreshToken);
          return throwError(() => error);
        })
      );
  }

  private handlePermissionChange(newPermissions: string | string[] | undefined): void {
    // If user has no permissions or license has expired
    if (!newPermissions || (Array.isArray(newPermissions) && newPermissions.length === 0)) {
      this.clearSession();
      this.router.navigate(['/license-expired']);
      return;
    }

    // Get current route
    const currentUrl = this.router.url;

    // Check if user still has access to current route
    const route = this.router.config.find(r => currentUrl.startsWith('/' + (r.path || '')));
    if (route && route.data && route.data['expectedRole']) {
      const expectedRoles = route.data['expectedRole'];
      const hasAccess = Array.isArray(expectedRoles)
        ? expectedRoles.some(role => this.hasRole(role))
        : this.hasRole(expectedRoles);

      if (!hasAccess) {
        // If user no longer has access to current page, redirect based on new role
        if (typeof newPermissions === 'string') {
          switch (newPermissions) {
            case 'member':
              this.router.navigate(['/member-dashboard']);
              break;
            case 'admin':
              this.router.navigate(['/admin-dashboard']);
              break;
            case 'trainer':
              this.router.navigate(['/trainer-dashboard']);
              break;
            default:
              this.router.navigate(['/']);
          }
        } else if (Array.isArray(newPermissions) && newPermissions.length > 0) {
          // If multiple roles, navigate to the first applicable dashboard
          const firstRole = newPermissions[0];
          switch (firstRole) {
            case 'member':
              this.router.navigate(['/member-dashboard']);
              break;
            case 'admin':
              this.router.navigate(['/admin-dashboard']);
              break;
            case 'trainer':
              this.router.navigate(['/trainer-dashboard']);
              break;
            default:
              this.router.navigate(['/']);
          }
        }
      }
    }
  }

  private handleFailedRefresh(refreshToken: string) {
    this.httpClient
      .post(this.apiUrl + 'auth/revoke-token', { refreshToken })
      .subscribe({
        next: () => {
          this.clearSession();
          window.location.href = '/login';
        },
        error: () => {
          this.clearSession();
          window.location.href = '/login';
        }
      });
  }

  logout() {
    const refreshToken = localStorage.getItem('refreshToken');
    if (refreshToken) {
        const headers = new HttpHeaders().set('X-Refresh-Token', refreshToken);
        // Önce session'ı temizle ve yönlendirmeyi yap, sonra token'ı revoke et
        this.clearSession();
        this.router.navigate(['/login']).then(() => {
            this.httpClient
                .post(
                    this.apiUrl + 'auth/logout',
                    {},
                    { headers: headers }
                )
                .subscribe({
                    next: (response) => {
                        console.log('Logout successful:', response);
                    },
                    error: (error) => {
                        console.error('Logout error:', error);
                        // Hata olsa bile kullanıcı zaten çıkış yapmış durumda
                        // Bu sadece backend'de token'ı iptal etme işlemi
                    }
                });
        });
    } else {
        this.clearSession();
        this.router.navigate(['/login']);
    }
  }

  clearSession() {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    this.currentUserSubject.next(null);
    this.stopRefreshTokenTimer();
  }

  getUserDevices(): Observable<UserDevice[]> {
    const refreshToken = this.isBrowser ? localStorage.getItem('refreshToken') : null;
    const options = refreshToken ? { headers: { 'X-Refresh-Token': refreshToken } } : {};

    return this.httpClient.get<any>(this.apiUrl + 'auth/devices', options)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          }
          throw new Error(response.message);
        })
      );
  }

  revokeDevice(deviceId: number): Observable<any> {
    const refreshToken = this.isBrowser ? localStorage.getItem('refreshToken') : null;
    const options = refreshToken ? { headers: { 'X-Refresh-Token': refreshToken } } : {};

    return this.httpClient.post(this.apiUrl + 'auth/revoke-device', { deviceId }, options)
      .pipe(
        map(response => {
          if (response && typeof response === 'object' && 'success' in response) {
            if ((response as any).success) {
              return response;
            }
            throw new Error((response as any).message || 'Cihaz oturumu sonlandırılamadı');
          }
          return response;
        }),
        catchError((error: HttpErrorResponse) => {
          let errorMessage = 'Cihaz oturumu sonlandırılamadı';

          if (error.error && typeof error.error === 'object' && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          return throwError(() => ({ success: false, message: errorMessage }));
        })
      );
  }

  revokeAllDevices(): Observable<any> {
    const refreshToken = this.isBrowser ? localStorage.getItem('refreshToken') : null;
    const options = refreshToken ? { headers: { 'X-Refresh-Token': refreshToken } } : {};

    return this.httpClient.post(this.apiUrl + 'auth/revoke-all-devices', {}, options);
  }

  private setSession(tokenData: any) {
    if (tokenData && tokenData.token) {
      localStorage.setItem('token', tokenData.token);
      localStorage.setItem('refreshToken', tokenData.refreshToken);

      const decodedToken = this.jwtHelper.decodeToken(tokenData.token);
      this.currentUserSubject.next(new UserModel(decodedToken));
      this.startRefreshTokenTimer();
    }
  }

  private startRefreshTokenTimer() {
    const token = localStorage.getItem('token');
    if (!token) return;

    try {
      const decodedToken = this.jwtHelper.decodeToken(token);
      const expires = new Date(decodedToken.exp * 1000);
      const timeout = expires.getTime() - Date.now() - (60 * 1000); // 1 dakika önce yenile

      this.stopRefreshTokenTimer();

      this.refreshTokenTimeout = setTimeout(() => {
        this.refreshToken().subscribe();
      }, Math.max(0, timeout));
    } catch (error) {
      console.error('Timer setup error:', error);
      this.logout();
    }
  }

  private stopRefreshTokenTimer() {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
  }

  public get currentUserValue(): UserModel | null {
    return this.currentUserSubject.value;
  }

  // Token'dan kullanıcı bilgilerini güncelle
  updateUserFromToken(token: string): void {
    try {
      const decodedToken = this.jwtHelper.decodeToken(token);
      this.currentUserSubject.next(new UserModel(decodedToken));
      this.startRefreshTokenTimer();
    } catch (error) {
      console.error('Token decode error:', error);
      this.logout();
    }
  }

  isAuthenticated(): boolean {
    if (!this.isBrowser) return false;

    const token = localStorage.getItem('token');
    if (!token) return false;

    try {
      return !this.jwtHelper.isTokenExpired(token);
    } catch (error) {
      return false;
    }
  }

  hasRole(role: string | string[]): boolean {
    const user = this.currentUserValue;
    if (user && user.role) {
      if (Array.isArray(role)) {
        return role.some((r) => this.hasRole(r));
      }
      if (Array.isArray(user.role)) {
        return user.role.includes(role);
      }
      return user.role === role;
    }
    return false;
  }
}
