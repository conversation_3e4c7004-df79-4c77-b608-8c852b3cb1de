import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { BaseApiService } from './baseApiService';
import { AuthService } from './auth.service';
import { map } from 'rxjs/operators';

export interface Company {
  id: number;
  name: string;
}

@Injectable({
  providedIn: 'root'
})
export class CompanyContextService extends BaseApiService {
  private companyIdSubject: BehaviorSubject<number>;
  public companyId: Observable<number>;
  
  private companiesSubject: BehaviorSubject<Company[]>;
  public companies: Observable<Company[]>;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    super();
    
    // CompanyId'yi localStorage'dan veya varsayılan değer olarak -1 ile başlat
    const storedCompanyId = localStorage.getItem('companyId');
    this.companyIdSubject = new BehaviorSubject<number>(storedCompanyId ? parseInt(storedCompanyId) : -1);
    this.companyId = this.companyIdSubject.asObservable();
    
    // Şirketler listesini boş dizi ile başlat
    this.companiesSubject = new BehaviorSubject<Company[]>([]);
    this.companies = this.companiesSubject.asObservable();
    
    // Kullanıcı oturum açtığında şirketleri yükle
    this.authService.currentUser.subscribe(user => {
      if (user) {
        this.companiesSubject.next([]);
              } else {
        this.companiesSubject.next([]);
        this.companyIdSubject.next(-1);
        localStorage.removeItem('companyId');
      }
    });
  }

  // Mevcut CompanyId'yi döndüren getter
  public get currentCompanyId(): number {
    return this.companyIdSubject.value;
  }

  // Kullanıcının bağlı olduğu şirketleri getir
  loadUserCompanies(): void {
    this.http.get<any>(`${this.apiUrl}usercompany/getusercompanies`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data.map((company: any) => ({
              id: company.companyID,
              name: company.companyName
            }));
          }
          return [];
        })
      )
      .subscribe({
        next: (companies: Company[]) => {
          this.companiesSubject.next(companies);
          
          // Eğer aktif şirket yoksa ve şirket listesi doluysa, ilk şirketi seç
          if (this.currentCompanyId === -1 && companies.length > 0) {
            this.setActiveCompany(companies[0].id);
          }
        },
        error: (error) => {
          console.error('Şirketler yüklenirken hata oluştu:', error);
          this.companiesSubject.next([]);
        }
      });
  }

  // Aktif şirketi değiştir
  setActiveCompany(companyId: number): void {
    if (companyId === this.currentCompanyId) {
      return; // Aynı şirket seçiliyse işlem yapma
    }

    // Backend'e aktif şirket değişikliğini bildir
    this.http.post<any>(`${this.apiUrl}auth/change-company`, { companyId })
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            // Token'ı güncelle
            localStorage.setItem('token', response.data.token);
            localStorage.setItem('refreshToken', response.data.refreshToken);
            
            // CompanyId'yi güncelle
            localStorage.setItem('companyId', companyId.toString());
            this.companyIdSubject.next(companyId);
            
            // AuthService'i yeni token ile güncelle
            this.authService.updateUserFromToken(response.data.token);
            
            // Sayfayı yenile (opsiyonel, daha iyi bir yaklaşım olabilir)
            window.location.reload();
          }
        },
        error: (error) => {
          console.error('Şirket değiştirme hatası:', error);
        }
      });
  }

  // Şirket adını ID'ye göre getir
  getCompanyName(companyId: number): string {
    const companies = this.companiesSubject.value;
    const company = companies.find(c => c.id === companyId);
    return company ? company.name : 'Bilinmeyen Şirket';
  }
}
