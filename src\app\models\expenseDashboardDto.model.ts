import { ExpenseDto } from './expenseDto.model';

export interface ExpenseDashboardDto {
  /**
   * Günlük toplam gider (bugün)
   */
  totalDailyExpense: number;

  /**
   * Seçili ayın toplam gideri
   */
  totalMonthlyExpense: number;

  /**
   * Seçili yılın toplam gideri
   */
  totalYearlyExpense: number;

  /**
   * Seçili ayın gider detayları
   */
  monthlyExpenseDetails: ExpenseDto[];

  /**
   * Yıllık aylık gider özeti (grafik için)
   * Key: Ay (1-12), Value: O ayın toplam gideri
   */
  monthlyExpenseSummary: { [key: number]: number };

  /**
   * Seçili yıl
   */
  selectedYear: number;

  /**
   * Seçili ay
   */
  selectedMonth: number;

  /**
   * <PERSON><PERSON> alınma tarihi (cache kontrolü için)
   */
  dataRetrievedAt: string;
}
