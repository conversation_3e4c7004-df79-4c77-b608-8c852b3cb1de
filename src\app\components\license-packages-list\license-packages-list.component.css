/* License Packages List Component Styles */

/* Container */
.modern-table-container {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  min-height: 200px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-secondary);
}

.empty-state-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state-title {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.empty-state-text {
  color: var(--text-secondary);
  margin-bottom: 0;
  font-size: 0.95rem;
}

/* Table Wrapper */
.modern-table-wrapper {
  overflow: hidden;
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-primary);
  font-size: 0.9rem;
}

.modern-table thead {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

.modern-table thead th {
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: white;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
  position: relative;
}

.modern-table thead th:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
}

.modern-table tbody tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modern-table tbody tr:last-child {
  border-bottom: none;
}

.modern-table tbody td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border: none;
  color: var(--text-primary);
}

/* Package Info */
.package-info {
  display: flex;
  flex-direction: column;
}

.package-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

/* Description */
.description-text {
  color: var(--text-secondary);
  font-size: 0.9rem;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Role Badge */
.role-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-admin {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.role-owner {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.role-member {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
  border: 1px solid rgba(46, 204, 113, 0.2);
}

/* Duration */
.duration-text {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Price */
.price-amount {
  color: var(--success);
  font-weight: 600;
  font-size: 1rem;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
  border: 1px solid rgba(46, 204, 113, 0.2);
}

.status-inactive {
  background-color: rgba(149, 165, 166, 0.1);
  color: #95a5a6;
  border: 1px solid rgba(149, 165, 166, 0.2);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.edit-btn {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.edit-btn:hover {
  background-color: rgba(52, 152, 219, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.delete-btn {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.delete-btn:hover {
  background-color: rgba(231, 76, 60, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-table {
    font-size: 0.8rem;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.75rem 0.5rem;
  }

  .description-text {
    max-width: 150px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .modern-table {
    font-size: 0.75rem;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.5rem 0.25rem;
  }

  .description-text {
    max-width: 100px;
  }

  .package-title {
    font-size: 0.85rem;
  }

  .role-badge,
  .status-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  .modern-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}