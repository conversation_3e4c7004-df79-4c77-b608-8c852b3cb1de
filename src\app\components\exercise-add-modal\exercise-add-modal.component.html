<div class="modal-header">
  <h4 class="modal-title">
    <fa-icon [icon]="faInfoCircle" class="title-icon"></fa-icon>
    {{mode === 'add' ? '<PERSON><PERSON>' : '<PERSON><PERSON><PERSON><PERSON>le'}}
  </h4>
  <button type="button" class="btn-close" (click)="onCancel()" [disabled]="isSubmitting">
    <fa-icon [icon]="faTimes"></fa-icon>
  </button>
</div>

<div class="modal-body">
  <form [formGroup]="exerciseForm" (ngSubmit)="onSubmit()">
    <div class="row">
      <!-- Kategori -->
      <div class="col-md-6 mb-3">
        <label for="exerciseCategoryID" class="form-label required">Kategori</label>
        <select 
          id="exerciseCategoryID"
          class="form-select"
          formControlName="exerciseCategoryID"
          [class.is-invalid]="isFieldInvalid('exerciseCategoryID')">
          <option value=""><PERSON><PERSON><PERSON></option>
          <option *ngFor="let category of categories" [value]="category.exerciseCategoryID">
            {{category.categoryName}}
          </option>
        </select>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('exerciseCategoryID')">
          {{getFieldError('exerciseCategoryID')}}
        </div>
      </div>

      <!-- Zorluk Seviyesi -->
      <div class="col-md-6 mb-3">
        <label for="difficultyLevel" class="form-label required">Zorluk Seviyesi</label>
        <select 
          id="difficultyLevel"
          class="form-select"
          formControlName="difficultyLevel"
          [class.is-invalid]="isFieldInvalid('difficultyLevel')">
          <option [value]="1">Başlangıç</option>
          <option [value]="2">Orta</option>
          <option [value]="3">İleri</option>
        </select>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('difficultyLevel')">
          {{getFieldError('difficultyLevel')}}
        </div>
      </div>
    </div>

    <!-- Egzersiz Adı -->
    <div class="mb-3">
      <label for="exerciseName" class="form-label required">Egzersiz Adı</label>
      <input 
        type="text" 
        id="exerciseName"
        class="form-control"
        formControlName="exerciseName"
        placeholder="Örn: Dumbbell Chest Press"
        [class.is-invalid]="isFieldInvalid('exerciseName')">
      <div class="invalid-feedback" *ngIf="isFieldInvalid('exerciseName')">
        {{getFieldError('exerciseName')}}
      </div>
    </div>

    <!-- Açıklama -->
    <div class="mb-3">
      <label for="description" class="form-label">Açıklama <span class="text-muted">(Opsiyonel)</span></label>
      <textarea 
        id="description"
        class="form-control"
        formControlName="description"
        rows="3"
        placeholder="Egzersizin kısa açıklaması..."
        [class.is-invalid]="isFieldInvalid('description')"></textarea>
      <div class="invalid-feedback" *ngIf="isFieldInvalid('description')">
        {{getFieldError('description')}}
      </div>
      <div class="form-text">
        Maksimum 1000 karakter
      </div>
    </div>

    <div class="row">
      <!-- Kas Grupları -->
      <div class="col-md-6 mb-3">
        <label for="muscleGroups" class="form-label">Kas Grupları <span class="text-muted">(Opsiyonel)</span></label>
        <input 
          type="text" 
          id="muscleGroups"
          class="form-control"
          formControlName="muscleGroups"
          placeholder="Örn: Göğüs, Triceps, Ön Omuz"
          [class.is-invalid]="isFieldInvalid('muscleGroups')">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('muscleGroups')">
          {{getFieldError('muscleGroups')}}
        </div>
        <div class="form-text">
          Virgülle ayırarak yazın
        </div>
      </div>

      <!-- Ekipman -->
      <div class="col-md-6 mb-3">
        <label for="equipment" class="form-label">Ekipman <span class="text-muted">(Opsiyonel)</span></label>
        <input 
          type="text" 
          id="equipment"
          class="form-control"
          formControlName="equipment"
          placeholder="Örn: Dumbbell, Bench"
          [class.is-invalid]="isFieldInvalid('equipment')">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('equipment')">
          {{getFieldError('equipment')}}
        </div>
        <div class="form-text">
          Gerekli ekipmanları yazın
        </div>
      </div>
    </div>

    <!-- Talimatlar -->
    <div class="mb-3">
      <label for="instructions" class="form-label">Talimatlar <span class="text-muted">(Opsiyonel)</span></label>
      <textarea 
        id="instructions"
        class="form-control"
        formControlName="instructions"
        rows="4"
        placeholder="Egzersizin nasıl yapılacağına dair detaylı talimatlar..."
        [class.is-invalid]="isFieldInvalid('instructions')"></textarea>
      <div class="invalid-feedback" *ngIf="isFieldInvalid('instructions')">
        {{getFieldError('instructions')}}
      </div>
      <div class="form-text">
        Maksimum 2000 karakter
      </div>
    </div>
  </form>
</div>

<div class="modal-footer">
  <button 
    type="button" 
    class="btn btn-secondary" 
    (click)="onCancel()"
    [disabled]="isSubmitting">
    İptal
  </button>
  <button 
    type="button" 
    class="btn btn-primary" 
    (click)="onSubmit()"
    [disabled]="exerciseForm.invalid || isSubmitting">
    <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
    <fa-icon [icon]="faSave" class="me-1"></fa-icon>
    {{isSubmitting ? (mode === 'add' ? 'Kaydediliyor...' : 'Güncelleniyor...') : (mode === 'add' ? 'Kaydet' : 'Güncelle')}}
  </button>
</div>
