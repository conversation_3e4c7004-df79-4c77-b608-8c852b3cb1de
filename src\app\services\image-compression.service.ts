import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ImageCompressionService {

  constructor() { }

  /**
   * <PERSON><PERSON><PERSON>ı<PERSON>tırır ve belirtilen boyutlara getirir
   * @param file - Sıkıştır<PERSON>lacak dosya
   * @param maxWidth - Ma<PERSON><PERSON><PERSON> g<PERSON> (varsayılan: 400)
   * @param maxHeight - Maks<PERSON>um yü<PERSON> (varsayılan: 400)
   * @param quality - Kalite (0-1 arası, varsayılan: 0.9)
   * @returns Promise<File> - Sıkıştırılmış dosya
   */
  async compressImage(
    file: File,
    maxWidth: number = 400,
    maxHeight: number = 400,
    quality: number = 0.9
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Orijinal boyutları al
        const { width, height } = img;

        // Yeni boyutları hesapla (aspect ratio korunarak)
        const { newWidth, newHeight } = this.calculateNewDimensions(
          width,
          height,
          maxWidth,
          maxHeight
        );

        // Canvas boyutlarını ayarla
        canvas.width = newWidth;
        canvas.height = newHeight;

        // Resmi canvas'a çiz
        ctx?.drawImage(img, 0, 0, newWidth, newHeight);

        // Canvas'ı blob'a çevir
        canvas.toBlob(
          (blob) => {
            if (blob) {
              // Blob'u File'a çevir
              const compressedFile = new File(
                [blob],
                file.name,
                {
                  type: file.type,
                  lastModified: Date.now()
                }
              );
              resolve(compressedFile);
            } else {
              reject(new Error('Canvas to blob conversion failed'));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('Image loading failed'));
      };

      // Dosyayı okuyup image'a yükle
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.onerror = () => {
        reject(new Error('File reading failed'));
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * Aspect ratio korunarak yeni boyutları hesaplar
   */
  private calculateNewDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { newWidth: number; newHeight: number } {
    let newWidth = originalWidth;
    let newHeight = originalHeight;

    // Genişlik kontrolü
    if (newWidth > maxWidth) {
      newHeight = (newHeight * maxWidth) / newWidth;
      newWidth = maxWidth;
    }

    // Yükseklik kontrolü
    if (newHeight > maxHeight) {
      newWidth = (newWidth * maxHeight) / newHeight;
      newHeight = maxHeight;
    }

    return {
      newWidth: Math.round(newWidth),
      newHeight: Math.round(newHeight)
    };
  }

  /**
   * Dosya boyutunu MB cinsinden döndürür
   */
  getFileSizeInMB(file: File): number {
    return file.size / (1024 * 1024);
  }

  /**
   * Sıkıştırma oranını hesaplar
   */
  getCompressionRatio(originalFile: File, compressedFile: File): number {
    return Math.round(((originalFile.size - compressedFile.size) / originalFile.size) * 100);
  }
}
