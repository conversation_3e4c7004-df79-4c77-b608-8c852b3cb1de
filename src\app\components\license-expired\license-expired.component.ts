import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-license-expired',
  templateUrl: './license-expired.component.html',
  styleUrls: ['./license-expired.component.css'],
  standalone: false
})
export class LicenseExpiredComponent {
  constructor(
    private router: Router,
    private authService: AuthService
  ) {}

  goToLogin() {
    this.authService.logout();
  }

  goToLicensePurchase() {
    this.router.navigate(['/license-purchase']);
  }
}
