import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';
import { 
  WorkoutProgramTemplate, 
  WorkoutProgramTemplateList, 
  WorkoutProgramTemplateAdd, 
  WorkoutProgramTemplateUpdate 
} from '../models/workout-program.models';

@Injectable({
  providedIn: 'root'
})
export class WorkoutProgramService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Tüm antrenman programı şablonlarını getirir
   */
  getAll(): Observable<ListResponseModel<WorkoutProgramTemplateList>> {
    return this.httpClient.get<ListResponseModel<WorkoutProgramTemplateList>>(
      `${this.apiUrl}workoutprogram/getall`
    );
  }

  /**
   * ID'ye göre antrenman programı şablonu detayını getirir
   */
  getById(templateId: number): Observable<SingleResponseModel<WorkoutProgramTemplate>> {
    return this.httpClient.get<SingleResponseModel<WorkoutProgramTemplate>>(
      `${this.apiUrl}workoutprogram/getbyid?id=${templateId}`
    );
  }

  /**
   * Yeni antrenman programı şablonu ekler
   */
  add(template: WorkoutProgramTemplateAdd): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}workoutprogram/add`,
      template
    );
  }

  /**
   * Antrenman programı şablonunu günceller
   */
  update(template: WorkoutProgramTemplateUpdate): Observable<ResponseModel> {
    return this.httpClient.put<ResponseModel>(
      `${this.apiUrl}workoutprogram/update`,
      template
    );
  }

  /**
   * Antrenman programı şablonunu siler
   */
  delete(templateId: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}workoutprogram/delete?id=${templateId}`
    );
  }
}
