/* src/app/components/help-dialog/help-dialog.component.css */

/* Help Dialog Container */
.help-dialog {
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  background-color: var(--card-bg-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px var(--shadow-color);
}

/* Header Styles */
.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  border-bottom: none;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
}

.help-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.header-text h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
}

.header-subtitle {
  margin: 0;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Body Styles */
.modern-card-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.help-content {
  padding: 1.5rem;
}

/* Content Sections */
.content-section {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.content-section:last-child {
  margin-bottom: 0;
}

.content-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-weight: 600;
  font-size: 1.1rem;
}

.content-header i {
  width: 24px;
  text-align: center;
}

/* Content Type Styles */
.content-text {
  padding: 1rem;
  background-color: var(--background-color);
  border-left: 4px solid var(--primary-color);
}

.content-text p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-color);
}

.content-list {
  padding: 1rem;
  background-color: var(--background-color);
  border-left: 4px solid var(--accent-color);
}

.content-steps {
  padding: 1rem;
  background-color: var(--background-color);
  border-left: 4px solid var(--secondary-color);
}

.content-warning {
  padding: 1rem;
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid #dc3545;
  border-radius: 8px;
}

.content-tip {
  padding: 1rem;
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
  border-radius: 8px;
}

/* List Styles */
.list-content {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-content li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.list-content li:last-child {
  margin-bottom: 0;
}

.list-content li i {
  color: var(--accent-color);
  margin-top: 0.125rem;
  flex-shrink: 0;
}

/* Steps Styles */
.steps-content {
  padding: 0;
  margin: 0;
  list-style: none;
  counter-reset: step-counter;
}

.steps-content li {
  position: relative;
  padding-left: 3rem;
  margin-bottom: 1rem;
  line-height: 1.6;
  counter-increment: step-counter;
}

.steps-content li:last-child {
  margin-bottom: 0;
}

.steps-content li::before {
  content: counter(step-counter);
  position: absolute;
  left: 0;
  top: 0;
  width: 2rem;
  height: 2rem;
  background: var(--secondary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Warning and Tip Content */
.warning-content p,
.tip-content p {
  margin: 0;
  line-height: 1.6;
}

.content-warning .content-header {
  color: #dc3545;
}

.content-tip .content-header {
  color: #ffc107;
}

/* Footer Styles */
.modern-card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1.5rem;
  background-color: var(--background-color);
  border-top: 1px solid var(--border-color);
}

/* Dark Mode Support */
[data-theme="dark"] .help-dialog {
  background-color: var(--card-bg-color);
}

[data-theme="dark"] .content-text,
[data-theme="dark"] .content-list,
[data-theme="dark"] .content-steps {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .content-warning {
  background-color: rgba(220, 53, 69, 0.15);
}

[data-theme="dark"] .content-tip {
  background-color: rgba(255, 193, 7, 0.15);
}

[data-theme="dark"] .modern-card-footer {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
  .help-dialog {
    max-width: 95vw;
    margin: 0.5rem;
    max-height: 90vh;
  }

  .modern-card-header {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-left {
    gap: 0.75rem;
    width: 100%;
  }

  .help-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .header-text h2 {
    font-size: 1.25rem;
  }

  .header-subtitle {
    font-size: 0.875rem;
  }

  .close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 32px;
    height: 32px;
  }

  .help-content {
    padding: 1rem;
  }

  .content-section {
    margin-bottom: 1rem;
  }

  .content-header {
    font-size: 1rem;
    gap: 0.5rem;
  }

  .steps-content li {
    padding-left: 2.5rem;
    font-size: 0.9rem;
  }

  .steps-content li::before {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }

  .modern-card-footer {
    padding: 1rem;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .help-dialog {
    max-width: 100vw;
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
  }

  .modern-card-header {
    padding: 0.75rem;
  }

  .help-content {
    padding: 0.75rem;
  }

  .content-header {
    font-size: 0.95rem;
  }

  .steps-content li {
    padding-left: 2rem;
    font-size: 0.85rem;
  }

  .list-content li {
    font-size: 0.85rem;
  }
}

/* Animation */
.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
