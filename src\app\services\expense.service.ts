import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';
import { Expense } from '../models/expense.model';
import { ExpenseDto } from '../models/expenseDto.model';
import { ExpenseDashboardDto } from '../models/expenseDashboardDto.model';
import { ExpensePagingParameters } from '../models/expensePagingParameters';
import { PaginatedResult } from '../models/pagination';
import { BaseApiService } from './baseApiService';

@Injectable({
  providedIn: 'root'
})
export class ExpenseService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  getAll(): Observable<ListResponseModel<Expense>> {
    return this.httpClient.get<ListResponseModel<Expense>>(`${this.apiUrl}expenses/getall`);
  }

  getById(id: number): Observable<SingleResponseModel<Expense>> {
    return this.httpClient.get<SingleResponseModel<Expense>>(`${this.apiUrl}expenses/getbyid?id=${id}`);
  }

  add(expense: Expense): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(`${this.apiUrl}expenses/add`, expense);
  }

  update(expense: Expense): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(`${this.apiUrl}expenses/update`, expense);
  }

  delete(id: number): Observable<ResponseModel> {
    // Backend controller'da HttpDelete kullanıldığı için method: 'delete' olmalı
    // Ancak mevcut projede delete için de post kullanılmış olabilir, kontrol etmek lazım.
    // Şimdilik backend controller'daki [HttpDelete] attribute'una uygun olarak delete kullanalım.
    // Eğer backend'de post kullanılıyorsa, burası post olmalı ve id body içinde gönderilmeli.
    return this.httpClient.delete<ResponseModel>(`${this.apiUrl}expenses/delete?id=${id}`);
  }

  getExpensesByDateRange(startDate: Date, endDate: Date): Observable<ListResponseModel<ExpenseDto>> {
    let params = new HttpParams()
      .set('startDate', startDate.toISOString()) // Tarihleri ISO formatında göndermek genellikle daha güvenlidir
      .set('endDate', endDate.toISOString());
    return this.httpClient.get<ListResponseModel<ExpenseDto>>(`${this.apiUrl}expenses/getbydaterange`, { params });
  }



  // Dashboard için optimize edilmiş tek API çağrısı - 5 API isteği yerine 1 API isteği
  getDashboardData(year: number, month: number): Observable<SingleResponseModel<ExpenseDashboardDto>> {
    let params = new HttpParams()
      .set('year', year.toString())
      .set('month', month.toString());
    return this.httpClient.get<SingleResponseModel<ExpenseDashboardDto>>(`${this.apiUrl}expenses/getdashboarddata`, { params });
  }

  // PaymentHistory benzeri API'ler
  getMonthlyExpense(year: number = 0): Observable<SingleResponseModel<any>> {
    let params = new HttpParams();
    if (year > 0) {
      params = params.set('year', year.toString());
    }
    return this.httpClient.get<SingleResponseModel<any>>(
      `${this.apiUrl}expenses/getmonthlyexpense`,
      { params }
    );
  }

  getExpenseTotals(parameters: any): Observable<SingleResponseModel<any>> {
    let params = new HttpParams();

    if (parameters.startDate) {
      params = params.set('startDate', parameters.startDate.toISOString());
    }
    if (parameters.endDate) {
      params = params.set('endDate', parameters.endDate.toISOString());
    }
    if (parameters.expenseType) {
      params = params.set('expenseType', parameters.expenseType);
    }
    if (parameters.minAmount) {
      params = params.set('minAmount', parameters.minAmount.toString());
    }
    if (parameters.maxAmount) {
      params = params.set('maxAmount', parameters.maxAmount.toString());
    }

    return this.httpClient.get<SingleResponseModel<any>>(
      `${this.apiUrl}expenses/getexpensetotals`,
      { params }
    );
  }

  // Pagination ve gelişmiş filtreleme için yeni metotlar
  getExpensesPaginated(parameters: ExpensePagingParameters): Observable<SingleResponseModel<PaginatedResult<ExpenseDto>>> {
    let params = new HttpParams()
      .set('pageNumber', parameters.pageNumber.toString())
      .set('pageSize', parameters.pageSize.toString());

    if (parameters.searchText) {
      params = params.set('searchText', parameters.searchText);
    }
    if (parameters.sortBy) {
      params = params.set('sortBy', parameters.sortBy);
    }
    if (parameters.sortDirection) {
      params = params.set('sortDirection', parameters.sortDirection);
    }
    if (parameters.startDate) {
      const startDateStr = parameters.startDate instanceof Date ? parameters.startDate.toISOString() : String(parameters.startDate);
      params = params.set('startDate', startDateStr);
    }
    if (parameters.endDate) {
      const endDateStr = parameters.endDate instanceof Date ? parameters.endDate.toISOString() : String(parameters.endDate);
      params = params.set('endDate', endDateStr);
    }
    if (parameters.expenseType) {
      params = params.set('expenseType', parameters.expenseType);
    }
    if (parameters.minAmount !== null && parameters.minAmount !== undefined) {
      params = params.set('minAmount', parameters.minAmount.toString());
    }
    if (parameters.maxAmount !== null && parameters.maxAmount !== undefined) {
      params = params.set('maxAmount', parameters.maxAmount.toString());
    }
    if (parameters.isActive !== null && parameters.isActive !== undefined) {
      params = params.set('isActive', parameters.isActive.toString());
    }

    return this.httpClient.get<SingleResponseModel<PaginatedResult<ExpenseDto>>>(`${this.apiUrl}expenses/getallpaginated`, { params });
  }

  // Export için tüm filtrelenmiş verileri getir
  getAllExpensesFiltered(parameters: ExpensePagingParameters): Observable<ListResponseModel<ExpenseDto>> {
    let params = new HttpParams();

    if (parameters.searchText) {
      params = params.set('searchText', parameters.searchText);
    }
    if (parameters.sortBy) {
      params = params.set('sortBy', parameters.sortBy);
    }
    if (parameters.sortDirection) {
      params = params.set('sortDirection', parameters.sortDirection);
    }
    if (parameters.startDate) {
      const startDateStr = parameters.startDate instanceof Date ? parameters.startDate.toISOString() : String(parameters.startDate);
      params = params.set('startDate', startDateStr);
    }
    if (parameters.endDate) {
      const endDateStr = parameters.endDate instanceof Date ? parameters.endDate.toISOString() : String(parameters.endDate);
      params = params.set('endDate', endDateStr);
    }
    if (parameters.expenseType) {
      params = params.set('expenseType', parameters.expenseType);
    }
    if (parameters.minAmount !== null && parameters.minAmount !== undefined) {
      params = params.set('minAmount', parameters.minAmount.toString());
    }
    if (parameters.maxAmount !== null && parameters.maxAmount !== undefined) {
      params = params.set('maxAmount', parameters.maxAmount.toString());
    }
    if (parameters.isActive !== null && parameters.isActive !== undefined) {
      params = params.set('isActive', parameters.isActive.toString());
    }

    return this.httpClient.get<ListResponseModel<ExpenseDto>>(`${this.apiUrl}expenses/getallfiltered`, { params });
  }
}