import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MemberService } from '../../../services/member.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';

@Component({
    selector: 'app-member-add',
    templateUrl: './member-add.component.html',
    styleUrls: ['./member-add.component.css'],
    standalone: false
})
export class MemberAddComponent implements OnInit {
  memberAddForm: FormGroup;
  isProcessCompleted: boolean = false;
  isSubmitting: boolean = false;

  // Required fields for form progress calculation
  requiredFields = ['name', 'phoneNumber', 'gender', 'email'];
  totalFields = 6; // Total number of fields in the form

  constructor(
    private formBuilder: FormBuilder,
    private memberService: MemberService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createMemberAddForm();
  }

  createMemberAddForm() {
    this.memberAddForm = this.formBuilder.group({
      name: ['', Validators.required],
      adress: [''],
      gender: ['', Validators.required],
      phoneNumber: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      birthDate: [''],
    });
  }

  add() {
    if (this.memberAddForm.invalid) {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.isSubmitting = false;

      // Tüm form kontrollerini dokunulmuş olarak işaretle
      Object.keys(this.memberAddForm.controls).forEach(key => {
        const control = this.memberAddForm.get(key);
        if (control) {
          control.markAsTouched();
          control.markAsDirty();
        }
      });

      // Eksik alanları vurgula ve titret
      this.highlightMissingFields();

      return;
    }

    this.isSubmitting = true;
    let memberModel = Object.assign({}, this.memberAddForm.value);

    if (memberModel.name) {
      memberModel.name = memberModel.name.toUpperCase();
    }

    if (memberModel.birthDate === '') {
      memberModel.birthDate = null;
    }
    if (memberModel.adress === '') {
      memberModel.adress = null;
    }
    // E-posta artık zorunlu olduğu için bu kontrole gerek yok
    // if (memberModel.email === '') {
    //   memberModel.email = null;
    // }

    this.memberService.add(memberModel).subscribe(
      (response) => {
        this.toastrService.success(response.message, 'Başarılı');
        this.isProcessCompleted = true;
        this.isSubmitting = false;
        setTimeout(() => {
          this.resetForm();
        }, 2000);
      },
      (responseError) => {
        if (responseError.error && responseError.error.Errors) {
          for (let i = 0; i < responseError.error.Errors.length; i++) {
            this.toastrService.error(
              responseError.error.Errors[i].ErrorMessage,
              'Hata'
            );
          }
        } else {
          this.toastrService.error('Beklenmedik bir hata oluştu', 'Hata');
        }
        this.isSubmitting = false;
      }
    );
  }

  resetForm() {
    this.memberAddForm.reset({
      name: '',
      adress: '',
      gender: '',
      phoneNumber: '',
      email: '',
      birthDate: '',
    });
  }

  // Eksik alanları vurgulama metodu
  highlightMissingFields() {
    // Zorunlu alanları kontrol et ve eksik olanları belirle
    const missingFields: string[] = [];
    const requiredControls: (HTMLElement | null)[] = [];

    // Ad Soyad kontrolü
    const nameControl = this.memberAddForm.get('name');
    if (nameControl?.invalid) {
      missingFields.push('Ad Soyad');
      requiredControls.push(document.getElementById('name'));
    }

    // Telefon Numarası kontrolü
    const phoneControl = this.memberAddForm.get('phoneNumber');
    if (phoneControl?.invalid) {
      missingFields.push('Telefon Numarası');
      requiredControls.push(document.getElementById('phoneNumber'));
    }

    // Cinsiyet kontrolü
    const genderControl = this.memberAddForm.get('gender');
    if (genderControl?.invalid) {
      missingFields.push('Cinsiyet');
      requiredControls.push(document.getElementById('gender'));
    }

    // E-posta kontrolü
    const emailControl = this.memberAddForm.get('email');
    if (emailControl?.invalid) {
      missingFields.push('E-posta');
      requiredControls.push(document.getElementById('email'));
    }

    // Eksik alan sayısına göre mesaj göster
    if (missingFields.length > 0) {
      const fieldList = missingFields.join(', ');
      this.toastrService.warning(`Lütfen şu alanları doldurun: ${fieldList}`, 'Eksik Alanlar');

      // Eksik alanları görsel olarak vurgula ve titret
      setTimeout(() => {
        requiredControls.forEach((element, index) => {
          if (element) {
            // Titreşim animasyonu ekle
            element.classList.add('shake-animation');

            // İlk eksik alana kaydır
            if (index === 0) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              element.focus(); // İlk alana odaklan
            }

            // Animasyonu bir süre sonra kaldır
            setTimeout(() => {
              element.classList.remove('shake-animation');
            }, 600);
          }
        });
      }, 100);
    }
  }

  /**
   * Calculate the form completion progress as a percentage
   * @returns number between 0-100
   */
  getFormProgress(): number {
    if (!this.memberAddForm) return 0;

    // Count filled required fields
    let filledRequiredFields = 0;
    for (const field of this.requiredFields) {
      if (this.memberAddForm.get(field)?.valid) {
        filledRequiredFields++;
      }
    }

    // Count filled optional fields
    let filledOptionalFields = 0;
    const optionalFields = Object.keys(this.memberAddForm.controls)
      .filter(key => !this.requiredFields.includes(key));

    for (const field of optionalFields) {
      if (this.memberAddForm.get(field)?.value) {
        filledOptionalFields++;
      }
    }

    // Calculate progress - required fields have more weight
    const requiredWeight = 0.7; // 70% of progress is from required fields
    const optionalWeight = 0.3; // 30% of progress is from optional fields

    const requiredProgress = (filledRequiredFields / this.requiredFields.length) * requiredWeight * 100;
    const optionalProgress = (filledOptionalFields / optionalFields.length) * optionalWeight * 100;

    return Math.round(requiredProgress + optionalProgress);
  }
}
