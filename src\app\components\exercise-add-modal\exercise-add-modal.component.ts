import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { faTimes, faSave, faInfoCircle } from '@fortawesome/free-solid-svg-icons';

import { ExerciseCategory } from '../../services/exercise-category.service';
import { CompanyExerciseService, CompanyExerciseAdd, CompanyExerciseUpdate, CombinedExercise } from '../../services/company-exercise.service';

export interface ExerciseAddModalData {
  categories: ExerciseCategory[];
  mode: 'add' | 'edit';
  exercise?: CombinedExercise;
}

@Component({
  selector: 'app-exercise-add-modal',
  templateUrl: './exercise-add-modal.component.html',
  styleUrls: ['./exercise-add-modal.component.css'],
  standalone: false
})
export class ExerciseAddModalComponent implements OnInit {
  // Icons
  faTimes = faTimes;
  faSave = faSave;
  faInfoCircle = faInfoCircle;

  exerciseForm: FormGroup;
  isSubmitting = false;
  categories: ExerciseCategory[] = [];
  mode: 'add' | 'edit' = 'add';
  exercise?: CombinedExercise;

  constructor(
    private fb: FormBuilder,
    private companyExerciseService: CompanyExerciseService,
    private toastrService: ToastrService,
    public dialogRef: MatDialogRef<ExerciseAddModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ExerciseAddModalData
  ) {
    this.categories = data.categories;
    this.mode = data.mode;
    this.exercise = data.exercise;
    this.exerciseForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.mode === 'edit' && this.exercise) {
      this.populateForm();
    }
  }

  populateForm(): void {
    if (this.exercise) {
      this.exerciseForm.patchValue({
        exerciseCategoryID: this.exercise.exerciseCategoryID,
        exerciseName: this.exercise.exerciseName,
        description: this.exercise.description || '',
        instructions: this.exercise.instructions || '',
        muscleGroups: this.exercise.muscleGroups || '',
        equipment: this.exercise.equipment || '',
        difficultyLevel: this.exercise.difficultyLevel || 1
      });
    }
  }

  createForm(): FormGroup {
    return this.fb.group({
      exerciseCategoryID: ['', [Validators.required]],
      exerciseName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(200)]],
      description: ['', [Validators.maxLength(1000)]],
      instructions: ['', [Validators.maxLength(2000)]],
      muscleGroups: ['', [Validators.maxLength(500)]],
      equipment: ['', [Validators.maxLength(200)]],
      difficultyLevel: [1, [Validators.required, Validators.min(1), Validators.max(3)]]
    });
  }

  onSubmit(): void {
    if (this.exerciseForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      if (this.mode === 'add') {
        this.addExercise();
      } else {
        this.updateExercise();
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private addExercise(): void {
    const exerciseData: CompanyExerciseAdd = {
      exerciseCategoryID: this.exerciseForm.value.exerciseCategoryID,
      exerciseName: this.exerciseForm.value.exerciseName.trim(),
      description: this.exerciseForm.value.description?.trim() || undefined,
      instructions: this.exerciseForm.value.instructions?.trim() || undefined,
      muscleGroups: this.exerciseForm.value.muscleGroups?.trim() || undefined,
      equipment: this.exerciseForm.value.equipment?.trim() || undefined,
      difficultyLevel: this.exerciseForm.value.difficultyLevel
    };

    this.companyExerciseService.add(exerciseData).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success('Egzersiz başarıyla eklendi', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message || 'Egzersiz eklenirken hata oluştu', 'Hata');
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error adding exercise:', error);
        this.toastrService.error('Egzersiz eklenirken hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  private updateExercise(): void {
    if (!this.exercise) {
      this.isSubmitting = false;
      return;
    }

    const exerciseData: CompanyExerciseUpdate = {
      companyExerciseID: this.exercise.exerciseID,
      exerciseCategoryID: this.exerciseForm.value.exerciseCategoryID,
      exerciseName: this.exerciseForm.value.exerciseName.trim(),
      description: this.exerciseForm.value.description?.trim() || undefined,
      instructions: this.exerciseForm.value.instructions?.trim() || undefined,
      muscleGroups: this.exerciseForm.value.muscleGroups?.trim() || undefined,
      equipment: this.exerciseForm.value.equipment?.trim() || undefined,
      difficultyLevel: this.exerciseForm.value.difficultyLevel,
      isActive: true
    };

    this.companyExerciseService.update(exerciseData).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success('Egzersiz başarıyla güncellendi', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message || 'Egzersiz güncellenirken hata oluştu', 'Hata');
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error updating exercise:', error);
        this.toastrService.error('Egzersiz güncellenirken hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.exerciseForm.controls).forEach(key => {
      const control = this.exerciseForm.get(key);
      control?.markAsTouched();
    });
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.exerciseForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.exerciseForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return this.getFieldDisplayName(fieldName) + ' zorunludur';
      }
      if (field.errors['minlength']) {
        return this.getFieldDisplayName(fieldName) + ' en az ' + field.errors['minlength'].requiredLength + ' karakter olmalıdır';
      }
      if (field.errors['maxlength']) {
        return this.getFieldDisplayName(fieldName) + ' en fazla ' + field.errors['maxlength'].requiredLength + ' karakter olmalıdır';
      }
      if (field.errors['min']) {
        return this.getFieldDisplayName(fieldName) + ' en az ' + field.errors['min'].min + ' olmalıdır';
      }
      if (field.errors['max']) {
        return this.getFieldDisplayName(fieldName) + ' en fazla ' + field.errors['max'].max + ' olmalıdır';
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'exerciseCategoryID': 'Kategori',
      'exerciseName': 'Egzersiz Adı',
      'description': 'Açıklama',
      'instructions': 'Talimatlar',
      'muscleGroups': 'Kas Grupları',
      'equipment': 'Ekipman',
      'difficultyLevel': 'Zorluk Seviyesi'
    };
    return fieldNames[fieldName] || fieldName;
  }

  getDifficultyLevelText(level: number): string {
    switch (level) {
      case 1: return 'Başlangıç';
      case 2: return 'Orta';
      case 3: return 'İleri';
      default: return '';
    }
  }
}
