/* Modern Components CSS - Global Styles for GymProject */

/* Common Variables */
:root {
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-pill: 50rem;
  
  /* Shadows */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* Transitions */
  --transition-speed: 0.3s;
  --transition-timing: ease;
}

/* Light Mode Variables (Default) */
:root {
  /* Primary Colors */
  --primary: #4361ee;
  --primary-light: rgba(67, 97, 238, 0.1);
  --primary-dark: #3a0ca3;
  
  /* Secondary Colors */
  --secondary: #6c757d;
  --secondary-light: rgba(108, 117, 125, 0.1);
  --secondary-dark: #495057;
  
  /* Success Colors */
  --success: #28a745;
  --success-light: rgba(40, 167, 69, 0.1);
  
  /* Info Colors */
  --info: #4cc9f0;
  --info-light: rgba(76, 201, 240, 0.1);
  
  /* Warning Colors */
  --warning: #ffc107;
  --warning-light: rgba(255, 193, 7, 0.1);
  
  /* Danger Colors */
  --danger: #dc3545;
  --danger-light: rgba(220, 53, 69, 0.1);
  
  /* Neutral Colors */
  --light: #f8f9fa;
  --dark: #343a40;
  --white: #ffffff;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;

  /* Background RGB Values for rgba() usage */
  --bg-primary-rgb: 255, 255, 255;
  --bg-secondary-rgb: 248, 249, 250;
  --bg-tertiary-rgb: 233, 236, 239;
  
  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #000000;
  
  /* Border Colors */
  --border-color: #dee2e6;

  /* RGB Versions for Opacity */
  --primary-rgb: 67, 97, 238;
  --secondary-rgb: 108, 117, 125;
  --success-rgb: 40, 167, 69;
  --info-rgb: 76, 201, 240;
  --warning-rgb: 255, 193, 7;
  --danger-rgb: 220, 53, 69;
}

/* Dark Mode Variables - Applied when data-theme="dark" is set */
[data-theme="dark"] {
  /* Background Colors */
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #2d2d2d;

  /* Background RGB Values for rgba() usage */
  --bg-primary-rgb: 18, 18, 18;
  --bg-secondary-rgb: 30, 30, 30;
  --bg-tertiary-rgb: 45, 45, 45;
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  
  /* Border Colors */
  --border-color: #3d3d3d;

  /* RGB Versions for Opacity (Dark Mode) */
  --primary-rgb: 109, 142, 255; /* Corresponds to --primary: #6d8eff; */
  --secondary-rgb: 160, 160, 160; /* Corresponds to --secondary: #a0a0a0; */
  --success-rgb: 76, 175, 80; /* Corresponds to --success: #4caf50; */
  --info-rgb: 100, 181, 246; /* Corresponds to --info: #64b5f6; */
  --warning-rgb: 255, 183, 77; /* Corresponds to --warning: #ffb74d; */
  --danger-rgb: 244, 67, 54; /* Corresponds to --danger: #f44336; */
  
  /* Override Primary Colors for better contrast */
  --primary: #6d8eff;
  --primary-light: rgba(109, 142, 255, 0.2);
  --primary-dark: #5a75d9;
  
  /* Override Other Colors */
  --secondary: #a0a0a0;
  --secondary-light: rgba(160, 160, 160, 0.2);
  --secondary-dark: #b8b8b8;
  
  --success: #4caf50;
  --success-light: rgba(76, 175, 80, 0.2);
  
  --info: #64b5f6;
  --info-light: rgba(100, 181, 246, 0.2);
  
  --warning: #ffb74d;
  --warning-light: rgba(255, 183, 77, 0.2);
  
  --danger: #f44336;
  --danger-light: rgba(244, 67, 54, 0.2);
  
  --light: #2d2d2d;
  --dark: #f0f0f0;
  --white: #121212;
}

/* Dark Mode Specific Styles */
[data-theme="dark"] {
  /* Force text color for inputs and selects */
  input, select, textarea, .form-control, .modern-form-control {
    color: var(--text-primary) !important;
    background-color: var(--bg-tertiary) !important;
  }

  /* Ensure labels are visible */
  label, .form-label, .modern-form-label {
    color: var(--text-primary) !important;
  }

  /* Force placeholder visibility in dark mode */
  input::placeholder,
  select::placeholder,
  textarea::placeholder,
  .form-control::placeholder,
  .modern-form-control::placeholder {
    color: #e9ecef !important;
    opacity: 0.8 !important;
  }

  /* Dark Mode for Utility Backgrounds */
  .bg-primary {
    background-color: var(--primary) !important;
    color: var(--btn-primary-text) !important; /* Ensure text contrast */
  }
  .bg-success {
    background-color: var(--success) !important;
    color: var(--white) !important; /* Assuming white text on success */
  }
  .bg-info {
    background-color: var(--info) !important;
    color: var(--bg-primary) !important; /* Dark text on light info color */
  }
  .bg-warning {
    background-color: var(--warning) !important;
    color: var(--bg-primary) !important; /* Dark text on light warning color */
  }
  .bg-danger {
    background-color: var(--danger) !important;
    color: var(--white) !important; /* Assuming white text on danger */
  }

  /* Dark Mode for Alerts */
  .alert { /* Genel alert stilini de hedefleyelim */
    border-width: 1px;
    border-style: solid;
  }
  .alert-info {
    background-color: var(--info-light) !important;
    color: var(--info) !important;
    border-color: rgba(var(--info-rgb), 0.3) !important; /* Daha yumuşak kenarlık */
  }
  .alert-success {
    background-color: var(--success-light) !important;
    color: var(--success) !important;
    border-color: rgba(var(--success-rgb), 0.3) !important;
  }
  .alert-warning {
    background-color: var(--warning-light) !important;
    color: var(--warning) !important;
    border-color: rgba(var(--warning-rgb), 0.3) !important;
  }
  .alert-danger {
    background-color: var(--danger-light) !important;
    color: var(--danger) !important;
    border-color: rgba(var(--danger-rgb), 0.3) !important;
  }
  .alert-light {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  /* Dark Mode for Standard Tables */
   .table {
     color: var(--text-primary); /* Tablo metin rengi */
     border-color: var(--border-color); /* Tablo kenarlık rengi */
   }
   .table th, .table td {
      border-color: var(--border-color) !important; /* Hücre kenarlık rengi */
      /* td arka planını ve metin rengini zorla */
      background-color: transparent !important;
      color: var(--text-primary) !important;
   }
   .table tbody tr {
      background-color: transparent !important; /* Satır arka planını zorla */
      color: var(--text-primary) !important; /* Satır metin rengini zorla */
   }
   .table tbody tr:hover {
      background-color: var(--bg-tertiary); /* Hover arka planı */
      color: var(--text-primary);
   }
   /* .table-striped için stiller zaten !important içeriyor, tekrar eklemeye gerek yok */
   /* Ancak, temel tr hover stilini de !important ile güçlendirelim */
   .table tbody tr:hover {
      background-color: var(--bg-tertiary) !important; /* Hover arka planı */
      color: var(--text-primary) !important;
   }
   /* Striped stilleri olduğu gibi kalabilir, zaten !important kullanıyorlar */
   .table-striped tbody tr:nth-of-type(odd) {
      background-color: var(--bg-secondary) !important;
      color: var(--text-primary) !important;
   }
   .table-striped tbody tr:nth-of-type(even) {
      background-color: var(--bg-primary) !important;
      color: var(--text-primary) !important;
   }
   .table-striped tbody tr:hover {
      background-color: var(--bg-tertiary) !important;
      color: var(--text-primary) !important;
   }

   .table-bordered {
      border: 1px solid var(--border-color);
   }


  /* Ensure text-white is actually white in dark mode if needed */
  .text-white {
     color: #ffffff !important; /* Force white text */
  }
}

/* Modern Card */
.modern-card {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  border: none;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.modern-card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-card-body {
  padding: var(--spacing-lg);
}

.modern-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: var(--spacing-md) var(--spacing-md);
  border-bottom: 2px solid rgba(0, 0, 0, 0.05);
}

.modern-table td {
  padding: var(--spacing-md) var(--spacing-md);
  vertical-align: middle;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-table tbody tr {
  transition: background-color var(--transition-speed) var(--transition-timing);
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

/* Modern Buttons */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.modern-btn-primary {
  background-color: var(--primary);
  color: var(--btn-primary-text); /* --white yerine --btn-primary-text kullanıldı */
}

.modern-btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-btn-secondary {
  background-color: var(--secondary);
  color: var(--white);
}

.modern-btn-secondary:hover {
  background-color: var(--secondary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-btn-success {
  background-color: var(--success);
  color: var(--white);
}

.modern-btn-success:hover {
  background-color: #218838;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-btn-danger {
  background-color: var(--danger);
  color: var(--white);
}

.modern-btn-danger:hover {
  background-color: #c82333;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-btn-outline-primary {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.modern-btn-outline-primary:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.modern-btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

.modern-btn-icon {
  margin-right: 0.5rem;
}

/* Modern Form Controls */
.modern-form-group {
  margin-bottom: var(--spacing-md);
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.modern-form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--dark);
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
}

.modern-form-control:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

/* Modern Badges */
.modern-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--border-radius-pill);
}

.modern-badge-primary {
  background-color: var(--primary-light);
  color: var(--primary);
}

.modern-badge-success {
  background-color: var(--success-light);
  color: var(--success);
}

.modern-badge-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.modern-badge-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.modern-badge-info {
  background-color: var(--info-light);
  color: var(--info);
}

/* Modern Avatars */
.modern-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 600;
  font-size: 1rem;
}

.modern-avatar-sm {
  width: 30px;
  height: 30px;
  font-size: 0.75rem;
}

.modern-avatar-lg {
  width: 60px;
  height: 60px;
  font-size: 1.5rem;
}

/* Modern Stats Card */
.modern-stats-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  transition: transform var(--transition-speed) var(--transition-timing);
}

.modern-stats-card:hover {
  transform: translateY(-5px);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: var(--spacing-md);
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.modern-stats-label {
  margin-bottom: 0;
  opacity: 0.8;
  font-weight: 500;
}

/* Modern Pagination */
.modern-pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: var(--border-radius-md);
}

.modern-page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--primary);
  background-color: var(--white);
  border: 1px solid #dee2e6;
  transition: all var(--transition-speed) var(--transition-timing);
}

.modern-page-link:hover {
  z-index: 2;
  color: var(--primary-dark);
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.modern-page-item.active .modern-page-link {
  z-index: 3;
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
}

.modern-page-item.disabled .modern-page-link {
  color: var(--secondary);
  pointer-events: none;
  cursor: auto;
  background-color: var(--white);
  border-color: #dee2e6;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes zoomIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.fade-in {
  animation: fadeIn 0.5s var(--transition-timing);
}

.slide-in-left {
  animation: slideInLeft 0.5s var(--transition-timing);
}

.slide-in-right {
  animation: slideInRight 0.5s var(--transition-timing);
}

.zoom-in {
  animation: zoomIn 0.5s var(--transition-timing);
}

/* Modern Card - Base Styles (Light Mode) */
.modern-card {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
  overflow: hidden;
  color: var(--text-primary);
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.modern-card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-secondary);
}

.modern-card-body {
  padding: var(--spacing-lg);
}

.modern-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* Modern Table - Base Styles (Light Mode) */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  color: var(--text-primary);
}

.modern-table th {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: var(--spacing-md) var(--spacing-md);
  border-bottom: 2px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.modern-table td {
  padding: var(--spacing-md) var(--spacing-md);
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: background-color var(--transition-speed) var(--transition-timing);
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

/* Form Controls - Base Styles (Light Mode) */
.modern-form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
}

.modern-form-control:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Input Group - Base Styles (Light Mode) */
.input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

/* Section Backgrounds - Base Styles (Light Mode) */
.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

/* Modal - Base Styles (Light Mode) */
.modern-modal-overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

.modern-modal-container {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.modern-modal-header, .modern-modal-footer {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
}

/* Dark Mode Specific Overrides - Extended */
[data-theme="dark"] {
  body, html {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }
  
  /* Alert Styles for Dark Mode */
  .alert-light {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
  }

  .alert-info {
    background-color: rgba(13, 202, 240, 0.1) !important;
    color: #0dcaf0 !important;
    border-color: rgba(13, 202, 240, 0.3) !important;
  }

  .alert-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    color: #ffc107 !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
  }

  .alert-success {
    background-color: rgba(25, 135, 84, 0.1) !important;
    color: #198754 !important;
    border-color: rgba(25, 135, 84, 0.3) !important;
  }

  /* Card Styles */
  .modern-card {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }
  
  .modern-card-header {
    border-bottom-color: var(--border-color);
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .modern-card-footer {
    border-top-color: var(--border-color);
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  /* Table Styles */
  .modern-table th {
    border-bottom-color: var(--border-color);
    color: var(--text-primary);
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .modern-table td {
    border-bottom-color: var(--border-color);
    color: var(--text-primary);
  }
  
  .modern-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  /* Form Controls */
  .modern-form-control {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }
  
  .modern-form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem var(--primary-light);
  }
  
  .modern-form-control::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
  }
  
  /* Input Group */
  .input-group-text {
    background-color: var(--primary-light);
    color: var(--primary);
    border-color: var(--border-color);
  }
  
  /* Pagination */
  .modern-page-link {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }
  
  .modern-page-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--primary);
  }
  
  .modern-page-item.active .modern-page-link {
    background-color: var(--primary);
    border-color: var(--primary);
    color: #ffffff;
  }
  
  .modern-page-item.disabled .modern-page-link {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    border-color: var(--border-color);
  }
  
  /* Badges */
  .modern-badge {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
  }
  
  .modern-badge-primary {
    background-color: var(--primary-light);
    color: var(--primary);
  }
  
  .modern-badge-success {
    background-color: var(--success-light);
    color: var(--success);
  }
  
  .modern-badge-warning {
    background-color: var(--warning-light);
    color: var(--warning);
  }
  
  .modern-badge-danger {
    background-color: var(--danger-light);
    color: var(--danger);
  }
  
  .modern-badge-info {
    background-color: var(--info-light);
    color: var(--info);
  }

  /* Bootstrap Badges for Dark Mode */
  .badge.bg-primary {
    background-color: #0d6efd !important;
    color: #ffffff !important;
  }

  .badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000000 !important;
  }

  .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
  }

  .badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
  }

  .badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
  }

  /* Buttons */
  .modern-btn-primary {
    background-color: var(--primary);
    color: #ffffff;
  }
  
  .modern-btn-secondary {
    background-color: var(--secondary);
    color: #ffffff;
  }
  
  .modern-btn-outline-primary {
    border-color: var(--primary);
    color: var(--primary);
  }
  
  .modern-btn-outline-primary:hover {
    background-color: var(--primary);
    color: #ffffff;
  }
  
  /* Section Backgrounds */
  .form-section {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .form-section:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
  
  /* Progress Bar */
  .progress {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  /* Modal */
  .modern-modal-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }
  
  .modern-modal-container {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
  }
  
  .modern-modal-header, .modern-modal-footer {
    border-color: var(--border-color);
    background-color: rgba(255, 255, 255, 0.05);
  }

  /* Global Pagination Dark Mode Styles */
  .pagination .page-link,
  .modern-pagination .modern-page-link {
    background-color: var(--bg-secondary) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-secondary) !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
  }

  .pagination .page-link:hover,
  .modern-pagination .modern-page-link:hover {
    background-color: var(--primary-light) !important;
    color: var(--primary) !important;
    border-color: var(--primary) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  }

  .pagination .page-item.active .page-link,
  .modern-pagination .modern-page-item.active .modern-page-link {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border-color: var(--primary) !important;
    box-shadow: 0 4px 10px rgba(67, 97, 238, 0.4) !important;
  }

  .pagination .page-item.disabled .page-link,
  .modern-pagination .modern-page-item.disabled .modern-page-link {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
    opacity: 0.4 !important;
  }
}
