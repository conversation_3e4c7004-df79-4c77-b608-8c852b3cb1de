import { Component, OnInit } from '@angular/core';
import { UserService } from '../../services/user-service.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { FileUploadService } from '../../services/file-upload.service';
import { RateLimitService } from '../../services/rate-limit.service';
import { ProfileImageService } from '../../services/profile-image.service';

import { UserLicenseService } from '../../services/user-license.service';
import { finalize } from 'rxjs';
import { DialogService } from '../../services/dialog.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css',
  standalone: false,
})
export class ProfileComponent implements OnInit {
  userProfile: any;
  isLoading: boolean = true;
  changePasswordForm: FormGroup;
  isPasswordFormVisible: boolean = false;
  isPasswordChanging: boolean = false;
  currentPasswordVisible: boolean = false;
  newPasswordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;
  isAdmin: boolean = false;
  isMember: boolean = false;

  // Profil fotoğrafı özellikleri
  profileImageUrl: string | null = null;
  isImageUploading: boolean = false;
  selectedFile: File | null = null;
  imagePreview: string | null = null;
  remainingUploads: number = 3;
  maxUploadsPerDay: number = 3;

  // Lisans bilgileri
  licenseInfo: any = null;
  isLicenseLoading: boolean = false;

  constructor(
    private userService: UserService,
    private authService: AuthService,
    private toastrService: ToastrService,
    private router: Router,
    private formBuilder: FormBuilder,
    private fileUploadService: FileUploadService,
    private rateLimitService: RateLimitService,
    private userLicenseService: UserLicenseService,
    private dialogService: DialogService,
    private profileImageService: ProfileImageService
  ) {}

  ngOnInit(): void {
    this.createChangePasswordForm();
    this.loadUserProfile();
    this.checkUserRole();
    this.loadRemainingUploads();
  }

  checkUserRole(): void {
    const currentUser = this.authService.currentUserValue;
    if (currentUser && currentUser.role) {
      if (Array.isArray(currentUser.role)) {
        this.isAdmin = currentUser.role.includes('admin');
        this.isMember = currentUser.role.includes('member');
      } else {
        this.isAdmin = currentUser.role === 'admin';
        this.isMember = currentUser.role === 'member';
      }
    }
  }

  loadUserProfile() {
    this.isLoading = true;
    this.userService.getUserProfile().subscribe({
      next: (data) => {
        this.userProfile = data;
        this.licenseInfo = data.license;
        this.loadProfileImage();
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error(error.message || 'Profil bilgileri yüklenirken bir hata oluştu');
        this.isLoading = false;
      }
    });
  }

  loadProfileImage() {
    if (this.userProfile?.user?.profileImagePath) {
      const currentUser = this.authService.currentUserValue;
      if (currentUser?.nameidentifier) {
        const userId = parseInt(currentUser.nameidentifier);
        this.profileImageUrl = this.fileUploadService.getProfileImageUrl(userId);
      }
    } else {
      // Profil fotoğrafı yoksa URL'yi null yap
      this.profileImageUrl = null;
    }
  }

  loadProfileImageWithCacheBuster() {
    const currentUser = this.authService.currentUserValue;
    if (currentUser?.nameidentifier) {
      const userId = parseInt(currentUser.nameidentifier);
      // Cache bypass için timestamp ekle
      const timestamp = new Date().getTime();
      this.profileImageUrl = `${this.fileUploadService.getProfileImageUrl(userId)}?t=${timestamp}`;
    }
  }

  loadRemainingUploads() {
    this.rateLimitService.getRemainingProfileImageUploads().subscribe({
      next: (response) => {
        if (response.success) {
          this.remainingUploads = response.remainingUploads;
          this.maxUploadsPerDay = response.maxUploadsPerDay;
        }
      },
      error: (error) => {
        // Hata durumunda varsayılan değerleri kullan
        console.warn('Kalan upload sayısı alınamadı:', error);
      }
    });
  }



  createChangePasswordForm() {
    this.changePasswordForm = this.formBuilder.group({
      currentPassword: ['', Validators.required],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required]
    }, {
      validator: this.passwordMatchValidator
    });
  }

  // Şifre eşleşme kontrolü
  passwordMatchValidator(g: FormGroup) {
    const newPassword = g.get('newPassword')?.value || '';
    const confirmPassword = g.get('confirmPassword')?.value || '';
    return newPassword === confirmPassword ? null : { 'mismatch': true };
  }

  togglePasswordForm() {
    this.isPasswordFormVisible = !this.isPasswordFormVisible;
    if (!this.isPasswordFormVisible) {
      this.changePasswordForm.reset();
    }
  }

  changePassword() {
    if (this.changePasswordForm.invalid) {
      this.toastrService.error('Lütfen tüm alanları doğru şekilde doldurun');
      return;
    }

    if (this.changePasswordForm.hasError('mismatch')) {
      this.toastrService.error('Yeni şifre ve şifre tekrarı eşleşmiyor');
      return;
    }

    const currentPassword = this.changePasswordForm.get('currentPassword')?.value || '';
    const newPassword = this.changePasswordForm.get('newPassword')?.value || '';

    this.isPasswordChanging = true;
    this.authService.changePassword(currentPassword, newPassword)
      .pipe(finalize(() => this.isPasswordChanging = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success(response.message || 'Şifreniz başarıyla değiştirildi');
            this.changePasswordForm.reset();
            this.isPasswordFormVisible = false;
          } else {
            this.toastrService.error(response.message || 'Şifre değiştirme işlemi başarısız');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Şifre değiştirme işlemi sırasında bir hata oluştu');
        }
      });
  }

  // Şifre görünürlüğünü değiştirir
  toggleCurrentPasswordVisibility(): void {
    this.currentPasswordVisible = !this.currentPasswordVisible;
  }

  toggleNewPasswordVisibility(): void {
    this.newPasswordVisible = !this.newPasswordVisible;
  }

  toggleConfirmPasswordVisibility(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }

  // Profil fotoğrafı yönetimi metodları
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const validation = this.fileUploadService.validateImageFile(file);
      if (!validation.isValid) {
        this.toastrService.error(validation.message!);
        this.clearFileInput();
        return;
      }

      this.selectedFile = file;

      // Önizleme oluştur
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file);

      // Otomatik yükleme
      this.uploadProfileImageAuto();
    }
  }



  deleteProfileImage(): void {
    this.dialogService.confirmDelete('Profil fotoğrafınızı').subscribe(confirmed => {
      if (confirmed) {
        this.fileUploadService.deleteProfileImage().subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success(response.message || 'Profil fotoğrafı başarıyla silindi');
              this.profileImageUrl = null;
              // ProfileImageService'i güncelle (sidebar için)
              this.profileImageService.refreshAfterDelete();
            } else {
              this.toastrService.error(response.message || 'Profil fotoğrafı silinirken bir hata oluştu');
            }
          },
          error: (error) => {
            // Hata mesajını backend'den al
            if (error.status === 400 && error.error && error.error.message) {
              this.toastrService.error(error.error.message);
            } else {
              this.toastrService.error('Profil fotoğrafı silinirken bir hata oluştu');
            }
          }
        });
      }
    });
  }



  triggerFileInput(): void {
    // Günlük yükleme limitini kontrol et
    if (this.remainingUploads <= 0) {
      this.toastrService.warning(
        `Günlük fotoğraf yükleme limitini aştınız. Günde en fazla ${this.maxUploadsPerDay} fotoğraf yükleyebilirsiniz.`,
        'Yükleme Limiti',
        {
          positionClass: 'toast-bottom-right',
          timeOut: 5000
        }
      );
      return;
    }

    const fileInput = document.getElementById('profileImageInput') as HTMLInputElement;
    fileInput?.click();
  }

  uploadProfileImageAuto(): void {
    if (!this.selectedFile) {
      return;
    }

    this.isImageUploading = true;

    // Compressed upload kullan
    this.fileUploadService.uploadCompressedProfileImage(this.selectedFile)
      .then(uploadObservable => {
        uploadObservable
          .pipe(finalize(() => this.isImageUploading = false))
          .subscribe({
            next: (response) => {
              if (response.success) {
                this.toastrService.success(response.message || 'Profil fotoğrafı başarıyla yüklendi');
                this.clearFileInput(); // Seçimi temizle
                // Profil fotoğrafını cache bypass ile yeniden yükle
                this.loadProfileImageWithCacheBuster();
                // ProfileImageService'i güncelle (sidebar için)
                this.profileImageService.refreshAfterUpload();
                // Kalan upload sayısını güncelle
                this.loadRemainingUploads();
              } else {
                this.toastrService.error(response.message || 'Profil fotoğrafı yüklenirken bir hata oluştu');
                this.clearFileInput();
              }
            },
            error: (error) => {
              // Rate limiting hatası için özel mesaj
              if (error.status === 400 && error.error && error.error.message) {
                this.toastrService.error(error.error.message);
              } else if (error.status === 429) {
                this.toastrService.error('Çok fazla istek gönderdiniz. Lütfen daha sonra tekrar deneyin.');
              } else {
                this.toastrService.error(error.message || 'Profil fotoğrafı yüklenirken bir hata oluştu');
              }
              this.clearFileInput();
            }
          });
      })
      .catch(error => {
        this.isImageUploading = false;
        this.toastrService.error(error.message || 'Resim sıkıştırma işlemi başarısız oldu');
        this.clearFileInput();
      });

  }

  clearFileInput(): void {
    this.selectedFile = null;
    this.imagePreview = null;
    // File input'u temizle
    const fileInput = document.getElementById('profileImageInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  // Lisans durumu için renk sınıfı döndürür
  getLicenseStatusClass(): string {
    if (!this.licenseInfo || !this.licenseInfo.remainingDays) {
      return 'text-danger';
    }

    const remainingDays = this.licenseInfo.remainingDays;
    if (remainingDays > 30) {
      return 'text-success';
    } else if (remainingDays > 7) {
      return 'text-warning';
    } else {
      return 'text-danger';
    }
  }

  // Lisans durumu için ikon döndürür
  getLicenseStatusIcon(): string {
    if (!this.licenseInfo || !this.licenseInfo.remainingDays) {
      return 'fas fa-times-circle';
    }

    const remainingDays = this.licenseInfo.remainingDays;
    if (remainingDays > 30) {
      return 'fas fa-check-circle';
    } else if (remainingDays > 7) {
      return 'fas fa-exclamation-triangle';
    } else {
      return 'fas fa-times-circle';
    }
  }

  // Lisans durumu için mesaj döndürür
  getLicenseStatusMessage(): string {
    if (!this.licenseInfo || !this.licenseInfo.remainingDays) {
      return 'Lisans süresi dolmuş';
    }

    const remainingDays = this.licenseInfo.remainingDays;
    if (remainingDays > 30) {
      return `Lisansınız ${remainingDays} gün daha geçerli`;
    } else if (remainingDays > 7) {
      return `Lisansınız ${remainingDays} gün sonra sona erecek`;
    } else if (remainingDays > 0) {
      return `Lisansınız ${remainingDays} gün sonra sona erecek - Acil yenileme gerekli!`;
    } else {
      return 'Lisans süresi dolmuş';
    }
  }
}